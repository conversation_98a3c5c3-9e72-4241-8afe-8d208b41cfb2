package models

import (
	"time"

	"go-admin/common/models"
)

type TFansPage struct {
	models.Model

	PlanetId       int64     `json:"plantId" gorm:"type:bigint;comment:plant"`
	StartTimeShow  time.Time `json:"startTimeShow" gorm:"type:datetime(3);comment:开始时间-显示"`
	StartTimeValue int64     `json:"startTimeValue" gorm:"type:bigint;comment:开始时间-值"`
	EndTimeShow    time.Time `json:"endTimeShow" gorm:"type:datetime(3);comment:结束时间-显示"`
	EndTimeValue   int64     `json:"endTimeValue" gorm:"type:bigint;comment:结束时间-值-值"`
	UrlLong        string    `json:"urlLong" gorm:"type:varchar(255);comment:长链接"`
	UrlShort       string    `json:"urlShort" gorm:"type:varchar(255);comment:短链接"`
	Cdkey          string    `json:"cdkey" gorm:"type:varchar(20);comment:奖励码"`
	RewardJson     string    `json:"rewardJson" gorm:"type:text;comment:奖励json"`
	RpcJson        string    `json:"rpcJson" gorm:"type:text;comment:RPC请求Json"`
	Remark         string    `json:"remark" gorm:"type:varchar(255);comment:备注"`
	models.ModelTime
	models.ControlBy
}

func (TFansPage) TableName() string {
	return "t_fans_page"
}

func (e *TFansPage) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFansPage) GetId() interface{} {
	return e.Id
}
