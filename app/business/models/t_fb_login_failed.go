package models

import (
	"time"

	"go-admin/common/models"
)

type TFbLoginFailed struct {
	models.Model

	Platform       int64     `json:"platform" gorm:"type:int;comment:Platform"`
	NowDate        time.Time `json:"nowDate" gorm:"type:datetime(3);comment:NowDate"`
	TimeStampValue time.Time `json:"timeStampValue" gorm:"type:datetime(3);comment:TimeStampValue"`
	AccType        string    `json:"accType" gorm:"type:int;comment:AccType"`
	Channel        string    `json:"channel" gorm:"type:int;comment:Channel"`
	AppVersion     string    `json:"appVersion" gorm:"type:varchar(20);comment:AppVersion"`
	AppLanguage    string    `json:"appLanguage" gorm:"type:varchar(20);comment:AppLanguage"`
	Country        string    `json:"country" gorm:"type:varchar(20);comment:Country"`
	DeviceID       string    `json:"deviceID" gorm:"type:varchar(100);comment:DeviceID"`
	Reason         string    `json:"reason" gorm:"type:varchar(255);comment:Reason"`
	//models.ModelTime
	//models.ControlBy
}

type TFbLoginFailedGroup struct {
	Reason string `json:"Reason"`
	Count  int64  `json:"Count"`
}

func (TFbLoginFailed) TableName() string {
	return "t_fb_login_failed_ods"
}

//func (e *TFbLoginFailed) Generate() models.ActiveRecord {
//	o := *e
//	return &o
//}

func (e *TFbLoginFailed) GetId() interface{} {
	return e.Id
}
