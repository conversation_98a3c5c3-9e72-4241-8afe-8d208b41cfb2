package models

import (
	"time"

	"go-admin/common/models"
)

type TMockLogin struct {
	models.Model

	PlanetId    int64     `json:"planetId" gorm:"type:bigint;comment:planet"`
	Uid         string    `json:"uid" gorm:"type:varchar(20);comment:用户ID"`
	YourUid     string    `json:"yourUid" gorm:"type:varchar(20);comment:your_uid"`
	ReplacedUid string    `json:"replacedUid" gorm:"type:varchar(20);comment:replaced_uid"`
	TimeBegin   time.Time `json:"timeBegin" gorm:"type:timestamp;comment:开始时间"`
	Remark      string    `json:"remark" gorm:"type:varchar(255);comment:备注"`
	DeleteFlg   int32     `json:"deleteFlg" gorm:"type:tinyint(1);comment:逻辑删除标记 1：删除"`
	models.ModelTime
	models.ControlBy
}

func (TMockLogin) TableName() string {
	return "t_mock_login"
}

func (e *TMockLogin) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TMockLogin) GetId() interface{} {
	return e.Id
}
