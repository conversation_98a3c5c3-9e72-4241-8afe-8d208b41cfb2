package models

import (
	"time"

	"go-admin/common/models"
)

type TPushLog struct {
	models.Model

	PlanetId       string    `json:"planetId" gorm:"type:bigint;comment:planet"`
	Uid            string    `json:"uid" gorm:"type:varchar(20);comment:用户ID"`
	Lang           string    `json:"lang" gorm:"type:int;comment:版本语言"`
	PushType       string    `json:"pushType" gorm:"type:int;comment:PushType"`
	PushId         string    `json:"pushId" gorm:"type:int;comment:PushId"`
	PushTotalNum   string    `json:"pushTotalNum" gorm:"type:int;comment:PushTotalNum"`
	PushOkNum      string    `json:"pushOkNum" gorm:"type:int;comment:PushOkNum"`
	PushFailNum    string    `json:"pushFailNum" gorm:"type:int;comment:PushFailNum"`
	PushErrorMsg   string    `json:"pushErrorMsg" gorm:"type:int;comment:PushErrorMsg"`
	TimeStampValue time.Time `json:"timeStampValue" gorm:"type:datetime(3);comment:PushErrorMsg"`
	//models.ModelTime
	//models.ControlBy
}

//type TPushLogStatics struct {
//	AllOkNum int32 `json:"pushOkNum"`
//	AllCount int32 `json:"pushFailNum"`
//}

type TPushAllRet struct {
	PushOkNum  int32 `json:"PushOkNum"`
	PushAllNum int32 `json:"PushAllNum"`
}

func (TPushLog) TableName() string {
	return "t_push_log_ods"
}

//func (e *TPushLog) Generate() models.ActiveRecord {
//	o := *e
//	return &o
//}

func (e *TPushLog) GetId() interface{} {
	return e.Id
}
