package apis

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"go-admin/common/rpc"
	"go-admin/config"
	"go-admin/core/sdk/pkg/cdkey"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/business/models"
	"go-admin/app/business/service"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
)

type TFansPage struct {
	api.Api
}

// GetPage 获取TFansPage列表
// @Summary 获取TFansPage列表
// @Description 获取TFansPage列表
// @Tags TFansPage
// @Param startTimeShow query time.Time false "开始时间-显示"
// @Param endTimeShow query time.Time false "结束时间-显示"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TFansPage}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fans-page [get]
// @Security Bearer
func (e TFansPage) GetPage(c *gin.Context) {
	req := dto.TFansPageGetPageReq{}
	s := service.TFansPage{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFansPage, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFansPage 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TFansPage
// @Summary 获取TFansPage
// @Description 获取TFansPage
// @Tags TFansPage
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TFansPage} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fans-page/{id} [get]
// @Security Bearer
func (e TFansPage) Get(c *gin.Context) {
	req := dto.TFansPageGetReq{}
	s := service.TFansPage{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TFansPage

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFansPage失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TFansPage
// @Summary 创建TFansPage
// @Description 创建TFansPage
// @Tags TFansPage
// @Accept application/json
// @Product application/json
// @Param data body dto.TFansPageInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-fans-page [post]
// @Security Bearer
func (e TFansPage) Insert(c *gin.Context) {
	req := dto.TFansPageInsertReq{}
	s := service.TFansPage{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	formatStartTime, _ := time.Parse("2006-01-02 15:04:05", req.StartTimeValue)
	formatEndTime, _ := time.Parse("2006-01-02 15:04:05", req.EndTimeValue)
	req.StartTimeShow = formatStartTime
	req.EndTimeShow = formatEndTime

	//生成8位CDKey
	cdKey := cdkey.RandStringBytes(8)

	// 数据库查询cdKey是否重复
	isExist, errLoad := s.Load(cdKey)
	if errLoad != nil {
		e.Error(500, errLoad, fmt.Sprintf("获取TFansPage失败，\r\n失败信息 %s", err.Error()))
		return
	}

	if isExist {
		cdKey = cdkey.RandStringBytes(8)
	}
	req.Cdkey = cdKey

	// 去系统配置获取粉丝页
	//长链接
	//urlLong := "http://mt-dev.playsparkle.com/web/share?code=" + cdKey
	urlLong := req.UrlLong + "?code=" + cdKey
	req.UrlLong = urlLong

	//TODO 短链接 暂时先不管

	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	// RPC到游戏服
	_, object, err := rpc.RpcGm2Game(rpc.Req{
		GmUid:          "0",
		GmCmd:          "4000071",
		Code:           cdKey,
		StartTimeStamp: req.StartTimeShow.Unix(),
		EndTimeStamp:   req.EndTimeShow.Unix(),
		RewardJson:     req.RewardJson,
	}, c)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("通知Game服务失败，\r\n信息 %s", err.Error()))
		return
	}
	req.RpcJson = object
	shortUrl, err := getShortUrl(req.UrlLong)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("生成短连接失败，\r\n信息 %s", err.Error()))
	}
	req.UrlShort = shortUrl
	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TFansPage  失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "创建成功")
}

// getBitLyToken 获取短连接
func getBitLyToken() (string, error) {
	//config.ExtConfig = config.Extend{
	//	BitLy: config.BitLy{
	//		UserName:     "<EMAIL>",
	//		PassWord:     "liuyalong199027",
	//		ClientId:     "bd41b1197c406ff23beca5d019be342df2b2239f",
	//		ClientSecret: "4b5ec1ecbb6f553fa49a9435e13b449827ca0cbb",
	//	},
	//}

	curlParams := url.Values{}
	curlParams.Set("client_id", config.ExtConfig.BitLy.ClientId)
	curlParams.Set("client_secret", config.ExtConfig.BitLy.ClientSecret)

	requestUrl := "https://api-ssl.bitly.com/oauth/access_token"
	req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer([]byte(curlParams.Encode())))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(config.ExtConfig.BitLy.UserName+":"+config.ExtConfig.BitLy.PassWord)))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	if resp.StatusCode != http.StatusOK {
		return "", errors.New(string(b))
	}
	return string(b), nil
}

func getShortUrl(longUrl string) (string, error) {
	token, err := getBitLyToken()
	if err != nil {
		return "", err
	}

	params := make(map[string]string)
	params["long_url"] = longUrl
	params["domain"] = "bit.ly"

	paramsBytes, err := json.Marshal(params)
	if err != nil {
		return "", err
	}

	requestUrl := "https://api-ssl.bitly.com/v4/shorten"
	req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer(paramsBytes))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	b, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return "", errors.New(string(b))
	}
	var result *bitLyData
	if err = json.Unmarshal(b, &result); err != nil {
		return "", err
	}
	return result.Link, nil
}

type bitLyData struct {
	Id   string `json:"id"`
	Link string `json:"link"`
}

// Update 修改TFansPage
// @Summary 修改TFansPage
// @Description 修改TFansPage
// @Tags TFansPage
// @Accept application/json
// @Product application/json
// @Param data body dto.TFansPageUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-fans-page/{id} [put]
// @Security Bearer
func (e TFansPage) Update(c *gin.Context) {

}

// Delete 删除TFansPage
// @Summary 删除TFansPage
// @Description 删除TFansPage
// @Tags TFansPage
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-fans-page [delete]
// @Security Bearer
func (e TFansPage) Delete(c *gin.Context) {
	s := service.TFansPage{}
	req := dto.TFansPageDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TFansPage失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
