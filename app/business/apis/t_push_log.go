package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/business/models"
	"go-admin/app/business/service"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
)

type TPushLog struct {
	api.Api
}

// GetPage 获取TPushLog列表
// @Summary 获取TPushLog列表
// @Description 获取TPushLog列表
// @Tags TPushLog
// @Param uid query string false "用户ID"
// @Param lang query string false "版本语言"
// @Param pushType query string false "PushType"
// @Param pushId query string false "PushId"
// @Param timeStampValue query time.Time false "PushErrorMsg"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TPushLog}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-push-log [get]
// @Security Bearer
func (e TPushLog) GetPage(c *gin.Context) {
	req2 := dto.TPushLogGetPageReq2{}
	s := service.TPushLog{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req2).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TPushLog, 0)
	var count int64

	isPushAll := req2.PushAll

	req := dto.TPushLogGetPageReq{}
	req.Lang = req2.Lang
	req.PushType = req2.PushType
	req.PushId = req2.PushId
	//req.TimeStampValue = req2.TimeStampValue
	req.BeginTime = req2.BeginTime
	req.EndTime = req2.EndTime

	req.PageIndex = req2.PageIndex
	req.PageSize = req2.PageSize
	req.TPushLogOrder = req2.TPushLogOrder

	if isPushAll {
		data := models.TPushAllRet{}
		err = s.GetPageAll(&req, p, &data)

		if err != nil {
			e.Error(500, err, fmt.Sprintf("获取TPushLog 失败，\r\n失败信息 %s", err.Error()))
			return
		}
		e.PageOK(data, int(count), req.GetPageIndex(), req.GetPageSize(), "查询汇总成功")

	} else {
		err = s.GetPage(&req, p, &list, &count)
		if err != nil {
			e.Error(500, err, fmt.Sprintf("获取TPushLog 失败，\r\n失败信息 %s", err.Error()))
			return
		}
		e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
	}
}

func (e TPushLog) GetPageAll(c *gin.Context) {
	req := dto.TPushLogGetPageReq{}
	s := service.TPushLog{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TPushLog, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TPushLog 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TPushLog
// @Summary 获取TPushLog
// @Description 获取TPushLog
// @Tags TPushLog
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TPushLog} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-push-log/{id} [get]
// @Security Bearer
func (e TPushLog) Get(c *gin.Context) {
	req := dto.TPushLogGetReq{}
	s := service.TPushLog{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TPushLog

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TPushLog失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TPushLog
// @Summary 创建TPushLog
// @Description 创建TPushLog
// @Tags TPushLog
// @Accept application/json
// @Product application/json
// @Param data body dto.TPushLogInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-push-log [post]
// @Security Bearer
func (e TPushLog) Insert(c *gin.Context) {
	req := dto.TPushLogInsertReq{}
	s := service.TPushLog{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TPushLog  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TPushLog
// @Summary 修改TPushLog
// @Description 修改TPushLog
// @Tags TPushLog
// @Accept application/json
// @Product application/json
// @Param data body dto.TPushLogUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-push-log/{id} [put]
// @Security Bearer
func (e TPushLog) Update(c *gin.Context) {
	req := dto.TPushLogUpdateReq{}
	s := service.TPushLog{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TPushLog 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TPushLog
// @Summary 删除TPushLog
// @Description 删除TPushLog
// @Tags TPushLog
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-push-log [delete]
// @Security Bearer
func (e TPushLog) Delete(c *gin.Context) {
	s := service.TPushLog{}
	req := dto.TPushLogDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TPushLog失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
