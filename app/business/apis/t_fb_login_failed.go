package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/business/models"
	"go-admin/app/business/service"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
)

type TFbLoginFailed struct {
	api.Api
}

// GetPage 获取TFbLoginFailed列表
// @Summary 获取TFbLoginFailed列表
// @Description 获取TFbLoginFailed列表
// @Tags TFbLoginFailed
// @Param platform query int64 false "Platform"
// @Param timeStampValue query time.Time false "TimeStampValue"
// @Param accType query string false "AccType"
// @Param appVersion query string false "AppVersion"
// @Param appLanguage query string false "AppLanguage"
// @Param country query string false "Country"
// @Param reason query string false "Reason"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TFbLoginFailed}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fb-login-failed [get]
// @Security Bearer
func (e TFbLoginFailed) GetPage(c *gin.Context) {
	req := dto.TFbLoginFailedGetPageReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFbLoginFailed, 0)
	var count int64

	err = s.
		GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFbLoginFailed 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

func (e TFbLoginFailed) GetGroupPage(c *gin.Context) {
	req := dto.TFbLoginFailedGetPageReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFbLoginFailedGroup, 0)
	var count int64

	err = s.
		GetPageGroup(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFbLoginFailed 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

func (e TPushLog) GetGroupError(c *gin.Context) {
	req := dto.TFbLoginFailedGetPageReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFbLoginFailed, 0)
	var count int64

	err = s.
		GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFbLoginFailed 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TFbLoginFailed
// @Summary 获取TFbLoginFailed
// @Description 获取TFbLoginFailed
// @Tags TFbLoginFailed
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TFbLoginFailed} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fb-login-failed/{id} [get]
// @Security Bearer
func (e TFbLoginFailed) Get(c *gin.Context) {
	req := dto.TFbLoginFailedGetReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TFbLoginFailed

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TFbLoginFailed失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TFbLoginFailed
// @Summary 创建TFbLoginFailed
// @Description 创建TFbLoginFailed
// @Tags TFbLoginFailed
// @Accept application/json
// @Product application/json
// @Param data body dto.TFbLoginFailedInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-fb-login-failed [post]
// @Security Bearer
func (e TFbLoginFailed) Insert(c *gin.Context) {
	req := dto.TFbLoginFailedInsertReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TFbLoginFailed  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TFbLoginFailed
// @Summary 修改TFbLoginFailed
// @Description 修改TFbLoginFailed
// @Tags TFbLoginFailed
// @Accept application/json
// @Product application/json
// @Param data body dto.TFbLoginFailedUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-fb-login-failed/{id} [put]
// @Security Bearer
func (e TFbLoginFailed) Update(c *gin.Context) {
	req := dto.TFbLoginFailedUpdateReq{}
	s := service.TFbLoginFailed{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TFbLoginFailed 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TFbLoginFailed
// @Summary 删除TFbLoginFailed
// @Description 删除TFbLoginFailed
// @Tags TFbLoginFailed
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-fb-login-failed [delete]
// @Security Bearer
func (e TFbLoginFailed) Delete(c *gin.Context) {
	s := service.TFbLoginFailed{}
	req := dto.TFbLoginFailedDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TFbLoginFailed失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
