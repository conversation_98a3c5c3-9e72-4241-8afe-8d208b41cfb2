package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/common/rpc"
	"go-admin/core/logger"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/business/models"
	"go-admin/app/business/service"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
)

type TMockLogin struct {
	api.Api
}

// GetPage 获取TMockLogin列表
// @Summary 获取TMockLogin列表
// @Description 获取TMockLogin列表
// @Tags TMockLogin
// @Param uid query string false "用户ID"
// @Param yourUid query string false "your_uid"
// @Param replacedUid query string false "replaced_uid"
// @Param timeBegin query time.Time false "开始时间"
// @Param deleteFlg query string false "逻辑删除标记 1：删除"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TMockLogin}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-mock-login [get]
// @Security Bearer
func (e TMockLogin) GetPage(c *gin.Context) {
	req := dto.TMockLoginGetPageReq{}
	s := service.TMockLogin{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TMockLogin, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TMockLogin 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TMockLogin
// @Summary 获取TMockLogin
// @Description 获取TMockLogin
// @Tags TMockLogin
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TMockLogin} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-mock-login/{id} [get]
// @Security Bearer
func (e TMockLogin) Get(c *gin.Context) {
	req := dto.TMockLoginGetReq{}
	s := service.TMockLogin{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TMockLogin

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TMockLogin失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TMockLogin
// @Summary 创建TMockLogin
// @Description 创建TMockLogin
// @Tags TMockLogin
// @Accept application/json
// @Product application/json
// @Param data body dto.TMockLoginInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-mock-login [post]
// @Security Bearer
func (e TMockLogin) Insert(c *gin.Context) {
	req := dto.TMockLoginInsertReq{}
	s := service.TMockLogin{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	// RPC到游戏服
	_, object, err := rpc.RpcGm2Game(rpc.Req{
		GmUid:  req.YourUid,
		GmCmd:  "4000097",
		Action: 1,
		UID:    req.YourUid,
		Param:  req.ReplacedUid,
	}, c)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("通知Game服务失败，\r\n信息 %s", err.Error()))
		return
	}

	e.Logger.Log(logger.InfoLevel, object)

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TMockLogin  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TMockLogin
// @Summary 修改TMockLogin
// @Description 修改TMockLogin
// @Tags TMockLogin
// @Accept application/json
// @Product application/json
// @Param data body dto.TMockLoginUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-mock-login/{id} [put]
// @Security Bearer
func (e TMockLogin) Update(c *gin.Context) {
	req := dto.TMockLoginUpdateReq{}
	s := service.TMockLogin{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TMockLogin 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TMockLogin
// @Summary 删除TMockLogin
// @Description 删除TMockLogin
// @Tags TMockLogin
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-mock-login [delete]
// @Security Bearer
func (e TMockLogin) Delete(c *gin.Context) {
	s := service.TMockLogin{}
	req := dto.TMockLoginDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	reqQuery := dto.TMockLoginGetReq{}
	reqQuery.Id = req.Ids[0]

	var dbRow models.TMockLogin
	err = s.Get(&reqQuery, p, &dbRow)

	// RPC到游戏服
	_, object, err := rpc.RpcGm2Game(rpc.Req{
		GmUid:  dbRow.YourUid,
		GmCmd:  "4000097",
		Action: 2,
		UID:    dbRow.YourUid,
		Param:  dbRow.ReplacedUid,
	}, c)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("通知Game服务失败，\r\n信息 %s", err.Error()))
		return
	}

	e.Logger.Log(logger.InfoLevel, object)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TMockLogin失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
