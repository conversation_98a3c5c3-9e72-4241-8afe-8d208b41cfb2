package dto

import (
	"time"

	"go-admin/app/business/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TFbLoginFailedGetPageReq struct {
	dto.Pagination `search:"-"`
	Platform       int64     `form:"platform"  search:"type:exact;column:Platform;table:t_fb_login_failed_ods" comment:"Platform"`
	TimeStampValue time.Time `form:"timeStampValue"  search:"type:gte;column:TimeStampValue;table:t_fb_login_failed_ods" comment:"TimeStampValue"`
	NowDate        time.Time `form:"nowDate"  search:"type:exact;column:NowDate;table:t_fb_login_failed_ods" comment:"NowDate"`
	AccType        string    `form:"accType"  search:"type:exact;column:AccType;table:t_fb_login_failed_ods" comment:"AccType"`
	AppVersion     string    `form:"appVersion"  search:"type:exact;column:AppVersion;table:t_fb_login_failed_ods" comment:"AppVersion"`
	AppLanguage    string    `form:"appLanguage"  search:"type:exact;column:AppLanguage;table:t_fb_login_failed_ods" comment:"AppLanguage"`
	Country        string    `form:"country"  search:"type:exact;column:Country;table:t_fb_login_failed_ods" comment:"Country"`
	Reason         string    `form:"reason"  search:"type:exact;column:Reason;table:t_fb_login_failed_ods" comment:"Reason"`
	TFbLoginFailedOrder
}

type TFbLoginFailedOrder struct {
	Id             int       `form:"idOrder"  search:"type:order;column:id;table:t_fb_login_failed_ods"`
	Platform       int64     `form:"platformOrder"  search:"type:order;column:Platform;table:t_fb_login_failed_ods"`
	NowDate        time.Time `form:"nowDateOrder"  search:"type:order;column:NowDate;table:t_fb_login_failed_ods"`
	TimeStampValue time.Time `form:"timeStampValueOrder"  search:"type:order;column:TimeStampValue;table:t_fb_login_failed_ods"`
	AccType        string    `form:"accTypeOrder"  search:"type:order;column:AccType;table:t_fb_login_failed_ods"`
	Channel        string    `form:"channelOrder"  search:"type:order;column:Channel;table:t_fb_login_failed_ods"`
	AppVersion     string    `form:"appVersionOrder"  search:"type:order;column:AppVersion;table:t_fb_login_failed_ods"`
	AppLanguage    string    `form:"appLanguageOrder"  search:"type:order;column:AppLanguage;table:t_fb_login_failed_ods"`
	Country        string    `form:"countryOrder"  search:"type:order;column:Country;table:t_fb_login_failed_ods"`
	DeviceID       string    `form:"deviceIDOrder"  search:"type:order;column:DeviceID;table:t_fb_login_failed_ods"`
	Reason         string    `form:"reasonOrder"  search:"type:order;column:Reason;table:t_fb_login_failed_ods"`
}

func (m *TFbLoginFailedGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFbLoginFailedInsertReq struct {
	Id             int       `json:"-" comment:""` //
	Platform       int64     `json:"platform" comment:"Platform"`
	NowDate        time.Time `json:"nowDate" comment:"NowDate"`
	TimeStampValue time.Time `json:"timeStampValue" comment:"TimeStampValue"`
	AccType        string    `json:"accType" comment:"AccType"`
	Channel        string    `json:"channel" comment:"Channel"`
	AppVersion     string    `json:"appVersion" comment:"AppVersion"`
	AppLanguage    string    `json:"appLanguage" comment:"AppLanguage"`
	Country        string    `json:"country" comment:"Country"`
	DeviceID       string    `json:"deviceID" comment:"DeviceID"`
	Reason         string    `json:"reason" comment:"Reason"`
	common.ControlBy
}

func (s *TFbLoginFailedInsertReq) Generate(model *models.TFbLoginFailed) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.Platform = s.Platform
	model.NowDate = s.NowDate
	model.TimeStampValue = s.TimeStampValue
	model.AccType = s.AccType
	model.Channel = s.Channel
	model.AppVersion = s.AppVersion
	model.AppLanguage = s.AppLanguage
	model.Country = s.Country
	model.DeviceID = s.DeviceID
	model.Reason = s.Reason
}

func (s *TFbLoginFailedInsertReq) GetId() interface{} {
	return s.Id
}

type TFbLoginFailedUpdateReq struct {
	Id             int       `uri:"id" comment:""` //
	Platform       int64     `json:"platform" comment:"Platform"`
	NowDate        time.Time `json:"nowDate" comment:"NowDate"`
	TimeStampValue time.Time `json:"timeStampValue" comment:"TimeStampValue"`
	AccType        string    `json:"accType" comment:"AccType"`
	Channel        string    `json:"channel" comment:"Channel"`
	AppVersion     string    `json:"appVersion" comment:"AppVersion"`
	AppLanguage    string    `json:"appLanguage" comment:"AppLanguage"`
	Country        string    `json:"country" comment:"Country"`
	DeviceID       string    `json:"deviceID" comment:"DeviceID"`
	Reason         string    `json:"reason" comment:"Reason"`
	common.ControlBy
}

func (s *TFbLoginFailedUpdateReq) Generate(model *models.TFbLoginFailed) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.Platform = s.Platform
	model.NowDate = s.NowDate
	model.TimeStampValue = s.TimeStampValue
	model.AccType = s.AccType
	model.Channel = s.Channel
	model.AppVersion = s.AppVersion
	model.AppLanguage = s.AppLanguage
	model.Country = s.Country
	model.DeviceID = s.DeviceID
	model.Reason = s.Reason
}

func (s *TFbLoginFailedUpdateReq) GetId() interface{} {
	return s.Id
}

// TFbLoginFailedGetReq 功能获取请求参数
type TFbLoginFailedGetReq struct {
	Id int `uri:"id"`
}

func (s *TFbLoginFailedGetReq) GetId() interface{} {
	return s.Id
}

// TFbLoginFailedDeleteReq 功能删除请求参数
type TFbLoginFailedDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFbLoginFailedDeleteReq) GetId() interface{} {
	return s.Ids
}
