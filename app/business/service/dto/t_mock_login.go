package dto

import (
	"time"

	"go-admin/app/business/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TMockLoginGetPageReq struct {
	dto.Pagination `search:"-"`
	Uid            string `form:"uid"  search:"type:exact;column:uid;table:t_mock_login" comment:"用户ID"`
	YourUid        string `form:"yourUid"  search:"type:exact;column:your_uid;table:t_mock_login" comment:"your_uid"`
	ReplacedUid    string `form:"replacedUid"  search:"type:exact;column:replaced_uid;table:t_mock_login" comment:"replaced_uid"`
	//TimeBegin time.Time `form:"timeBegin"  search:"type:exact;column:time_begin;table:t_mock_login" comment:"开始时间"`
	BeginTime string `form:"beginTime" search:"type:gte;column:time_begin;table:t_mock_login" comment:"开始时间"`
	EndTime   string `form:"endTime" search:"type:lte;column:time_begin;table:t_mock_login" comment:"开始时间"`
	DeleteFlg string `form:"deleteFlg"  search:"type:exact;column:delete_flg;table:t_mock_login" comment:"逻辑删除标记 1：删除"`
	PlanetId  int64  `form:"planetId"  search:"type:exact;column:planet_id;table:t_mock_login" comment:"planet"`
	TMockLoginOrder
}

type TMockLoginOrder struct {
	Id          int       `form:"idOrder"  search:"type:order;column:id;table:t_mock_login"`
	PlanetId    int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_mock_login"`
	Uid         string    `form:"uidOrder"  search:"type:order;column:uid;table:t_mock_login"`
	YourUid     string    `form:"yourUidOrder"  search:"type:order;column:your_uid;table:t_mock_login"`
	ReplacedUid string    `form:"replacedUidOrder"  search:"type:order;column:replaced_uid;table:t_mock_login"`
	TimeBegin   time.Time `form:"timeBeginOrder"  search:"type:order;column:time_begin;table:t_mock_login"`
	Remark      string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_mock_login"`
	DeleteFlg   string    `form:"deleteFlgOrder"  search:"type:order;column:delete_flg;table:t_mock_login"`
	CreateBy    string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_mock_login"`
	UpdateBy    string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_mock_login"`
	CreatedAt   time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_mock_login"`
	UpdatedAt   time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_mock_login"`
	DeletedAt   time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_mock_login"`
}

func (m *TMockLoginGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TMockLoginInsertReq struct {
	Id          int       `json:"-" comment:""` //
	PlanetId    int64     `json:"planetId" comment:"planet"`
	Uid         string    `json:"uid" comment:"用户ID"`
	YourUid     string    `json:"yourUid" comment:"your_uid"`
	ReplacedUid string    `json:"replacedUid" comment:"replaced_uid"`
	TimeBegin   time.Time `json:"timeBegin" comment:"开始时间"`
	Remark      string    `json:"remark" comment:"备注"`
	DeleteFlg   int32     `json:"deleteFlg" comment:"逻辑删除标记 1：删除"`
	common.ControlBy
}

func (s *TMockLoginInsertReq) Generate(model *models.TMockLogin) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.YourUid = s.YourUid
	model.ReplacedUid = s.ReplacedUid
	model.TimeBegin = s.TimeBegin
	model.Remark = s.Remark
	model.DeleteFlg = s.DeleteFlg
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TMockLoginInsertReq) GetId() interface{} {
	return s.Id
}

type TMockLoginUpdateReq struct {
	Id          int       `uri:"id" comment:""` //
	PlanetId    int64     `json:"planetId" comment:"planet"`
	Uid         string    `json:"uid" comment:"用户ID"`
	YourUid     string    `json:"yourUid" comment:"your_uid"`
	ReplacedUid string    `json:"replacedUid" comment:"replaced_uid"`
	TimeBegin   time.Time `json:"timeBegin" comment:"开始时间"`
	Remark      string    `json:"remark" comment:"备注"`
	DeleteFlg   int32     `json:"deleteFlg" comment:"逻辑删除标记 1：删除"`
	common.ControlBy
}

func (s *TMockLoginUpdateReq) Generate(model *models.TMockLogin) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.YourUid = s.YourUid
	model.ReplacedUid = s.ReplacedUid
	model.TimeBegin = s.TimeBegin
	model.Remark = s.Remark
	model.DeleteFlg = s.DeleteFlg
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TMockLoginUpdateReq) GetId() interface{} {
	return s.Id
}

// TMockLoginGetReq 功能获取请求参数
type TMockLoginGetReq struct {
	Id int `uri:"id"`
}

func (s *TMockLoginGetReq) GetId() interface{} {
	return s.Id
}

// TMockLoginDeleteReq 功能删除请求参数
type TMockLoginDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TMockLoginDeleteReq) GetId() interface{} {
	return s.Ids
}
