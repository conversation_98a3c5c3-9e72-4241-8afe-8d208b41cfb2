package dto

import (
	"time"

	"go-admin/app/business/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TPushLogGetPageReq2 struct {
	dto.Pagination `search:"-"`
	Uid            string `form:"uid"  search:"type:exact;column:Uid;table:t_push_log_ods" comment:"用户ID"`
	Lang           string `form:"lang"  search:"type:exact;column:Lang;table:t_push_log_ods" comment:"版本语言"`
	PushType       string `form:"pushType"  search:"type:exact;column:PushType;table:t_push_log_ods" comment:"PushType"`
	PushId         string `form:"pushId"  search:"type:exact;column:PushId;table:t_push_log_ods" comment:"PushId"`
	//TimeStampValue time.Time `form:"timeStampValue"  search:"type:exact;column:TimeStampValue;table:t_push_log_ods" comment:"PushErrorMsg"`
	BeginTime string `form:"beginTime" search:"type:gte;column:TimeStampValue;table:t_push_log_ods" comment:"TimeStampValue"`
	EndTime   string `form:"endTime" search:"type:lte;column:TimeStampValue;table:t_push_log_ods" comment:"TimeStampValue"`
	PushAll   bool   `form:"pushAll" comment:"区别检索所有或者部分数据"`

	TPushLogOrder
}

type TPushLogGetPageReq struct {
	dto.Pagination `search:"-"`
	Uid            string `form:"uid"  search:"type:exact;column:Uid;table:t_push_log_ods" comment:"用户ID"`
	Lang           string `form:"lang"  search:"type:exact;column:Lang;table:t_push_log_ods" comment:"版本语言"`
	PushType       string `form:"pushType"  search:"type:exact;column:PushType;table:t_push_log_ods" comment:"PushType"`
	PushId         string `form:"pushId"  search:"type:exact;column:PushId;table:t_push_log_ods" comment:"PushId"`
	//TimeStampValue time.Time `form:"timeStampValue"  search:"type:exact;column:TimeStampValue;table:t_push_log_ods" comment:"PushErrorMsg"`
	BeginTime string `form:"beginTime" search:"type:gte;column:TimeStampValue;table:t_push_log_ods" comment:"TimeStampValue"`
	EndTime   string `form:"endTime" search:"type:lte;column:TimeStampValue;table:t_push_log_ods" comment:"TimeStampValue"`

	TPushLogOrder
}

type TPushLogOrder struct {
	Id             int       `form:"idOrder"  search:"type:order;column:id;table:t_push_log_ods"`
	PlanetId       string    `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_push_log_ods"`
	Uid            string    `form:"uidOrder"  search:"type:order;column:Uid;table:t_push_log_ods"`
	Lang           string    `form:"langOrder"  search:"type:order;column:Lang;table:t_push_log_ods"`
	PushType       string    `form:"pushTypeOrder"  search:"type:order;column:PushType;table:t_push_log_ods"`
	PushId         string    `form:"pushIdOrder"  search:"type:order;column:PushId;table:t_push_log_ods"`
	PushTotalNum   string    `form:"pushTotalNumOrder"  search:"type:order;column:PushTotalNum;table:t_push_log_ods"`
	PushOkNum      string    `form:"pushOkNumOrder"  search:"type:order;column:PushOkNum;table:t_push_log_ods"`
	PushFailNum    string    `form:"pushFailNumOrder"  search:"type:order;column:PushFailNum;table:t_push_log_ods"`
	PushErrorMsg   string    `form:"pushErrorMsgOrder"  search:"type:order;column:PushErrorMsg;table:t_push_log_ods"`
	TimeStampValue time.Time `form:"timeStampValueOrder"  search:"type:order;column:TimeStampValue;table:t_push_log_ods"`
	CreateBy       string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_push_log_ods"`
	UpdateBy       string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_push_log_ods"`
	CreatedAt      time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_push_log_ods"`
	UpdatedAt      time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_push_log_ods"`
	DeletedAt      time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_push_log_ods"`
}

func (m *TPushLogGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TPushLogInsertReq struct {
	Id             int       `json:"-" comment:""` //
	PlanetId       string    `json:"planetId" comment:"planet"`
	Uid            string    `json:"uid" comment:"用户ID"`
	Lang           string    `json:"lang" comment:"版本语言"`
	PushType       string    `json:"pushType" comment:"PushType"`
	PushId         string    `json:"pushId" comment:"PushId"`
	PushTotalNum   string    `json:"pushTotalNum" comment:"PushTotalNum"`
	PushOkNum      string    `json:"pushOkNum" comment:"PushOkNum"`
	PushFailNum    string    `json:"pushFailNum" comment:"PushFailNum"`
	PushErrorMsg   string    `json:"pushErrorMsg" comment:"PushErrorMsg"`
	TimeStampValue time.Time `json:"timeStampValue" comment:"PushErrorMsg"`
	common.ControlBy
}

func (s *TPushLogInsertReq) Generate(model *models.TPushLog) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.Lang = s.Lang
	model.PushType = s.PushType
	model.PushId = s.PushId
	model.PushTotalNum = s.PushTotalNum
	model.PushOkNum = s.PushOkNum
	model.PushFailNum = s.PushFailNum
	model.PushErrorMsg = s.PushErrorMsg
	model.TimeStampValue = s.TimeStampValue
	//model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TPushLogInsertReq) GetId() interface{} {
	return s.Id
}

type TPushLogUpdateReq struct {
	Id             int       `uri:"id" comment:""` //
	PlanetId       string    `json:"planetId" comment:"planet"`
	Uid            string    `json:"uid" comment:"用户ID"`
	Lang           string    `json:"lang" comment:"版本语言"`
	PushType       string    `json:"pushType" comment:"PushType"`
	PushId         string    `json:"pushId" comment:"PushId"`
	PushTotalNum   string    `json:"pushTotalNum" comment:"PushTotalNum"`
	PushOkNum      string    `json:"pushOkNum" comment:"PushOkNum"`
	PushFailNum    string    `json:"pushFailNum" comment:"PushFailNum"`
	PushErrorMsg   string    `json:"pushErrorMsg" comment:"PushErrorMsg"`
	TimeStampValue time.Time `json:"timeStampValue" comment:"PushErrorMsg"`
	common.ControlBy
}

func (s *TPushLogUpdateReq) Generate(model *models.TPushLog) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.Lang = s.Lang
	model.PushType = s.PushType
	model.PushId = s.PushId
	model.PushTotalNum = s.PushTotalNum
	model.PushOkNum = s.PushOkNum
	model.PushFailNum = s.PushFailNum
	model.PushErrorMsg = s.PushErrorMsg
	model.TimeStampValue = s.TimeStampValue
	//model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TPushLogUpdateReq) GetId() interface{} {
	return s.Id
}

// TPushLogGetReq 功能获取请求参数
type TPushLogGetReq struct {
	Id int `uri:"id"`
}

func (s *TPushLogGetReq) GetId() interface{} {
	return s.Id
}

// TPushLogDeleteReq 功能删除请求参数
type TPushLogDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TPushLogDeleteReq) GetId() interface{} {
	return s.Ids
}
