package dto

import (
	"time"

	"go-admin/app/business/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TFansPageGetPageReq struct {
	dto.Pagination `search:"-"`
	StartTimeShow  time.Time `form:"startTimeShow"  search:"type:gte;column:start_time_show;table:t_fans_page" comment:"开始时间-显示"`
	EndTimeShow    time.Time `form:"endTimeShow"  search:"type:lte;column:end_time_show;table:t_fans_page" comment:"结束时间-显示"`
	PlanetId       int64     `form:"planetId"  search:"type:exact;column:planet_id;table:t_fans_page" comment:"planet"`
	TFansPageOrder
}

type TFansPageOrder struct {
	Id             int       `form:"idOrder"  search:"type:order;column:id;table:t_fans_page"`
	PlanetId       int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_fans_page"`
	StartTimeShow  time.Time `form:"startTimeShowOrder"  search:"type:order;column:start_time_show;table:t_fans_page"`
	StartTimeValue string    `form:"startTimeValueOrder"  search:"type:order;column:start_time_value;table:t_fans_page"`
	EndTimeShow    time.Time `form:"endTimeShowOrder"  search:"type:order;column:end_time_show;table:t_fans_page"`
	EndTimeValue   string    `form:"endTimeValueOrder"  search:"type:order;column:end_time_value;table:t_fans_page"`
	UrlLong        string    `form:"urlLongOrder"  search:"type:order;column:url_long;table:t_fans_page"`
	UrlShort       string    `form:"urlShortOrder"  search:"type:order;column:url_short;table:t_fans_page"`
	Cdkey          string    `form:"cdkeyOrder"  search:"type:order;column:cdkey;table:t_fans_page"`
	RewardJson     string    `form:"rewardJsonOrder"  search:"type:order;column:reward_json;table:t_fans_page"`
	RpcJson        string    `form:"rpcJsonOrder"  search:"type:order;column:rpc_json;table:t_fans_page"`
	Remark         string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_fans_page"`
	CreateBy       string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_fans_page"`
	UpdateBy       string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_fans_page"`
	CreatedAt      time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_fans_page"`
	UpdatedAt      time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_fans_page"`
	DeletedAt      time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_fans_page"`
}

func (m *TFansPageGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFansPageInsertReq struct {
	Id             int       `json:"-" comment:""` //
	PlanetId       int64     `json:"planetId" comment:"planet"`
	StartTimeShow  time.Time `json:"startTimeShow" comment:"开始时间-显示"`
	StartTimeValue string    `json:"startTimeValue" comment:"开始时间-值"`
	EndTimeShow    time.Time `json:"endTimeShow" comment:"结束时间-显示"`
	EndTimeValue   string    `json:"endTimeValue" comment:"结束时间-值-值"`
	UrlLong        string    `json:"urlLong" comment:"长链接"`
	UrlShort       string    `json:"urlShort" comment:"短链接"`
	Cdkey          string    `json:"cdkey" comment:"奖励码"`
	RewardJson     string    `json:"rewardJson" comment:"奖励json"`
	RpcJson        string    `json:"rpcJson" comment:"RPC请求Json"`
	Remark         string    `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TFansPageInsertReq) Generate(model *models.TFansPage) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.StartTimeShow = s.StartTimeShow
	formatStartTime, _ := time.Parse("2006-01-02 15:04:05", s.StartTimeValue)
	formatEndTime, _ := time.Parse("2006-01-02 15:04:05", s.EndTimeValue)
	model.StartTimeValue = formatStartTime.Unix()
	model.EndTimeShow = s.EndTimeShow
	model.EndTimeValue = formatEndTime.Unix()
	model.UrlLong = s.UrlLong
	model.UrlShort = s.UrlShort
	model.Cdkey = s.Cdkey
	model.RewardJson = s.RewardJson
	model.RpcJson = s.RpcJson
	model.Remark = s.Remark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFansPageInsertReq) GetId() interface{} {
	return s.Id
}

type TFansPageUpdateReq struct {
	Id             int       `uri:"id" comment:""` //
	PlanetId       int64     `json:"planetId" comment:"planet"`
	StartTimeShow  time.Time `json:"startTimeShow" comment:"开始时间-显示"`
	StartTimeValue string    `json:"startTimeValue" comment:"开始时间-值"`
	EndTimeShow    time.Time `json:"endTimeShow" comment:"结束时间-显示"`
	EndTimeValue   string    `json:"endTimeValue" comment:"结束时间-值-值"`
	UrlLong        string    `json:"urlLong" comment:"长链接"`
	UrlShort       string    `json:"urlShort" comment:"短链接"`
	Cdkey          string    `json:"cdkey" comment:"奖励码"`
	RewardJson     string    `json:"rewardJson" comment:"奖励json"`
	RpcJson        string    `json:"rpcJson" comment:"RPC请求Json"`
	Remark         string    `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TFansPageUpdateReq) GetId() interface{} {
	return s.Id
}

// TFansPageGetReq 功能获取请求参数
type TFansPageGetReq struct {
	Id int `uri:"id"`
}

func (s *TFansPageGetReq) GetId() interface{} {
	return s.Id
}

// TFansPageDeleteReq 功能删除请求参数
type TFansPageDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFansPageDeleteReq) GetId() interface{} {
	return s.Ids
}
