package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/business/models"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TMockLogin struct {
	service.Service
}

// GetPage 获取TMockLogin列表
func (e *TMockLogin) GetPage(c *dto.TMockLoginGetPageReq, p *actions.DataPermission, list *[]models.TMockLogin, count *int64) error {
	var err error
	var data models.TMockLogin

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Order("delete_flg asc, created_at desc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TMockLoginService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TMockLogin对象
func (e *TMockLogin) Get(d *dto.TMockLoginGetReq, p *actions.DataPermission, model *models.TMockLogin) error {
	var data models.TMockLogin

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTMockLogin error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TMockLogin对象
func (e *TMockLogin) Insert(c *dto.TMockLoginInsertReq) error {
	var err error
	var data models.TMockLogin
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMockLoginService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TMockLogin对象
func (e *TMockLogin) Update(c *dto.TMockLoginUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TMockLogin{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TMockLoginService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TMockLogin
func (e *TMockLogin) Remove(d *dto.TMockLoginDeleteReq, p *actions.DataPermission) error {
	var data models.TMockLogin

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).First(&data, d.GetId()).Update("delete_flg", 1)
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTMockLogin error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
