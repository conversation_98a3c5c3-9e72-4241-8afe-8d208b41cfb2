package service

import (
	"errors"
	"go-admin/app/business/models"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
	"go-admin/common/database"
	cDto "go-admin/common/dto"
	"go-admin/common/timext"
	"go-admin/common/tool"
	"go-admin/core/sdk"
	"go-admin/core/sdk/service"
	"gorm.io/gorm"
)

type TFbLoginFailed struct {
	service.Service
}

// GetPage 获取TFbLoginFailed列表
func (e *TFbLoginFailed) GetPage(c *dto.TFbLoginFailedGetPageReq, p *actions.DataPermission, list *[]models.TFbLoginFailed, count *int64) error {
	var err error
	var data models.TFbLoginFailed

	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)

	err = gameDb.Model(&data).Order("TimeStampValue desc").
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFbLoginFailedService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// GetPageGroup 获取TFbLoginFailed 汇总列表
func (e *TFbLoginFailed) GetPageGroup(c *dto.TFbLoginFailedGetPageReq, p *actions.DataPermission, list *[]models.TFbLoginFailedGroup, count *int64) error {
	var err error

	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)

	sqlStr := `SELECT
		t.Reason,
		COUNT(t.NowDate) as Count
	FROM
		t_fb_login_failed_ods t
	WHERE t.NowDate >= ?
	GROUP BY t.Reason`
	firstDayOfMonth := timext.GetFirstDateOfMonth(c.TimeStampValue).Format("2006-01-02")
	err = gameDb.Raw(sqlStr, firstDayOfMonth).Scan(&list).Error
	if err != nil {
		e.Log.Errorf("TFbLoginFailed GetPageGroup error:%s \r\n", err)
		return err
	}

	*count = int64(len(*list))

	return nil
}

// Get 获取TFbLoginFailed对象
func (e *TFbLoginFailed) Get(d *dto.TFbLoginFailedGetReq, p *actions.DataPermission, model *models.TFbLoginFailed) error {
	var data models.TFbLoginFailed

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFbLoginFailed error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFbLoginFailed对象
func (e *TFbLoginFailed) Insert(c *dto.TFbLoginFailedInsertReq) error {
	var err error
	var data models.TFbLoginFailed
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFbLoginFailedService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFbLoginFailed对象
func (e *TFbLoginFailed) Update(c *dto.TFbLoginFailedUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TFbLoginFailed{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TFbLoginFailedService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TFbLoginFailed
func (e *TFbLoginFailed) Remove(d *dto.TFbLoginFailedDeleteReq, p *actions.DataPermission) error {
	var data models.TFbLoginFailed

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTFbLoginFailed error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
