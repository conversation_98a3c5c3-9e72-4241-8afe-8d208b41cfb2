package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/business/models"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFansPage struct {
	service.Service
}

// GetPage 获取TFansPage列表
func (e *TFansPage) GetPage(c *dto.TFansPageGetPageReq, p *actions.DataPermission, list *[]models.TFansPage, count *int64) error {
	var err error
	var data models.TFansPage

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Order("id DESC").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFansPageService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFansPage对象
func (e *TFansPage) Get(d *dto.TFansPageGetReq, p *actions.DataPermission, model *models.TFansPage) error {
	var data models.TFansPage

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFansPage error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFansPage对象
func (e *TFansPage) Insert(c *dto.TFansPageInsertReq) error {
	var err error
	var data models.TFansPage
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFansPageService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Remove 删除TFansPage
func (e *TFansPage) Remove(d *dto.TFansPageDeleteReq, p *actions.DataPermission) error {
	var data models.TFansPage

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTFansPage error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}

// Load 是否存在cdKey
func (e *TFansPage) Load(cdKey string) (bool, error) {
	data := models.TFansPage{}

	tx := e.Orm.Model(&data).Where("cdkey = ?", cdKey).
		First(&data)
	err := tx.Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		e.Log.Errorf("Service GetTFansPage error:%s \r\n", err)
		return false, err
	}

	if tx.RowsAffected > 0 {
		return true, nil
	}
	return false, nil
}
