package service

import (
	"errors"
	"go-admin/common/database"
	"go-admin/common/tool"
	"go-admin/core/sdk"
	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/business/models"
	"go-admin/app/business/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TPushLog struct {
	service.Service
}

// GetPage 获取TPushLog列表
func (e *TPushLog) GetPage(c *dto.TPushLogGetPageReq, p *actions.DataPermission, list *[]models.TPushLog, count *int64) error {
	var err error
	var data models.TPushLog

	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)

	err = gameDb.Model(&data).Order("TimeStampValue desc").
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TPushLogService GetPage error:%s \r\n", err)
		return err
	}

	return nil
}

func (e *TPushLog) GetPageAll(c *dto.TPushLogGetPageReq, p *actions.DataPermission, data *models.TPushAllRet) error {

	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)

	sqlStr := `SELECT
		sum(PushOkNum) as PushOkNum,
		sum(PushTotalNum) as PushAllNum
	from
		t_push_log_ods;`
	err := gameDb.Raw(sqlStr).Scan(&data).Error
	if err != nil {
		e.Log.Errorf("TPushLogService GetPageAll error:%s \r\n", err)
		return err
	}

	return nil
}

// Get 获取TPushLog对象
func (e *TPushLog) Get(d *dto.TPushLogGetReq, p *actions.DataPermission, model *models.TPushLog) error {
	var data models.TPushLog

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTPushLog error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TPushLog对象
func (e *TPushLog) Insert(c *dto.TPushLogInsertReq) error {
	var err error
	var data models.TPushLog
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TPushLogService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TPushLog对象
func (e *TPushLog) Update(c *dto.TPushLogUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TPushLog{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TPushLogService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TPushLog
func (e *TPushLog) Remove(d *dto.TPushLogDeleteReq, p *actions.DataPermission) error {
	var data models.TPushLog

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTPushLog error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
