package models

import (
	"go-admin/common/models"
)

type TRodHolders struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    RodSlot string `json:"rodSlot" gorm:"type:varchar(64);comment:竿架槽"` 
    StandCount string `json:"standCount" gorm:"type:int(11);comment:竿架"` 
    BiteAlarm string `json:"biteAlarm" gorm:"type:varchar(64);comment:咬钩报警器"` 
    Weight string `json:"weight" gorm:"type:varchar(64);comment:重量（千克）"` 
    Money string `json:"money" gorm:"type:varchar(64);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:varchar(64);comment:解锁等级"` 
    Description string `json:"description" gorm:"type:varchar(512);comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(128);comment:描述翻译"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TRodHolders) TableName() string {
    return "t_rod_holders"
}

func (e *TRodHolders) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TRodHolders) GetId() interface{} {
	return e.Id
}