package models

import (

	"go-admin/common/models"

)

type THats struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Material string `json:"material" gorm:"type:varchar(64);comment:材料"` 
    Tackles string `json:"tackles" gorm:"type:int(11);comment:辅助"` 
    Flashlight string `json:"flashlight" gorm:"type:varchar(64);comment:手电筒"` 
    FlashlightSlot string `json:"flashlightSlot" gorm:"type:varchar(64);comment:手电筒槽"` 
    Money string `json:"money" gorm:"type:varchar(128);comment:货币"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    Description string `json:"description" gorm:"type:text;comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(256);comment:描述翻译"` 
    Remark string `json:"remark" gorm:"type:varchar(64);comment:备注"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (THats) TableName() string {
    return "t_hats"
}

func (e *THats) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *THats) GetId() interface{} {
	return e.Id
}