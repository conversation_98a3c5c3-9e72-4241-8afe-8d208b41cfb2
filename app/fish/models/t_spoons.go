package models

import (
	"go-admin/common/models"
)

type TSpoons struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Color string `json:"color" gorm:"type:varchar(64);comment:颜色"` 
    Weight string `json:"weight" gorm:"type:varchar(64);comment:重量(g)"` 
    Length string `json:"length" gorm:"type:varchar(64);comment:长度(cm)"` 
    HookSize string `json:"hookSize" gorm:"type:varchar(64);comment:鱼钩"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Money string `json:"money" gorm:"type:varchar(512);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    FitReel string `json:"fitReel" gorm:"type:varchar(64);comment:适配鱼轮"` 
    FitBait string `json:"fitBait" gorm:"type:varchar(64);comment:适配饵类"` 
    Description string `json:"description" gorm:"type:text;comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(256);comment:描述翻译"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TSpoons) TableName() string {
    return "t_spoons"
}

func (e *TSpoons) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TSpoons) GetId() interface{} {
	return e.Id
}