package models

import (
	"go-admin/common/models"
)

type TSpinningRods struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    Length string `json:"length" gorm:"type:varchar(64);comment:长度(m)"` 
    LureWeight string `json:"lureWeight" gorm:"type:varchar(64);comment:饵重（g）"` 
    Power string `json:"power" gorm:"type:varchar(64);comment:硬度"` 
    LineWeight string `json:"lineWeight" gorm:"type:varchar(64);comment:钓重（kg）"` 
    Action string `json:"action" gorm:"type:varchar(64);comment:调性"` 
    Pieces string `json:"pieces" gorm:"type:int(11);comment:节数"` 
    Guides string `json:"guides" gorm:"type:int(11);comment:导环线"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Money string `json:"money" gorm:"type:varchar(64);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    Technology string `json:"technology" gorm:"type:varchar(128);comment:技术"` 
    FitReel string `json:"fitReel" gorm:"type:varchar(64);comment:适配鱼轮"` 
    FitBait string `json:"fitBait" gorm:"type:varchar(64);comment:适配饵类"` 
    Description string `json:"description" gorm:"type:text;comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(512);comment:描述翻译"` 
    Remark string `json:"remark" gorm:"type:varchar(64);comment:备注"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TSpinningRods) TableName() string {
    return "t_spinning_rods"
}

func (e *TSpinningRods) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TSpinningRods) GetId() interface{} {
	return e.Id
}