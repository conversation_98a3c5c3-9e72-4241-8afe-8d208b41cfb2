package models

import (
	"go-admin/common/models"
)

type TWaistCoats struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    Material string `json:"material" gorm:"type:varchar(64);comment:材质"` 
    StorageCapacity string `json:"storageCapacity" gorm:"type:varchar(64);comment:容量"` 
    Reels string `json:"reels" gorm:"type:varchar(64);comment:卷线器"` 
    Lines string `json:"lines" gorm:"type:varchar(64);comment:钓线"` 
    Tackles string `json:"tackles" gorm:"type:int(11);comment:渔具"` 
    Money string `json:"money" gorm:"type:varchar(64);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Description string `json:"description" gorm:"type:text;comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(256);comment:描述翻译"` 
    Remark string `json:"remark" gorm:"type:varchar(64);comment:备注"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TWaistCoats) TableName() string {
    return "t_waist_coats"
}

func (e *TWaistCoats) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TWaistCoats) GetId() interface{} {
	return e.Id
}