package models

import (
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     
     

	"go-admin/common/models"

)

type TFishInformation struct {
    models.Model
    
    EnName string `json:"enName" gorm:"type:varchar(64);comment:英文名称"` 
    CnName string `json:"cnName" gorm:"type:varchar(64);comment:中文名称"` 
    Species string `json:"species" gorm:"type:varchar(64);comment:种类"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    WikiImageUrl string `json:"wikiImageUrl" gorm:"type:varchar(128);comment:wiki图片地址"` 
    Rule string `json:"rule" gorm:"type:varchar(64);comment:规则"` 
    Weight string `json:"weight" gorm:"type:varchar(64);comment:重量(max)"` 
    Reward string `json:"reward" gorm:"type:varchar(64);comment:单价(kg)"` 
    Length string `json:"length" gorm:"type:varchar(64);comment:长度(cm)"` 
    Baits string `json:"baits" gorm:"type:varchar(256);comment:诱饵"` 
    CnBaits string `json:"cnBaits" gorm:"type:varchar(64);comment:诱饵翻译"` 
    Lures string `json:"lures" gorm:"type:varchar(256);comment:假饵"` 
    CnLures string `json:"cnLures" gorm:"type:varchar(128);comment:假饵翻译"` 
    Haunt string `json:"haunt" gorm:"type:varchar(512);comment:出没"` 
    Scene string `json:"scene" gorm:"type:varchar(512);comment:场景"` 
    Area string `json:"area" gorm:"type:varchar(64);comment:区域"` 
    Water string `json:"water" gorm:"type:varchar(64);comment:水域"` 
    Food string `json:"food" gorm:"type:varchar(64);comment:食物"` 
    Feature string `json:"feature" gorm:"type:varchar(128);comment:特征"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TFishInformation) TableName() string {
    return "t_fish_information"
}

func (e *TFishInformation) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFishInformation) GetId() interface{} {
	return e.Id
}