package models

import (
	"go-admin/common/models"
)

type TStringer struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    MaxSingleFish string `json:"maxSingleFish" gorm:"type:varchar(64);comment:单条鱼最大重量（kg）"` 
    MaxTotalFish string `json:"maxTotalFish" gorm:"type:int(11);comment:鱼总重量（kg）"` 
    FishFriendly string `json:"fishFriendly" gorm:"type:varchar(64);comment:对鱼是否有害"` 
    Durability string `json:"durability" gorm:"type:varchar(64);comment:耐用性"` 
    Material string `json:"material" gorm:"type:varchar(64);comment:材质"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Money string `json:"money" gorm:"type:varchar(64);comment:货币"` 
    Description string `json:"description" gorm:"type:varchar(512);comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(128);comment:描述中文"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TStringer) TableName() string {
    return "t_stringer"
}

func (e *TStringer) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TStringer) GetId() interface{} {
	return e.Id
}