package models

import (
	"go-admin/common/models"
)

type TSpinReels struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(128);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    GearRatio string `json:"gearRatio" gorm:"type:varchar(64);comment:传动比"` 
    Recovery string `json:"recovery" gorm:"type:int(11);comment:收线速度（cm）"` 
    LineCapacity string `json:"lineCapacity" gorm:"type:varchar(64);comment:绕线量(mm/m)"` 
    MaxDrag string `json:"maxDrag" gorm:"type:varchar(64);comment:最大拉力（kg）"` 
    BallBearings string `json:"ballBearings" gorm:"type:varchar(64);comment:滚珠轴承"` 
    Weight string `json:"weight" gorm:"type:int(11);comment:重量（g）"` 
    Drag string `json:"drag" gorm:"type:varchar(64);comment:摩擦离合器"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Money string `json:"money" gorm:"type:varchar(128);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    Technology string `json:"technology" gorm:"type:varchar(256);comment:技术"` 
    Description string `json:"description" gorm:"type:text;comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(512);comment:描述翻译"` 
    Remark string `json:"remark" gorm:"type:varchar(64);comment:备注"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TSpinReels) TableName() string {
    return "t_spin_reels"
}

func (e *TSpinReels) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TSpinReels) GetId() interface{} {
	return e.Id
}