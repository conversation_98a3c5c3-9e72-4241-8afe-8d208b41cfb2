package models

import (
	"go-admin/common/models"
)

type TFluorocarbonLeaders struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    Thickness string `json:"thickness" gorm:"type:float(10,2);comment:直径（mm）"` 
    TestWeight string `json:"testWeight" gorm:"type:varchar(64);comment:测试重量（kg）"` 
    Length string `json:"length" gorm:"type:float(10,2);comment:长度（m）"` 
    Color string `json:"color" gorm:"type:varchar(64);comment:颜色"` 
    Quantity string `json:"quantity" gorm:"type:int(11);comment:数量"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Money string `json:"money" gorm:"type:varchar(128);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    Description string `json:"description" gorm:"type:varchar(256);comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(128);comment:描述翻译"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TFluorocarbonLeaders) TableName() string {
    return "t_fluorocarbon_leaders"
}

func (e *TFluorocarbonLeaders) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFluorocarbonLeaders) GetId() interface{} {
	return e.Id
}