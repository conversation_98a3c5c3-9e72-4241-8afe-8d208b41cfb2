package models

import (
	"go-admin/common/models"
)

type TScene struct {
    models.Model
    
    Scene string `json:"scene" gorm:"type:varchar(64);comment:场景"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    TravelFee string `json:"travelFee" gorm:"type:varchar(64);comment:旅行费"` 
    FishingFee string `json:"fishingFee" gorm:"type:varchar(64);comment:钓鱼费(per_day)"` 
    BasicLicenseReleased string `json:"basicLicenseReleased" gorm:"type:text;comment:基本执照"` 
    AdvancedLicenseReleased string `json:"advancedLicenseReleased" gorm:"type:varchar(64);comment:高级执照"` 
    EnFish string `json:"enFish" gorm:"type:varchar(64);comment:鱼英文"` 
    CnFish string `json:"cnFish" gorm:"type:varchar(64);comment:鱼中文"` 
    SpeciesRule string `json:"speciesRule" gorm:"type:varchar(64);comment:鱼规则"` 
    GameList string `json:"gameList" gorm:"type:varchar(512);comment:比赛列表"` 
    SceneUrl string `json:"sceneUrl" gorm:"type:varchar(128);comment:场景图片"` 
    WeatherUrl string `json:"weatherUrl" gorm:"type:varchar(128);comment:天气图片"` 
    BasicLicenseReleasedUrl string `json:"basicLicenseReleasedUrl" gorm:"type:varchar(128);comment:基本执照图片"` 
    AdvancedLicenseReleasedUrl string `json:"advancedLicenseReleasedUrl" gorm:"type:varchar(128);comment:高级执照图片"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TScene) TableName() string {
    return "t_scene"
}

func (e *TScene) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TScene) GetId() interface{} {
	return e.Id
}