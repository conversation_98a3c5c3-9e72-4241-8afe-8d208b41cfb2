package models

import (
	"go-admin/common/models"
)

type TFluorocarbonLines struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    Thickness string `json:"thickness" gorm:"type:float(10,2);comment:直径（mm）"` 
    TestWeight string `json:"testWeight" gorm:"type:varchar(64);comment:测试重量（kg）"` 
    Color string `json:"color" gorm:"type:varchar(64);comment:颜色"` 
    Length string `json:"length" gorm:"type:varchar(64);comment:长度（m）"` 
    Money string `json:"money" gorm:"type:varchar(256);comment:货币"` 
    BaitCoin string `json:"baitCoin" gorm:"type:varchar(64);comment:饵币"` 
    ClubCurrency string `json:"clubCurrency" gorm:"type:varchar(64);comment:俱乐部币"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Use string `json:"use" gorm:"type:varchar(64);comment:使用"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:text;comment:描述翻译"` 
    Remark string `json:"remark" gorm:"type:varchar(256);comment:备注"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TFluorocarbonLines) TableName() string {
    return "t_fluorocarbon_lines"
}

func (e *TFluorocarbonLines) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFluorocarbonLines) GetId() interface{} {
	return e.Id
}