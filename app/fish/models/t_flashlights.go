package models

import (
	"go-admin/common/models"
)

type TFlashlights struct {
    models.Model
    
    Category string `json:"category" gorm:"type:varchar(64);comment:类别"` 
    Resident string `json:"resident" gorm:"type:varchar(64);comment:常驻"` 
    CnTaxon string `json:"cnTaxon" gorm:"type:varchar(64);comment:类名中文"` 
    EnTaxon string `json:"enTaxon" gorm:"type:varchar(64);comment:类名英文"` 
    ImageUrl string `json:"imageUrl" gorm:"type:varchar(128);comment:图片地址"` 
    EnName string `json:"enName" gorm:"type:varchar(64);comment:名称英文"` 
    Brand string `json:"brand" gorm:"type:varchar(64);comment:品牌"` 
    LightingRange string `json:"lightingRange" gorm:"type:varchar(64);comment:照明范围"` 
    LightingSpotAngle string `json:"lightingSpotAngle" gorm:"type:varchar(64);comment:照明角度"` 
    LightingTone string `json:"lightingTone" gorm:"type:varchar(64);comment:色调"` 
    Money string `json:"money" gorm:"type:varchar(64);comment:货币"` 
    RequiredLevel string `json:"requiredLevel" gorm:"type:int(11);comment:解锁等级"` 
    Description string `json:"description" gorm:"type:varchar(512);comment:描述"` 
    CnDescriptive string `json:"cnDescriptive" gorm:"type:varchar(128);comment:描述翻译"` 
    CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"` 
    models.ModelTime
    models.ControlBy
}

func (TFlashlights) TableName() string {
    return "t_flashlights"
}

func (e *TFlashlights) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFlashlights) GetId() interface{} {
	return e.Id
}