package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCatapults struct {
	service.Service
}

// GetPage 获取TCatapults列表
func (e *TCatapults) GetPage(c *dto.TCatapultsGetPageReq, p *actions.DataPermission, list *[]models.TCatapults, count *int64) error {
	var err error
	var data models.TCatapults

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TCatapultsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCatapults对象
func (e *TCatapults) Get(d *dto.TCatapultsGetReq, p *actions.DataPermission, model *models.TCatapults) error {
	var data models.TCatapults

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCatapults error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCatapults对象
func (e *TCatapults) Insert(c *dto.TCatapultsInsertReq) error {
    var err error
    var data models.TCatapults
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCatapultsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCatapults对象
func (e *TCatapults) Update(c *dto.TCatapultsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCatapults{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCatapultsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCatapults
func (e *TCatapults) Remove(d *dto.TCatapultsDeleteReq, p *actions.DataPermission) error {
	var data models.TCatapults

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCatapults error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}