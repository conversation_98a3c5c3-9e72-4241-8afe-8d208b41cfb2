package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFishInformation struct {
	service.Service
}

// GetPage 获取TFishInformation列表
func (e *TFishInformation) GetPage(c *dto.TFishInformationGetPageReq, p *actions.DataPermission, list *[]models.TFishInformation, count *int64) error {
	var err error
	var data models.TFishInformation

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFishInformationService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFishInformation对象
func (e *TFishInformation) Get(d *dto.TFishInformationGetReq, p *actions.DataPermission, model *models.TFishInformation) error {
	var data models.TFishInformation

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFishInformation error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFishInformation对象
func (e *TFishInformation) Insert(c *dto.TFishInformationInsertReq) error {
    var err error
    var data models.TFishInformation
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFishInformationService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFishInformation对象
func (e *TFishInformation) Update(c *dto.TFishInformationUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFishInformation{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFishInformationService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFishInformation
func (e *TFishInformation) Remove(d *dto.TFishInformationDeleteReq, p *actions.DataPermission) error {
	var data models.TFishInformation

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFishInformation error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}