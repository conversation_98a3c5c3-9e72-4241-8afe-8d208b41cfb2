package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBottomRods struct {
	service.Service
}

// GetPage 获取TBottomRods列表
func (e *TBottomRods) GetPage(c *dto.TBottomRodsGetPageReq, p *actions.DataPermission, list *[]models.TBottomRods, count *int64) error {
	var err error
	var data models.TBottomRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON><PERSON><PERSON>("TBottomRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBottomRods对象
func (e *TBottomRods) Get(d *dto.TBottomRodsGetReq, p *actions.DataPermission, model *models.TBottomRods) error {
	var data models.TBottomRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBottomRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TBottomRods对象
func (e *TBottomRods) Insert(c *dto.TBottomRodsInsertReq) error {
    var err error
    var data models.TBottomRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TBottomRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TBottomRods对象
func (e *TBottomRods) Update(c *dto.TBottomRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TBottomRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TBottomRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TBottomRods
func (e *TBottomRods) Remove(d *dto.TBottomRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TBottomRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTBottomRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}