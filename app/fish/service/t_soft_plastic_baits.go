package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSoftPlasticBaits struct {
	service.Service
}

// GetPage 获取TSoftPlasticBaits列表
func (e *TSoftPlasticBaits) GetPage(c *dto.TSoftPlasticBaitsGetPageReq, p *actions.DataPermission, list *[]models.TSoftPlasticBaits, count *int64) error {
	var err error
	var data models.TSoftPlasticBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TSoftPlasticBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSoftPlasticBaits对象
func (e *TSoftPlasticBaits) Get(d *dto.TSoftPlasticBaitsGetReq, p *actions.DataPermission, model *models.TSoftPlasticBaits) error {
	var data models.TSoftPlasticBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSoftPlasticBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSoftPlasticBaits对象
func (e *TSoftPlasticBaits) Insert(c *dto.TSoftPlasticBaitsInsertReq) error {
    var err error
    var data models.TSoftPlasticBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSoftPlasticBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSoftPlasticBaits对象
func (e *TSoftPlasticBaits) Update(c *dto.TSoftPlasticBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSoftPlasticBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSoftPlasticBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSoftPlasticBaits
func (e *TSoftPlasticBaits) Remove(d *dto.TSoftPlasticBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TSoftPlasticBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSoftPlasticBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}