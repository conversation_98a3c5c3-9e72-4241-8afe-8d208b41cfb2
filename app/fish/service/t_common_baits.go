package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCommonBaits struct {
	service.Service
}

// GetPage 获取TCommonBaits列表
func (e *TCommonBaits) GetPage(c *dto.TCommonBaitsGetPageReq, p *actions.DataPermission, list *[]models.TCommonBaits, count *int64) error {
	var err error
	var data models.TCommonBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TCommonBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCommonBaits对象
func (e *TCommonBaits) Get(d *dto.TCommonBaitsGetReq, p *actions.DataPermission, model *models.TCommonBaits) error {
	var data models.TCommonBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCommonBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCommonBaits对象
func (e *TCommonBaits) Insert(c *dto.TCommonBaitsInsertReq) error {
    var err error
    var data models.TCommonBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCommonBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCommonBaits对象
func (e *TCommonBaits) Update(c *dto.TCommonBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCommonBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCommonBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCommonBaits
func (e *TCommonBaits) Remove(d *dto.TCommonBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TCommonBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCommonBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}