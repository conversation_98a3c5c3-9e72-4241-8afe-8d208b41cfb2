package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSpinners struct {
	service.Service
}

// GetPage 获取TSpinners列表
func (e *TSpinners) GetPage(c *dto.TSpinnersGetPageReq, p *actions.DataPermission, list *[]models.TSpinners, count *int64) error {
	var err error
	var data models.TSpinners

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TSpinnersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSpinners对象
func (e *TSpinners) Get(d *dto.TSpinnersGetReq, p *actions.DataPermission, model *models.TSpinners) error {
	var data models.TSpinners

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSpinners error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSpinners对象
func (e *TSpinners) Insert(c *dto.TSpinnersInsertReq) error {
    var err error
    var data models.TSpinners
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSpinnersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSpinners对象
func (e *TSpinners) Update(c *dto.TSpinnersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSpinners{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSpinnersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSpinners
func (e *TSpinners) Remove(d *dto.TSpinnersDeleteReq, p *actions.DataPermission) error {
	var data models.TSpinners

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSpinners error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}