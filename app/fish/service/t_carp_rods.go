package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCarpRods struct {
	service.Service
}

// GetPage 获取TCarpRods列表
func (e *TCarpRods) GetPage(c *dto.TCarpRodsGetPageReq, p *actions.DataPermission, list *[]models.TCarpRods, count *int64) error {
	var err error
	var data models.TCarpRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON><PERSON><PERSON>("TCarpRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCarpRods对象
func (e *TCarpRods) Get(d *dto.TCarpRodsGetReq, p *actions.DataPermission, model *models.TCarpRods) error {
	var data models.TCarpRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCarpRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCarpRods对象
func (e *TCarpRods) Insert(c *dto.TCarpRodsInsertReq) error {
    var err error
    var data models.TCarpRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCarpRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCarpRods对象
func (e *TCarpRods) Update(c *dto.TCarpRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCarpRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCarpRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCarpRods
func (e *TCarpRods) Remove(d *dto.TCarpRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TCarpRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCarpRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}