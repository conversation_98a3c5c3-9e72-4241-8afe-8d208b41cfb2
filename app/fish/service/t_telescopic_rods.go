package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TTelescopicRods struct {
	service.Service
}

// GetPage 获取TTelescopicRods列表
func (e *TTelescopicRods) GetPage(c *dto.TTelescopicRodsGetPageReq, p *actions.DataPermission, list *[]models.TTelescopicRods, count *int64) error {
	var err error
	var data models.TTelescopicRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TTelescopicRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TTelescopicRods对象
func (e *TTelescopicRods) Get(d *dto.TTelescopicRodsGetReq, p *actions.DataPermission, model *models.TTelescopicRods) error {
	var data models.TTelescopicRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTTelescopicRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TTelescopicRods对象
func (e *TTelescopicRods) Insert(c *dto.TTelescopicRodsInsertReq) error {
    var err error
    var data models.TTelescopicRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TTelescopicRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TTelescopicRods对象
func (e *TTelescopicRods) Update(c *dto.TTelescopicRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TTelescopicRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TTelescopicRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TTelescopicRods
func (e *TTelescopicRods) Remove(d *dto.TTelescopicRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TTelescopicRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTTelescopicRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}