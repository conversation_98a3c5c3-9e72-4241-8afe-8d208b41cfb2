package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBraidedLines struct {
	service.Service
}

// GetPage 获取TBraidedLines列表
func (e *TBraidedLines) GetPage(c *dto.TBraidedLinesGetPageReq, p *actions.DataPermission, list *[]models.TBraidedLines, count *int64) error {
	var err error
	var data models.TBraidedLines

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TBraidedLinesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBraidedLines对象
func (e *TBraidedLines) Get(d *dto.TBraidedLinesGetReq, p *actions.DataPermission, model *models.TBraidedLines) error {
	var data models.TBraidedLines

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBraidedLines error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TBraidedLines对象
func (e *TBraidedLines) Insert(c *dto.TBraidedLinesInsertReq) error {
    var err error
    var data models.TBraidedLines
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TBraidedLinesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TBraidedLines对象
func (e *TBraidedLines) Update(c *dto.TBraidedLinesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TBraidedLines{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TBraidedLinesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TBraidedLines
func (e *TBraidedLines) Remove(d *dto.TBraidedLinesDeleteReq, p *actions.DataPermission) error {
	var data models.TBraidedLines

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTBraidedLines error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}