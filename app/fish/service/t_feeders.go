package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFeeders struct {
	service.Service
}

// GetPage 获取TFeeders列表
func (e *TFeeders) GetPage(c *dto.TFeedersGetPageReq, p *actions.DataPermission, list *[]models.TFeeders, count *int64) error {
	var err error
	var data models.TFeeders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TFeedersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFeeders对象
func (e *TFeeders) Get(d *dto.TFeedersGetReq, p *actions.DataPermission, model *models.TFeeders) error {
	var data models.TFeeders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFeeders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFeeders对象
func (e *TFeeders) Insert(c *dto.TFeedersInsertReq) error {
    var err error
    var data models.TFeeders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFeedersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFeeders对象
func (e *TFeeders) Update(c *dto.TFeedersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFeeders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFeedersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFeeders
func (e *TFeeders) Remove(d *dto.TFeedersDeleteReq, p *actions.DataPermission) error {
	var data models.TFeeders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFeeders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}