package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSpodRods struct {
	service.Service
}

// GetPage 获取TSpodRods列表
func (e *TSpodRods) GetPage(c *dto.TSpodRodsGetPageReq, p *actions.DataPermission, list *[]models.TSpodRods, count *int64) error {
	var err error
	var data models.TSpodRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TSpodRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSpodRods对象
func (e *TSpodRods) Get(d *dto.TSpodRodsGetReq, p *actions.DataPermission, model *models.TSpodRods) error {
	var data models.TSpodRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSpodRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSpodRods对象
func (e *TSpodRods) Insert(c *dto.TSpodRodsInsertReq) error {
    var err error
    var data models.TSpodRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSpodRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSpodRods对象
func (e *TSpodRods) Update(c *dto.TSpodRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSpodRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSpodRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSpodRods
func (e *TSpodRods) Remove(d *dto.TSpodRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TSpodRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSpodRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}