package service

import (
	"errors"

	"go-admin/core/sdk/service"

	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TScene struct {
	service.Service
}

// GetPage 获取TScene列表
func (e *TScene) GetPage(c *dto.TSceneGetPageReq, p *actions.DataPermission, list *[]models.TScene, count *int64) error {
	var err error
	var data models.TScene

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TSceneService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TScene对象
func (e *TScene) Get(d *dto.TSceneGetReq, p *actions.DataPermission, model *models.TScene) error {
	var data models.TScene

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTScene error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TScene对象
func (e *TScene) Insert(c *dto.TSceneInsertReq) error {
    var err error
    var data models.TScene
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSceneService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TScene对象
func (e *TScene) Update(c *dto.TSceneUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TScene{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSceneService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TScene
func (e *TScene) Remove(d *dto.TSceneDeleteReq, p *actions.DataPermission) error {
	var data models.TScene

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTScene error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}