package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TAlarm struct {
	service.Service
}

// GetPage 获取TAlarm列表
func (e *TAlarm) GetPage(c *dto.TAlarmGetPageReq, p *actions.DataPermission, list *[]models.TAlarm, count *int64) error {
	var err error
	var data models.TAlarm

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TAlarmService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TAlarm对象
func (e *TAlarm) Get(d *dto.TAlarmGetReq, p *actions.DataPermission, model *models.TAlarm) error {
	var data models.TAlarm

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTAlarm error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TAlarm对象
func (e *TAlarm) Insert(c *dto.TAlarmInsertReq) error {
    var err error
    var data models.TAlarm
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TAlarmService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TAlarm对象
func (e *TAlarm) Update(c *dto.TAlarmUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TAlarm{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TAlarmService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TAlarm
func (e *TAlarm) Remove(d *dto.TAlarmDeleteReq, p *actions.DataPermission) error {
	var data models.TAlarm

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTAlarm error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}