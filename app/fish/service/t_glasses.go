package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TGlasses struct {
	service.Service
}

// GetPage 获取TGlasses列表
func (e *TGlasses) GetPage(c *dto.TGlassesGetPageReq, p *actions.DataPermission, list *[]models.TGlasses, count *int64) error {
	var err error
	var data models.TGlasses

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TGlassesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TGlasses对象
func (e *TGlasses) Get(d *dto.TGlassesGetReq, p *actions.DataPermission, model *models.TGlasses) error {
	var data models.TGlasses

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTGlasses error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TGlasses对象
func (e *TGlasses) Insert(c *dto.TGlassesInsertReq) error {
    var err error
    var data models.TGlasses
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TGlassesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TGlasses对象
func (e *TGlasses) Update(c *dto.TGlassesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TGlasses{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TGlassesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TGlasses
func (e *TGlasses) Remove(d *dto.TGlassesDeleteReq, p *actions.DataPermission) error {
	var data models.TGlasses

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTGlasses error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}