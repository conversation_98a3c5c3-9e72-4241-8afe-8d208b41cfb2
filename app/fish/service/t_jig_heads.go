package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TJigHeads struct {
	service.Service
}

// GetPage 获取TJigHeads列表
func (e *TJigHeads) GetPage(c *dto.TJigHeadsGetPageReq, p *actions.DataPermission, list *[]models.TJigHeads, count *int64) error {
	var err error
	var data models.TJigHeads

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON><PERSON><PERSON>("TJigHeadsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TJigHeads对象
func (e *TJigHeads) Get(d *dto.TJigHeadsGetReq, p *actions.DataPermission, model *models.TJigHeads) error {
	var data models.TJigHeads

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTJigHeads error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TJigHeads对象
func (e *TJigHeads) Insert(c *dto.TJigHeadsInsertReq) error {
    var err error
    var data models.TJigHeads
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TJigHeadsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TJigHeads对象
func (e *TJigHeads) Update(c *dto.TJigHeadsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TJigHeads{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TJigHeadsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TJigHeads
func (e *TJigHeads) Remove(d *dto.TJigHeadsDeleteReq, p *actions.DataPermission) error {
	var data models.TJigHeads

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTJigHeads error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}