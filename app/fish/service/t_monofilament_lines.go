package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TMonofilamentLines struct {
	service.Service
}

// GetPage 获取TMonofilamentLines列表
func (e *TMonofilamentLines) GetPage(c *dto.TMonofilamentLinesGetPageReq, p *actions.DataPermission, list *[]models.TMonofilamentLines, count *int64) error {
	var err error
	var data models.TMonofilamentLines

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TMonofilamentLinesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TMonofilamentLines对象
func (e *TMonofilamentLines) Get(d *dto.TMonofilamentLinesGetReq, p *actions.DataPermission, model *models.TMonofilamentLines) error {
	var data models.TMonofilamentLines

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTMonofilamentLines error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TMonofilamentLines对象
func (e *TMonofilamentLines) Insert(c *dto.TMonofilamentLinesInsertReq) error {
    var err error
    var data models.TMonofilamentLines
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMonofilamentLinesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TMonofilamentLines对象
func (e *TMonofilamentLines) Update(c *dto.TMonofilamentLinesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TMonofilamentLines{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TMonofilamentLinesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TMonofilamentLines
func (e *TMonofilamentLines) Remove(d *dto.TMonofilamentLinesDeleteReq, p *actions.DataPermission) error {
	var data models.TMonofilamentLines

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTMonofilamentLines error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}