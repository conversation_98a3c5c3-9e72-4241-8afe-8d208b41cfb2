package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TWaistCoats struct {
	service.Service
}

// GetPage 获取TWaistCoats列表
func (e *TWaistCoats) GetPage(c *dto.TWaistCoatsGetPageReq, p *actions.DataPermission, list *[]models.TWaistCoats, count *int64) error {
	var err error
	var data models.TWaistCoats

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TWaistCoatsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TWaistCoats对象
func (e *TWaistCoats) Get(d *dto.TWaistCoatsGetReq, p *actions.DataPermission, model *models.TWaistCoats) error {
	var data models.TWaistCoats

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTWaistCoats error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TWaistCoats对象
func (e *TWaistCoats) Insert(c *dto.TWaistCoatsInsertReq) error {
    var err error
    var data models.TWaistCoats
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TWaistCoatsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TWaistCoats对象
func (e *TWaistCoats) Update(c *dto.TWaistCoatsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TWaistCoats{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TWaistCoatsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TWaistCoats
func (e *TWaistCoats) Remove(d *dto.TWaistCoatsDeleteReq, p *actions.DataPermission) error {
	var data models.TWaistCoats

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTWaistCoats error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}