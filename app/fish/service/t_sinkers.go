package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSinkers struct {
	service.Service
}

// GetPage 获取TSinkers列表
func (e *TSinkers) GetPage(c *dto.TSinkersGetPageReq, p *actions.DataPermission, list *[]models.TSinkers, count *int64) error {
	var err error
	var data models.TSinkers

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TSinkersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSinkers对象
func (e *TSinkers) Get(d *dto.TSinkersGetReq, p *actions.DataPermission, model *models.TSinkers) error {
	var data models.TSinkers

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSinkers error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSinkers对象
func (e *TSinkers) Insert(c *dto.TSinkersInsertReq) error {
    var err error
    var data models.TSinkers
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSinkersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSinkers对象
func (e *TSinkers) Update(c *dto.TSinkersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSinkers{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSinkersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSinkers
func (e *TSinkers) Remove(d *dto.TSinkersDeleteReq, p *actions.DataPermission) error {
	var data models.TSinkers

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSinkers error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}