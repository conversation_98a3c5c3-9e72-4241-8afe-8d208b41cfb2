package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBassJigs struct {
	service.Service
}

// GetPage 获取TBassJigs列表
func (e *TBassJigs) GetPage(c *dto.TBassJigsGetPageReq, p *actions.DataPermission, list *[]models.TBassJigs, count *int64) error {
	var err error
	var data models.TBassJigs

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TBassJigsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBassJigs对象
func (e *TBassJigs) Get(d *dto.TBassJigsGetReq, p *actions.DataPermission, model *models.TBassJigs) error {
	var data models.TBassJigs

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBassJigs error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TBassJigs对象
func (e *TBassJigs) Insert(c *dto.TBassJigsInsertReq) error {
    var err error
    var data models.TBassJigs
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TBassJigsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TBassJigs对象
func (e *TBassJigs) Update(c *dto.TBassJigsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TBassJigs{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TBassJigsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TBassJigs
func (e *TBassJigs) Remove(d *dto.TBassJigsDeleteReq, p *actions.DataPermission) error {
	var data models.TBassJigs

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTBassJigs error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}