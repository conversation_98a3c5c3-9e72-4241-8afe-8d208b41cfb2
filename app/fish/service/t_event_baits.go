package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TEventBaits struct {
	service.Service
}

// GetPage 获取TEventBaits列表
func (e *TEventBaits) GetPage(c *dto.TEventBaitsGetPageReq, p *actions.DataPermission, list *[]models.TEventBaits, count *int64) error {
	var err error
	var data models.TEventBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TEventBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TEventBaits对象
func (e *TEventBaits) Get(d *dto.TEventBaitsGetReq, p *actions.DataPermission, model *models.TEventBaits) error {
	var data models.TEventBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTEventBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TEventBaits对象
func (e *TEventBaits) Insert(c *dto.TEventBaitsInsertReq) error {
    var err error
    var data models.TEventBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TEventBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TEventBaits对象
func (e *TEventBaits) Update(c *dto.TEventBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TEventBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TEventBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TEventBaits
func (e *TEventBaits) Remove(d *dto.TEventBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TEventBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTEventBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}