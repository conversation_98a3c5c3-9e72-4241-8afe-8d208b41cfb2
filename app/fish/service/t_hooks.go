package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type THooks struct {
	service.Service
}

// GetPage 获取THooks列表
func (e *THooks) GetPage(c *dto.THooksGetPageReq, p *actions.DataPermission, list *[]models.THooks, count *int64) error {
	var err error
	var data models.THooks

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("THooksService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取THooks对象
func (e *THooks) Get(d *dto.THooksGetReq, p *actions.DataPermission, model *models.THooks) error {
	var data models.THooks

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTHooks error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建THooks对象
func (e *THooks) Insert(c *dto.THooksInsertReq) error {
    var err error
    var data models.THooks
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("THooksService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改THooks对象
func (e *THooks) Update(c *dto.THooksUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.THooks{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("THooksService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除THooks
func (e *THooks) Remove(d *dto.THooksDeleteReq, p *actions.DataPermission) error {
	var data models.THooks

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTHooks error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}