package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBoiliesPelletsBaits struct {
	service.Service
}

// GetPage 获取TBoiliesPelletsBaits列表
func (e *TBoiliesPelletsBaits) GetPage(c *dto.TBoiliesPelletsBaitsGetPageReq, p *actions.DataPermission, list *[]models.TBoiliesPelletsBaits, count *int64) error {
	var err error
	var data models.TBoiliesPelletsBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TBoiliesPelletsBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBoiliesPelletsBaits对象
func (e *TBoiliesPelletsBaits) Get(d *dto.TBoiliesPelletsBaitsGetReq, p *actions.DataPermission, model *models.TBoiliesPelletsBaits) error {
	var data models.TBoiliesPelletsBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBoiliesPelletsBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TBoiliesPelletsBaits对象
func (e *TBoiliesPelletsBaits) Insert(c *dto.TBoiliesPelletsBaitsInsertReq) error {
    var err error
    var data models.TBoiliesPelletsBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TBoiliesPelletsBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TBoiliesPelletsBaits对象
func (e *TBoiliesPelletsBaits) Update(c *dto.TBoiliesPelletsBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TBoiliesPelletsBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TBoiliesPelletsBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TBoiliesPelletsBaits
func (e *TBoiliesPelletsBaits) Remove(d *dto.TBoiliesPelletsBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TBoiliesPelletsBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTBoiliesPelletsBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}