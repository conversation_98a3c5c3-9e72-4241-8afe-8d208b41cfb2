package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TStringer struct {
	service.Service
}

// GetPage 获取TStringer列表
func (e *TStringer) GetPage(c *dto.TStringerGetPageReq, p *actions.DataPermission, list *[]models.TStringer, count *int64) error {
	var err error
	var data models.TStringer

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TStringerService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TStringer对象
func (e *TStringer) Get(d *dto.TStringerGetReq, p *actions.DataPermission, model *models.TStringer) error {
	var data models.TStringer

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTStringer error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TStringer对象
func (e *TStringer) Insert(c *dto.TStringerInsertReq) error {
    var err error
    var data models.TStringer
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TStringerService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TStringer对象
func (e *TStringer) Update(c *dto.TStringerUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TStringer{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TStringerService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TStringer
func (e *TStringer) Remove(d *dto.TStringerDeleteReq, p *actions.DataPermission) error {
	var data models.TStringer

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTStringer error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}