package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSpinReels struct {
	service.Service
}

// GetPage 获取TSpinReels列表
func (e *TSpinReels) GetPage(c *dto.TSpinReelsGetPageReq, p *actions.DataPermission, list *[]models.TSpinReels, count *int64) error {
	var err error
	var data models.TSpinReels

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TSpinReelsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSpinReels对象
func (e *TSpinReels) Get(d *dto.TSpinReelsGetReq, p *actions.DataPermission, model *models.TSpinReels) error {
	var data models.TSpinReels

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSpinReels error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSpinReels对象
func (e *TSpinReels) Insert(c *dto.TSpinReelsInsertReq) error {
    var err error
    var data models.TSpinReels
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSpinReelsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSpinReels对象
func (e *TSpinReels) Update(c *dto.TSpinReelsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSpinReels{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSpinReelsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSpinReels
func (e *TSpinReels) Remove(d *dto.TSpinReelsDeleteReq, p *actions.DataPermission) error {
	var data models.TSpinReels

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSpinReels error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}