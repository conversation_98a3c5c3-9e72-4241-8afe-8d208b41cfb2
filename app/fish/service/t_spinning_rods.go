package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSpinningRods struct {
	service.Service
}

// GetPage 获取TSpinningRods列表
func (e *TSpinningRods) GetPage(c *dto.TSpinningRodsGetPageReq, p *actions.DataPermission, list *[]models.TSpinningRods, count *int64) error {
	var err error
	var data models.TSpinningRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TSpinningRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSpinningRods对象
func (e *TSpinningRods) Get(d *dto.TSpinningRodsGetReq, p *actions.DataPermission, model *models.TSpinningRods) error {
	var data models.TSpinningRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSpinningRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSpinningRods对象
func (e *TSpinningRods) Insert(c *dto.TSpinningRodsInsertReq) error {
    var err error
    var data models.TSpinningRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSpinningRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSpinningRods对象
func (e *TSpinningRods) Update(c *dto.TSpinningRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSpinningRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSpinningRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSpinningRods
func (e *TSpinningRods) Remove(d *dto.TSpinningRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TSpinningRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSpinningRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}