package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TTackleBoxes struct {
	service.Service
}

// GetPage 获取TTackleBoxes列表
func (e *TTackleBoxes) GetPage(c *dto.TTackleBoxesGetPageReq, p *actions.DataPermission, list *[]models.TTackleBoxes, count *int64) error {
	var err error
	var data models.TTackleBoxes

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TTackleBoxesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TTackleBoxes对象
func (e *TTackleBoxes) Get(d *dto.TTackleBoxesGetReq, p *actions.DataPermission, model *models.TTackleBoxes) error {
	var data models.TTackleBoxes

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTTackleBoxes error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TTackleBoxes对象
func (e *TTackleBoxes) Insert(c *dto.TTackleBoxesInsertReq) error {
    var err error
    var data models.TTackleBoxes
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TTackleBoxesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TTackleBoxes对象
func (e *TTackleBoxes) Update(c *dto.TTackleBoxesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TTackleBoxes{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TTackleBoxesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TTackleBoxes
func (e *TTackleBoxes) Remove(d *dto.TTackleBoxesDeleteReq, p *actions.DataPermission) error {
	var data models.TTackleBoxes

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTTackleBoxes error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}