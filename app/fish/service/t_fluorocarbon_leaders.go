package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFluorocarbonLeaders struct {
	service.Service
}

// GetPage 获取TFluorocarbonLeaders列表
func (e *TFluorocarbonLeaders) GetPage(c *dto.TFluorocarbonLeadersGetPageReq, p *actions.DataPermission, list *[]models.TFluorocarbonLeaders, count *int64) error {
	var err error
	var data models.TFluorocarbonLeaders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFluorocarbonLeadersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFluorocarbonLeaders对象
func (e *TFluorocarbonLeaders) Get(d *dto.TFluorocarbonLeadersGetReq, p *actions.DataPermission, model *models.TFluorocarbonLeaders) error {
	var data models.TFluorocarbonLeaders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFluorocarbonLeaders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFluorocarbonLeaders对象
func (e *TFluorocarbonLeaders) Insert(c *dto.TFluorocarbonLeadersInsertReq) error {
    var err error
    var data models.TFluorocarbonLeaders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFluorocarbonLeadersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFluorocarbonLeaders对象
func (e *TFluorocarbonLeaders) Update(c *dto.TFluorocarbonLeadersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFluorocarbonLeaders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFluorocarbonLeadersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFluorocarbonLeaders
func (e *TFluorocarbonLeaders) Remove(d *dto.TFluorocarbonLeadersDeleteReq, p *actions.DataPermission) error {
	var data models.TFluorocarbonLeaders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFluorocarbonLeaders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}