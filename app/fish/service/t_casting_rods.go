package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCastingRods struct {
	service.Service
}

// GetPage 获取TCastingRods列表
func (e *TCastingRods) GetPage(c *dto.TCastingRodsGetPageReq, p *actions.DataPermission, list *[]models.TCastingRods, count *int64) error {
	var err error
	var data models.TCastingRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TCastingRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCastingRods对象
func (e *TCastingRods) Get(d *dto.TCastingRodsGetReq, p *actions.DataPermission, model *models.TCastingRods) error {
	var data models.TCastingRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCastingRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCastingRods对象
func (e *TCastingRods) Insert(c *dto.TCastingRodsInsertReq) error {
    var err error
    var data models.TCastingRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCastingRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCastingRods对象
func (e *TCastingRods) Update(c *dto.TCastingRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCastingRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCastingRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCastingRods
func (e *TCastingRods) Remove(d *dto.TCastingRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TCastingRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCastingRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}