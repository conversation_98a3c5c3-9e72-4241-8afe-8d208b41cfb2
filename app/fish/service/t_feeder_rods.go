package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFeederRods struct {
	service.Service
}

// GetPage 获取TFeederRods列表
func (e *TFeederRods) GetPage(c *dto.TFeederRodsGetPageReq, p *actions.DataPermission, list *[]models.TFeederRods, count *int64) error {
	var err error
	var data models.TFeederRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TFeederRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFeederRods对象
func (e *TFeederRods) Get(d *dto.TFeederRodsGetReq, p *actions.DataPermission, model *models.TFeederRods) error {
	var data models.TFeederRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFeederRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFeederRods对象
func (e *TFeederRods) Insert(c *dto.TFeederRodsInsertReq) error {
    var err error
    var data models.TFeederRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFeederRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFeederRods对象
func (e *TFeederRods) Update(c *dto.TFeederRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFeederRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFeederRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFeederRods
func (e *TFeederRods) Remove(d *dto.TFeederRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TFeederRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFeederRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}