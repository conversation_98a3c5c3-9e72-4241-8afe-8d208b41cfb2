package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TMatchRods struct {
	service.Service
}

// GetPage 获取TMatchRods列表
func (e *TMatchRods) GetPage(c *dto.TMatchRodsGetPageReq, p *actions.DataPermission, list *[]models.TMatchRods, count *int64) error {
	var err error
	var data models.TMatchRods

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TMatchRodsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TMatchRods对象
func (e *TMatchRods) Get(d *dto.TMatchRodsGetReq, p *actions.DataPermission, model *models.TMatchRods) error {
	var data models.TMatchRods

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTMatchRods error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TMatchRods对象
func (e *TMatchRods) Insert(c *dto.TMatchRodsInsertReq) error {
    var err error
    var data models.TMatchRods
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMatchRodsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TMatchRods对象
func (e *TMatchRods) Update(c *dto.TMatchRodsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TMatchRods{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TMatchRodsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TMatchRods
func (e *TMatchRods) Remove(d *dto.TMatchRodsDeleteReq, p *actions.DataPermission) error {
	var data models.TMatchRods

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTMatchRods error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}