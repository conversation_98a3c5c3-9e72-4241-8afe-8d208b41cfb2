package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TPlugs struct {
	service.Service
}

// GetPage 获取TPlugs列表
func (e *TPlugs) GetPage(c *dto.TPlugsGetPageReq, p *actions.DataPermission, list *[]models.TPlugs, count *int64) error {
	var err error
	var data models.TPlugs

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TPlugsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TPlugs对象
func (e *TPlugs) Get(d *dto.TPlugsGetReq, p *actions.DataPermission, model *models.TPlugs) error {
	var data models.TPlugs

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTPlugs error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TPlugs对象
func (e *TPlugs) Insert(c *dto.TPlugsInsertReq) error {
    var err error
    var data models.TPlugs
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TPlugsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TPlugs对象
func (e *TPlugs) Update(c *dto.TPlugsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TPlugs{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TPlugsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TPlugs
func (e *TPlugs) Remove(d *dto.TPlugsDeleteReq, p *actions.DataPermission) error {
	var data models.TPlugs

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTPlugs error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}