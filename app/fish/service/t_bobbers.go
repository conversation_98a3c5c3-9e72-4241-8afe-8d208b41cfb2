package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBobbers struct {
	service.Service
}

// GetPage 获取TBobbers列表
func (e *TBobbers) GetPage(c *dto.TBobbersGetPageReq, p *actions.DataPermission, list *[]models.TBobbers, count *int64) error {
	var err error
	var data models.TBobbers

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TBobbersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBobbers对象
func (e *TBobbers) Get(d *dto.TBobbersGetReq, p *actions.DataPermission, model *models.TBobbers) error {
	var data models.TBobbers

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBobbers error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TBobbers对象
func (e *TBobbers) Insert(c *dto.TBobbersInsertReq) error {
    var err error
    var data models.TBobbers
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TBobbersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TBobbers对象
func (e *TBobbers) Update(c *dto.TBobbersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TBobbers{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TBobbersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TBobbers
func (e *TBobbers) Remove(d *dto.TBobbersDeleteReq, p *actions.DataPermission) error {
	var data models.TBobbers

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTBobbers error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}