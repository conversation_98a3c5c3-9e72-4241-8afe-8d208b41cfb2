package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TPlugsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_plugs" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_plugs" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_plugs" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_plugs" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_plugs" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_plugs" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_plugs" comment:"名称英文"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_plugs" comment:"颜色"`
    Bouyancy string `form:"bouyancy"  search:"type:contains;column:bouyancy;table:t_plugs" comment:"浮力"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_plugs" comment:"重量(g)"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_plugs" comment:"长度(cm)"`
    HookSize string `form:"hookSize"  search:"type:contains;column:hook_size;table:t_plugs" comment:"鱼钩"`
    Depth string `form:"depth"  search:"type:contains;column:depth;table:t_plugs" comment:"深度(m)"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_plugs" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_plugs" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_plugs" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_plugs" comment:"俱乐部币"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_plugs" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_plugs" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_plugs" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_plugs" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_plugs" comment:"创建者名称"`
    TPlugsOrder
}

type TPlugsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_plugs"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_plugs"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_plugs"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_plugs"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_plugs"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_plugs"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_plugs"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_plugs"`
    Bouyancy string `form:"bouyancyOrder"  search:"type:order;column:bouyancy;table:t_plugs"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_plugs"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_plugs"`
    HookSize string `form:"hookSizeOrder"  search:"type:order;column:hook_size;table:t_plugs"`
    Depth string `form:"depthOrder"  search:"type:order;column:depth;table:t_plugs"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_plugs"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_plugs"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_plugs"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_plugs"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_plugs"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_plugs"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_plugs"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_plugs"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_plugs"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_plugs"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_plugs"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_plugs"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_plugs"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_plugs"`
    
}

func (m *TPlugsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TPlugsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Color string `json:"color" comment:"颜色"`
    Bouyancy string `json:"bouyancy" comment:"浮力"`
    Weight string `json:"weight" comment:"重量(g)"`
    Length string `json:"length" comment:"长度(cm)"`
    HookSize string `json:"hookSize" comment:"鱼钩"`
    Depth string `json:"depth" comment:"深度(m)"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TPlugsInsertReq) Generate(model *models.TPlugs)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Color = s.Color
    model.Bouyancy = s.Bouyancy
    model.Weight = s.Weight
    model.Length = s.Length
    model.HookSize = s.HookSize
    model.Depth = s.Depth
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TPlugsInsertReq) GetId() interface{} {
	return s.Id
}

type TPlugsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Color string `json:"color" comment:"颜色"`
    Bouyancy string `json:"bouyancy" comment:"浮力"`
    Weight string `json:"weight" comment:"重量(g)"`
    Length string `json:"length" comment:"长度(cm)"`
    HookSize string `json:"hookSize" comment:"鱼钩"`
    Depth string `json:"depth" comment:"深度(m)"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TPlugsUpdateReq) Generate(model *models.TPlugs)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Color = s.Color
    model.Bouyancy = s.Bouyancy
    model.Weight = s.Weight
    model.Length = s.Length
    model.HookSize = s.HookSize
    model.Depth = s.Depth
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TPlugsUpdateReq) GetId() interface{} {
	return s.Id
}

// TPlugsGetReq 功能获取请求参数
type TPlugsGetReq struct {
     Id int `uri:"id"`
}
func (s *TPlugsGetReq) GetId() interface{} {
	return s.Id
}

// TPlugsDeleteReq 功能删除请求参数
type TPlugsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TPlugsDeleteReq) GetId() interface{} {
	return s.Ids
}