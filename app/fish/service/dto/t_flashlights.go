package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TFlashlightsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_flashlights" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_flashlights" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_flashlights" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_flashlights" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_flashlights" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_flashlights" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_flashlights" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_flashlights" comment:"品牌"`
    LightingRange string `form:"lightingRange"  search:"type:contains;column:lighting_range;table:t_flashlights" comment:"照明范围"`
    LightingSpotAngle string `form:"lightingSpotAngle"  search:"type:contains;column:lighting_spot_angle;table:t_flashlights" comment:"照明角度"`
    LightingTone string `form:"lightingTone"  search:"type:contains;column:lighting_tone;table:t_flashlights" comment:"色调"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_flashlights" comment:"货币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_flashlights" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_flashlights" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_flashlights" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_flashlights" comment:"创建者名称"`
    TFlashlightsOrder
}

type TFlashlightsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_flashlights"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_flashlights"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_flashlights"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_flashlights"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_flashlights"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_flashlights"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_flashlights"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_flashlights"`
    LightingRange string `form:"lightingRangeOrder"  search:"type:order;column:lighting_range;table:t_flashlights"`
    LightingSpotAngle string `form:"lightingSpotAngleOrder"  search:"type:order;column:lighting_spot_angle;table:t_flashlights"`
    LightingTone string `form:"lightingToneOrder"  search:"type:order;column:lighting_tone;table:t_flashlights"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_flashlights"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_flashlights"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_flashlights"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_flashlights"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_flashlights"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_flashlights"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_flashlights"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_flashlights"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_flashlights"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_flashlights"`
    
}

func (m *TFlashlightsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFlashlightsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    LightingRange string `json:"lightingRange" comment:"照明范围"`
    LightingSpotAngle string `json:"lightingSpotAngle" comment:"照明角度"`
    LightingTone string `json:"lightingTone" comment:"色调"`
    Money string `json:"money" comment:"货币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFlashlightsInsertReq) Generate(model *models.TFlashlights)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.LightingRange = s.LightingRange
    model.LightingSpotAngle = s.LightingSpotAngle
    model.LightingTone = s.LightingTone
    model.Money = s.Money
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFlashlightsInsertReq) GetId() interface{} {
	return s.Id
}

type TFlashlightsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    LightingRange string `json:"lightingRange" comment:"照明范围"`
    LightingSpotAngle string `json:"lightingSpotAngle" comment:"照明角度"`
    LightingTone string `json:"lightingTone" comment:"色调"`
    Money string `json:"money" comment:"货币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFlashlightsUpdateReq) Generate(model *models.TFlashlights)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.LightingRange = s.LightingRange
    model.LightingSpotAngle = s.LightingSpotAngle
    model.LightingTone = s.LightingTone
    model.Money = s.Money
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TFlashlightsUpdateReq) GetId() interface{} {
	return s.Id
}

// TFlashlightsGetReq 功能获取请求参数
type TFlashlightsGetReq struct {
     Id int `uri:"id"`
}
func (s *TFlashlightsGetReq) GetId() interface{} {
	return s.Id
}

// TFlashlightsDeleteReq 功能删除请求参数
type TFlashlightsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFlashlightsDeleteReq) GetId() interface{} {
	return s.Ids
}