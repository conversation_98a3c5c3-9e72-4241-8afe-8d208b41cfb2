package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TJigHeadsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_jig_heads" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_jig_heads" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_jig_heads" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_jig_heads" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_jig_heads" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_jig_heads" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_jig_heads" comment:"名称英文"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_jig_heads" comment:"重量（g）"`
    Hook string `form:"hook"  search:"type:contains;column:hook;table:t_jig_heads" comment:"鱼钩"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_jig_heads" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_jig_heads" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_jig_heads" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_jig_heads" comment:"俱乐部币"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_jig_heads" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_jig_heads" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_jig_heads" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_jig_heads" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_jig_heads" comment:"创建者名称"`
    TJigHeadsOrder
}

type TJigHeadsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_jig_heads"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_jig_heads"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_jig_heads"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_jig_heads"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_jig_heads"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_jig_heads"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_jig_heads"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_jig_heads"`
    Hook string `form:"hookOrder"  search:"type:order;column:hook;table:t_jig_heads"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_jig_heads"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_jig_heads"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_jig_heads"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_jig_heads"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_jig_heads"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_jig_heads"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_jig_heads"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_jig_heads"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_jig_heads"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_jig_heads"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_jig_heads"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_jig_heads"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_jig_heads"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_jig_heads"`
    
}

func (m *TJigHeadsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TJigHeadsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Weight string `json:"weight" comment:"重量（g）"`
    Hook string `json:"hook" comment:"鱼钩"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TJigHeadsInsertReq) Generate(model *models.TJigHeads)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Weight = s.Weight
    model.Hook = s.Hook
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TJigHeadsInsertReq) GetId() interface{} {
	return s.Id
}

type TJigHeadsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Weight string `json:"weight" comment:"重量（g）"`
    Hook string `json:"hook" comment:"鱼钩"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TJigHeadsUpdateReq) Generate(model *models.TJigHeads)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Weight = s.Weight
    model.Hook = s.Hook
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TJigHeadsUpdateReq) GetId() interface{} {
	return s.Id
}

// TJigHeadsGetReq 功能获取请求参数
type TJigHeadsGetReq struct {
     Id int `uri:"id"`
}
func (s *TJigHeadsGetReq) GetId() interface{} {
	return s.Id
}

// TJigHeadsDeleteReq 功能删除请求参数
type TJigHeadsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TJigHeadsDeleteReq) GetId() interface{} {
	return s.Ids
}