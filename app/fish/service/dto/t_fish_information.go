package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TFishInformationGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_fish_information" comment:"主键id"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_fish_information" comment:"英文名称"`
    CnName string `form:"cnName"  search:"type:contains;column:cn_name;table:t_fish_information" comment:"中文名称"`
    Species string `form:"species"  search:"type:contains;column:species;table:t_fish_information" comment:"种类"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_fish_information" comment:"图片地址"`
    WikiImageUrl string `form:"wikiImageUrl"  search:"type:contains;column:wiki_image_url;table:t_fish_information" comment:"wiki图片地址"`
    Rule string `form:"rule"  search:"type:contains;column:rule;table:t_fish_information" comment:"规则"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_fish_information" comment:"重量(max)"`
    Reward string `form:"reward"  search:"type:contains;column:reward;table:t_fish_information" comment:"单价(kg)"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_fish_information" comment:"长度(cm)"`
    Baits string `form:"baits"  search:"type:contains;column:baits;table:t_fish_information" comment:"诱饵"`
    CnBaits string `form:"cnBaits"  search:"type:contains;column:cn_baits;table:t_fish_information" comment:"诱饵翻译"`
    Lures string `form:"lures"  search:"type:contains;column:lures;table:t_fish_information" comment:"假饵"`
    CnLures string `form:"cnLures"  search:"type:contains;column:cn_lures;table:t_fish_information" comment:"假饵翻译"`
    Haunt string `form:"haunt"  search:"type:contains;column:haunt;table:t_fish_information" comment:"出没"`
    Scene string `form:"scene"  search:"type:contains;column:scene;table:t_fish_information" comment:"场景"`
    Area string `form:"area"  search:"type:contains;column:area;table:t_fish_information" comment:"区域"`
    Water string `form:"water"  search:"type:contains;column:water;table:t_fish_information" comment:"水域"`
    Food string `form:"food"  search:"type:contains;column:food;table:t_fish_information" comment:"食物"`
    Feature string `form:"feature"  search:"type:contains;column:feature;table:t_fish_information" comment:"特征"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_fish_information" comment:"创建者名称"`
    TFishInformationOrder
}

type TFishInformationOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_fish_information"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_fish_information"`
    CnName string `form:"cnNameOrder"  search:"type:order;column:cn_name;table:t_fish_information"`
    Species string `form:"speciesOrder"  search:"type:order;column:species;table:t_fish_information"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_fish_information"`
    WikiImageUrl string `form:"wikiImageUrlOrder"  search:"type:order;column:wiki_image_url;table:t_fish_information"`
    Rule string `form:"ruleOrder"  search:"type:order;column:rule;table:t_fish_information"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_fish_information"`
    Reward string `form:"rewardOrder"  search:"type:order;column:reward;table:t_fish_information"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_fish_information"`
    Baits string `form:"baitsOrder"  search:"type:order;column:baits;table:t_fish_information"`
    CnBaits string `form:"cnBaitsOrder"  search:"type:order;column:cn_baits;table:t_fish_information"`
    Lures string `form:"luresOrder"  search:"type:order;column:lures;table:t_fish_information"`
    CnLures string `form:"cnLuresOrder"  search:"type:order;column:cn_lures;table:t_fish_information"`
    Haunt string `form:"hauntOrder"  search:"type:order;column:haunt;table:t_fish_information"`
    Scene string `form:"sceneOrder"  search:"type:order;column:scene;table:t_fish_information"`
    Area string `form:"areaOrder"  search:"type:order;column:area;table:t_fish_information"`
    Water string `form:"waterOrder"  search:"type:order;column:water;table:t_fish_information"`
    Food string `form:"foodOrder"  search:"type:order;column:food;table:t_fish_information"`
    Feature string `form:"featureOrder"  search:"type:order;column:feature;table:t_fish_information"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_fish_information"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_fish_information"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_fish_information"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_fish_information"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_fish_information"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_fish_information"`
    
}

func (m *TFishInformationGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFishInformationInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    EnName string `json:"enName" comment:"英文名称"`
    CnName string `json:"cnName" comment:"中文名称"`
    Species string `json:"species" comment:"种类"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    WikiImageUrl string `json:"wikiImageUrl" comment:"wiki图片地址"`
    Rule string `json:"rule" comment:"规则"`
    Weight string `json:"weight" comment:"重量(max)"`
    Reward string `json:"reward" comment:"单价(kg)"`
    Length string `json:"length" comment:"长度(cm)"`
    Baits string `json:"baits" comment:"诱饵"`
    CnBaits string `json:"cnBaits" comment:"诱饵翻译"`
    Lures string `json:"lures" comment:"假饵"`
    CnLures string `json:"cnLures" comment:"假饵翻译"`
    Haunt string `json:"haunt" comment:"出没"`
    Scene string `json:"scene" comment:"场景"`
    Area string `json:"area" comment:"区域"`
    Water string `json:"water" comment:"水域"`
    Food string `json:"food" comment:"食物"`
    Feature string `json:"feature" comment:"特征"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFishInformationInsertReq) Generate(model *models.TFishInformation)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.EnName = s.EnName
    model.CnName = s.CnName
    model.Species = s.Species
    model.ImageUrl = s.ImageUrl
    model.WikiImageUrl = s.WikiImageUrl
    model.Rule = s.Rule
    model.Weight = s.Weight
    model.Reward = s.Reward
    model.Length = s.Length
    model.Baits = s.Baits
    model.CnBaits = s.CnBaits
    model.Lures = s.Lures
    model.CnLures = s.CnLures
    model.Haunt = s.Haunt
    model.Scene = s.Scene
    model.Area = s.Area
    model.Water = s.Water
    model.Food = s.Food
    model.Feature = s.Feature
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFishInformationInsertReq) GetId() interface{} {
	return s.Id
}

type TFishInformationUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    EnName string `json:"enName" comment:"英文名称"`
    CnName string `json:"cnName" comment:"中文名称"`
    Species string `json:"species" comment:"种类"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    WikiImageUrl string `json:"wikiImageUrl" comment:"wiki图片地址"`
    Rule string `json:"rule" comment:"规则"`
    Weight string `json:"weight" comment:"重量(max)"`
    Reward string `json:"reward" comment:"单价(kg)"`
    Length string `json:"length" comment:"长度(cm)"`
    Baits string `json:"baits" comment:"诱饵"`
    CnBaits string `json:"cnBaits" comment:"诱饵翻译"`
    Lures string `json:"lures" comment:"假饵"`
    CnLures string `json:"cnLures" comment:"假饵翻译"`
    Haunt string `json:"haunt" comment:"出没"`
    Scene string `json:"scene" comment:"场景"`
    Area string `json:"area" comment:"区域"`
    Water string `json:"water" comment:"水域"`
    Food string `json:"food" comment:"食物"`
    Feature string `json:"feature" comment:"特征"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFishInformationUpdateReq) Generate(model *models.TFishInformation)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.EnName = s.EnName
    model.CnName = s.CnName
    model.Species = s.Species
    model.ImageUrl = s.ImageUrl
    model.WikiImageUrl = s.WikiImageUrl
    model.Rule = s.Rule
    model.Weight = s.Weight
    model.Reward = s.Reward
    model.Length = s.Length
    model.Baits = s.Baits
    model.CnBaits = s.CnBaits
    model.Lures = s.Lures
    model.CnLures = s.CnLures
    model.Haunt = s.Haunt
    model.Scene = s.Scene
    model.Area = s.Area
    model.Water = s.Water
    model.Food = s.Food
    model.Feature = s.Feature
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TFishInformationUpdateReq) GetId() interface{} {
	return s.Id
}

// TFishInformationGetReq 功能获取请求参数
type TFishInformationGetReq struct {
     Id int `uri:"id"`
}
func (s *TFishInformationGetReq) GetId() interface{} {
	return s.Id
}

// TFishInformationDeleteReq 功能删除请求参数
type TFishInformationDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFishInformationDeleteReq) GetId() interface{} {
	return s.Ids
}