package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TFluorocarbonLinesGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_fluorocarbon_lines" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_fluorocarbon_lines" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_fluorocarbon_lines" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_fluorocarbon_lines" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_fluorocarbon_lines" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_fluorocarbon_lines" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_fluorocarbon_lines" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_fluorocarbon_lines" comment:"品牌"`
    Thickness string `form:"thickness"  search:"type:contains;column:thickness;table:t_fluorocarbon_lines" comment:"直径（mm）"`
    TestWeight string `form:"testWeight"  search:"type:contains;column:test_weight;table:t_fluorocarbon_lines" comment:"测试重量（kg）"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_fluorocarbon_lines" comment:"颜色"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_fluorocarbon_lines" comment:"长度（m）"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_fluorocarbon_lines" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_fluorocarbon_lines" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_fluorocarbon_lines" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_fluorocarbon_lines" comment:"解锁等级"`
    Use string `form:"use"  search:"type:contains;column:use;table:t_fluorocarbon_lines" comment:"使用"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_fluorocarbon_lines" comment:"描述翻译"`
    Remark string `form:"remark"  search:"type:contains;column:remark;table:t_fluorocarbon_lines" comment:"备注"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_fluorocarbon_lines" comment:"创建者名称"`
    TFluorocarbonLinesOrder
}

type TFluorocarbonLinesOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_fluorocarbon_lines"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_fluorocarbon_lines"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_fluorocarbon_lines"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_fluorocarbon_lines"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_fluorocarbon_lines"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_fluorocarbon_lines"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_fluorocarbon_lines"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_fluorocarbon_lines"`
    Thickness string `form:"thicknessOrder"  search:"type:order;column:thickness;table:t_fluorocarbon_lines"`
    TestWeight string `form:"testWeightOrder"  search:"type:order;column:test_weight;table:t_fluorocarbon_lines"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_fluorocarbon_lines"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_fluorocarbon_lines"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_fluorocarbon_lines"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_fluorocarbon_lines"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_fluorocarbon_lines"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_fluorocarbon_lines"`
    Use string `form:"useOrder"  search:"type:order;column:use;table:t_fluorocarbon_lines"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_fluorocarbon_lines"`
    Remark string `form:"remarkOrder"  search:"type:order;column:remark;table:t_fluorocarbon_lines"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_fluorocarbon_lines"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_fluorocarbon_lines"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_fluorocarbon_lines"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_fluorocarbon_lines"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_fluorocarbon_lines"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_fluorocarbon_lines"`
    
}

func (m *TFluorocarbonLinesGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFluorocarbonLinesInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Thickness string `json:"thickness" comment:"直径（mm）"`
    TestWeight string `json:"testWeight" comment:"测试重量（kg）"`
    Color string `json:"color" comment:"颜色"`
    Length string `json:"length" comment:"长度（m）"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Use string `json:"use" comment:"使用"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFluorocarbonLinesInsertReq) Generate(model *models.TFluorocarbonLines)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Thickness = s.Thickness
    model.TestWeight = s.TestWeight
    model.Color = s.Color
    model.Length = s.Length
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Use = s.Use
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFluorocarbonLinesInsertReq) GetId() interface{} {
	return s.Id
}

type TFluorocarbonLinesUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Thickness string `json:"thickness" comment:"直径（mm）"`
    TestWeight string `json:"testWeight" comment:"测试重量（kg）"`
    Color string `json:"color" comment:"颜色"`
    Length string `json:"length" comment:"长度（m）"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Use string `json:"use" comment:"使用"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFluorocarbonLinesUpdateReq) Generate(model *models.TFluorocarbonLines)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Thickness = s.Thickness
    model.TestWeight = s.TestWeight
    model.Color = s.Color
    model.Length = s.Length
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Use = s.Use
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TFluorocarbonLinesUpdateReq) GetId() interface{} {
	return s.Id
}

// TFluorocarbonLinesGetReq 功能获取请求参数
type TFluorocarbonLinesGetReq struct {
     Id int `uri:"id"`
}
func (s *TFluorocarbonLinesGetReq) GetId() interface{} {
	return s.Id
}

// TFluorocarbonLinesDeleteReq 功能删除请求参数
type TFluorocarbonLinesDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFluorocarbonLinesDeleteReq) GetId() interface{} {
	return s.Ids
}