package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TTelescopicRodsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_telescopic_rods" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_telescopic_rods" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_telescopic_rods" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_telescopic_rods" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_telescopic_rods" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_telescopic_rods" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_telescopic_rods" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_telescopic_rods" comment:"品牌"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_telescopic_rods" comment:"长度(m)"`
    LineWeight string `form:"lineWeight"  search:"type:contains;column:line_weight;table:t_telescopic_rods" comment:"钓重（kg）"`
    Action string `form:"action"  search:"type:contains;column:action;table:t_telescopic_rods" comment:"调性"`
    Pieces string `form:"pieces"  search:"type:contains;column:pieces;table:t_telescopic_rods" comment:"节数"`
    Guides string `form:"guides"  search:"type:contains;column:guides;table:t_telescopic_rods" comment:"导环线"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_telescopic_rods" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_telescopic_rods" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_telescopic_rods" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_telescopic_rods" comment:"俱乐部币"`
    Technology string `form:"technology"  search:"type:contains;column:technology;table:t_telescopic_rods" comment:"技术"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_telescopic_rods" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_telescopic_rods" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_telescopic_rods" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_telescopic_rods" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_telescopic_rods" comment:"创建者名称"`
    TTelescopicRodsOrder
}

type TTelescopicRodsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_telescopic_rods"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_telescopic_rods"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_telescopic_rods"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_telescopic_rods"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_telescopic_rods"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_telescopic_rods"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_telescopic_rods"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_telescopic_rods"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_telescopic_rods"`
    LineWeight string `form:"lineWeightOrder"  search:"type:order;column:line_weight;table:t_telescopic_rods"`
    Action string `form:"actionOrder"  search:"type:order;column:action;table:t_telescopic_rods"`
    Pieces string `form:"piecesOrder"  search:"type:order;column:pieces;table:t_telescopic_rods"`
    Guides string `form:"guidesOrder"  search:"type:order;column:guides;table:t_telescopic_rods"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_telescopic_rods"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_telescopic_rods"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_telescopic_rods"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_telescopic_rods"`
    Technology string `form:"technologyOrder"  search:"type:order;column:technology;table:t_telescopic_rods"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_telescopic_rods"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_telescopic_rods"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_telescopic_rods"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_telescopic_rods"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_telescopic_rods"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_telescopic_rods"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_telescopic_rods"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_telescopic_rods"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_telescopic_rods"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_telescopic_rods"`
    
}

func (m *TTelescopicRodsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TTelescopicRodsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Length string `json:"length" comment:"长度(m)"`
    LineWeight string `json:"lineWeight" comment:"钓重（kg）"`
    Action string `json:"action" comment:"调性"`
    Pieces string `json:"pieces" comment:"节数"`
    Guides string `json:"guides" comment:"导环线"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TTelescopicRodsInsertReq) Generate(model *models.TTelescopicRods)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Length = s.Length
    model.LineWeight = s.LineWeight
    model.Action = s.Action
    model.Pieces = s.Pieces
    model.Guides = s.Guides
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TTelescopicRodsInsertReq) GetId() interface{} {
	return s.Id
}

type TTelescopicRodsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Length string `json:"length" comment:"长度(m)"`
    LineWeight string `json:"lineWeight" comment:"钓重（kg）"`
    Action string `json:"action" comment:"调性"`
    Pieces string `json:"pieces" comment:"节数"`
    Guides string `json:"guides" comment:"导环线"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TTelescopicRodsUpdateReq) Generate(model *models.TTelescopicRods)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Length = s.Length
    model.LineWeight = s.LineWeight
    model.Action = s.Action
    model.Pieces = s.Pieces
    model.Guides = s.Guides
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TTelescopicRodsUpdateReq) GetId() interface{} {
	return s.Id
}

// TTelescopicRodsGetReq 功能获取请求参数
type TTelescopicRodsGetReq struct {
     Id int `uri:"id"`
}
func (s *TTelescopicRodsGetReq) GetId() interface{} {
	return s.Id
}

// TTelescopicRodsDeleteReq 功能删除请求参数
type TTelescopicRodsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TTelescopicRodsDeleteReq) GetId() interface{} {
	return s.Ids
}