package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TStringerGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_stringer" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_stringer" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_stringer" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_stringer" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_stringer" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_stringer" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_stringer" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_stringer" comment:"品牌"`
    MaxSingleFish string `form:"maxSingleFish"  search:"type:contains;column:max_single_fish;table:t_stringer" comment:"单条鱼最大重量（kg）"`
    MaxTotalFish string `form:"maxTotalFish"  search:"type:contains;column:max_total_fish;table:t_stringer" comment:"鱼总重量（kg）"`
    FishFriendly string `form:"fishFriendly"  search:"type:contains;column:fish_friendly;table:t_stringer" comment:"对鱼是否有害"`
    Durability string `form:"durability"  search:"type:contains;column:durability;table:t_stringer" comment:"耐用性"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_stringer" comment:"材质"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_stringer" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_stringer" comment:"货币"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_stringer" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_stringer" comment:"描述中文"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_stringer" comment:"创建者名称"`
    TStringerOrder
}

type TStringerOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_stringer"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_stringer"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_stringer"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_stringer"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_stringer"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_stringer"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_stringer"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_stringer"`
    MaxSingleFish string `form:"maxSingleFishOrder"  search:"type:order;column:max_single_fish;table:t_stringer"`
    MaxTotalFish string `form:"maxTotalFishOrder"  search:"type:order;column:max_total_fish;table:t_stringer"`
    FishFriendly string `form:"fishFriendlyOrder"  search:"type:order;column:fish_friendly;table:t_stringer"`
    Durability string `form:"durabilityOrder"  search:"type:order;column:durability;table:t_stringer"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_stringer"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_stringer"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_stringer"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_stringer"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_stringer"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_stringer"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_stringer"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_stringer"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_stringer"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_stringer"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_stringer"`
    
}

func (m *TStringerGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TStringerInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    MaxSingleFish string `json:"maxSingleFish" comment:"单条鱼最大重量（kg）"`
    MaxTotalFish string `json:"maxTotalFish" comment:"鱼总重量（kg）"`
    FishFriendly string `json:"fishFriendly" comment:"对鱼是否有害"`
    Durability string `json:"durability" comment:"耐用性"`
    Material string `json:"material" comment:"材质"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述中文"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TStringerInsertReq) Generate(model *models.TStringer)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.MaxSingleFish = s.MaxSingleFish
    model.MaxTotalFish = s.MaxTotalFish
    model.FishFriendly = s.FishFriendly
    model.Durability = s.Durability
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TStringerInsertReq) GetId() interface{} {
	return s.Id
}

type TStringerUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    MaxSingleFish string `json:"maxSingleFish" comment:"单条鱼最大重量（kg）"`
    MaxTotalFish string `json:"maxTotalFish" comment:"鱼总重量（kg）"`
    FishFriendly string `json:"fishFriendly" comment:"对鱼是否有害"`
    Durability string `json:"durability" comment:"耐用性"`
    Material string `json:"material" comment:"材质"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述中文"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TStringerUpdateReq) Generate(model *models.TStringer)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.MaxSingleFish = s.MaxSingleFish
    model.MaxTotalFish = s.MaxTotalFish
    model.FishFriendly = s.FishFriendly
    model.Durability = s.Durability
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TStringerUpdateReq) GetId() interface{} {
	return s.Id
}

// TStringerGetReq 功能获取请求参数
type TStringerGetReq struct {
     Id int `uri:"id"`
}
func (s *TStringerGetReq) GetId() interface{} {
	return s.Id
}

// TStringerDeleteReq 功能删除请求参数
type TStringerDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TStringerDeleteReq) GetId() interface{} {
	return s.Ids
}