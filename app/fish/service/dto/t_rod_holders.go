package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TRodHoldersGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_rod_holders" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_rod_holders" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_rod_holders" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_rod_holders" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_rod_holders" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_rod_holders" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_rod_holders" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_rod_holders" comment:"品牌"`
    RodSlot string `form:"rodSlot"  search:"type:contains;column:rod_slot;table:t_rod_holders" comment:"竿架槽"`
    StandCount string `form:"standCount"  search:"type:contains;column:stand_count;table:t_rod_holders" comment:"竿架"`
    BiteAlarm string `form:"biteAlarm"  search:"type:contains;column:bite_alarm;table:t_rod_holders" comment:"咬钩报警器"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_rod_holders" comment:"重量（千克）"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_rod_holders" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_rod_holders" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_rod_holders" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_rod_holders" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_rod_holders" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_rod_holders" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_rod_holders" comment:"创建者名称"`
    TRodHoldersOrder
}

type TRodHoldersOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_rod_holders"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_rod_holders"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_rod_holders"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_rod_holders"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_rod_holders"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_rod_holders"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_rod_holders"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_rod_holders"`
    RodSlot string `form:"rodSlotOrder"  search:"type:order;column:rod_slot;table:t_rod_holders"`
    StandCount string `form:"standCountOrder"  search:"type:order;column:stand_count;table:t_rod_holders"`
    BiteAlarm string `form:"biteAlarmOrder"  search:"type:order;column:bite_alarm;table:t_rod_holders"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_rod_holders"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_rod_holders"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_rod_holders"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_rod_holders"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_rod_holders"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_rod_holders"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_rod_holders"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_rod_holders"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_rod_holders"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_rod_holders"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_rod_holders"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_rod_holders"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_rod_holders"`
    
}

func (m *TRodHoldersGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TRodHoldersInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    RodSlot string `json:"rodSlot" comment:"竿架槽"`
    StandCount string `json:"standCount" comment:"竿架"`
    BiteAlarm string `json:"biteAlarm" comment:"咬钩报警器"`
    Weight string `json:"weight" comment:"重量（千克）"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TRodHoldersInsertReq) Generate(model *models.TRodHolders)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.RodSlot = s.RodSlot
    model.StandCount = s.StandCount
    model.BiteAlarm = s.BiteAlarm
    model.Weight = s.Weight
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TRodHoldersInsertReq) GetId() interface{} {
	return s.Id
}

type TRodHoldersUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    RodSlot string `json:"rodSlot" comment:"竿架槽"`
    StandCount string `json:"standCount" comment:"竿架"`
    BiteAlarm string `json:"biteAlarm" comment:"咬钩报警器"`
    Weight string `json:"weight" comment:"重量（千克）"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TRodHoldersUpdateReq) Generate(model *models.TRodHolders)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.RodSlot = s.RodSlot
    model.StandCount = s.StandCount
    model.BiteAlarm = s.BiteAlarm
    model.Weight = s.Weight
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TRodHoldersUpdateReq) GetId() interface{} {
	return s.Id
}

// TRodHoldersGetReq 功能获取请求参数
type TRodHoldersGetReq struct {
     Id int `uri:"id"`
}
func (s *TRodHoldersGetReq) GetId() interface{} {
	return s.Id
}

// TRodHoldersDeleteReq 功能删除请求参数
type TRodHoldersDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TRodHoldersDeleteReq) GetId() interface{} {
	return s.Ids
}