package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TAlarmGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_alarm" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_alarm" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_alarm" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_alarm" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_alarm" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_alarm" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_alarm" comment:"名称英文"`
    Sensitivity string `form:"sensitivity"  search:"type:contains;column:sensitivity;table:t_alarm" comment:"敏感度"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_alarm" comment:"材料"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_alarm" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_alarm" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_alarm" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_alarm" comment:"俱乐部币"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_alarm" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_alarm" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_alarm" comment:"创建者名称"`
    TAlarmOrder
}

type TAlarmOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_alarm"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_alarm"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_alarm"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_alarm"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_alarm"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_alarm"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_alarm"`
    Sensitivity string `form:"sensitivityOrder"  search:"type:order;column:sensitivity;table:t_alarm"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_alarm"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_alarm"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_alarm"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_alarm"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_alarm"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_alarm"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_alarm"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_alarm"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_alarm"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_alarm"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_alarm"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_alarm"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_alarm"`
    
}

func (m *TAlarmGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TAlarmInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Sensitivity string `json:"sensitivity" comment:"敏感度"`
    Material string `json:"material" comment:"材料"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TAlarmInsertReq) Generate(model *models.TAlarm)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Sensitivity = s.Sensitivity
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TAlarmInsertReq) GetId() interface{} {
	return s.Id
}

type TAlarmUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Sensitivity string `json:"sensitivity" comment:"敏感度"`
    Material string `json:"material" comment:"材料"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TAlarmUpdateReq) Generate(model *models.TAlarm)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Sensitivity = s.Sensitivity
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TAlarmUpdateReq) GetId() interface{} {
	return s.Id
}

// TAlarmGetReq 功能获取请求参数
type TAlarmGetReq struct {
     Id int `uri:"id"`
}
func (s *TAlarmGetReq) GetId() interface{} {
	return s.Id
}

// TAlarmDeleteReq 功能删除请求参数
type TAlarmDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TAlarmDeleteReq) GetId() interface{} {
	return s.Ids
}