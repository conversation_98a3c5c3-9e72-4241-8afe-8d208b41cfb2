package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TSceneGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_scene" comment:"主键id"`
    Scene string `form:"scene"  search:"type:contains;column:scene;table:t_scene" comment:"场景"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_scene" comment:"解锁等级"`
    TravelFee string `form:"travelFee"  search:"type:contains;column:travel_fee;table:t_scene" comment:"旅行费"`
    FishingFee string `form:"fishingFee"  search:"type:contains;column:fishing_fee;table:t_scene" comment:"钓鱼费(per_day)"`
    BasicLicenseReleased string `form:"basicLicenseReleased"  search:"type:contains;column:basic_license_released;table:t_scene" comment:"基本执照"`
    AdvancedLicenseReleased string `form:"advancedLicenseReleased"  search:"type:contains;column:advanced_license_released;table:t_scene" comment:"高级执照"`
    EnFish string `form:"enFish"  search:"type:contains;column:en_fish;table:t_scene" comment:"鱼英文"`
    CnFish string `form:"cnFish"  search:"type:contains;column:cn_fish;table:t_scene" comment:"鱼中文"`
    SpeciesRule string `form:"speciesRule"  search:"type:contains;column:species_rule;table:t_scene" comment:"鱼规则"`
    GameList string `form:"gameList"  search:"type:contains;column:game_list;table:t_scene" comment:"比赛列表"`
    SceneUrl string `form:"sceneUrl"  search:"type:contains;column:scene_url;table:t_scene" comment:"场景图片"`
    WeatherUrl string `form:"weatherUrl"  search:"type:contains;column:weather_url;table:t_scene" comment:"天气图片"`
    BasicLicenseReleasedUrl string `form:"basicLicenseReleasedUrl"  search:"type:contains;column:basic_license_released_url;table:t_scene" comment:"基本执照图片"`
    AdvancedLicenseReleasedUrl string `form:"advancedLicenseReleasedUrl"  search:"type:contains;column:advanced_license_released_url;table:t_scene" comment:"高级执照图片"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_scene" comment:"创建者名称"`
    TSceneOrder
}

type TSceneOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_scene"`
    Scene string `form:"sceneOrder"  search:"type:order;column:scene;table:t_scene"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_scene"`
    TravelFee string `form:"travelFeeOrder"  search:"type:order;column:travel_fee;table:t_scene"`
    FishingFee string `form:"fishingFeeOrder"  search:"type:order;column:fishing_fee;table:t_scene"`
    BasicLicenseReleased string `form:"basicLicenseReleasedOrder"  search:"type:order;column:basic_license_released;table:t_scene"`
    AdvancedLicenseReleased string `form:"advancedLicenseReleasedOrder"  search:"type:order;column:advanced_license_released;table:t_scene"`
    EnFish string `form:"enFishOrder"  search:"type:order;column:en_fish;table:t_scene"`
    CnFish string `form:"cnFishOrder"  search:"type:order;column:cn_fish;table:t_scene"`
    SpeciesRule string `form:"speciesRuleOrder"  search:"type:order;column:species_rule;table:t_scene"`
    GameList string `form:"gameListOrder"  search:"type:order;column:game_list;table:t_scene"`
    SceneUrl string `form:"sceneUrlOrder"  search:"type:order;column:scene_url;table:t_scene"`
    WeatherUrl string `form:"weatherUrlOrder"  search:"type:order;column:weather_url;table:t_scene"`
    BasicLicenseReleasedUrl string `form:"basicLicenseReleasedUrlOrder"  search:"type:order;column:basic_license_released_url;table:t_scene"`
    AdvancedLicenseReleasedUrl string `form:"advancedLicenseReleasedUrlOrder"  search:"type:order;column:advanced_license_released_url;table:t_scene"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_scene"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_scene"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_scene"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_scene"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_scene"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_scene"`
    
}

func (m *TSceneGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TSceneInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Scene string `json:"scene" comment:"场景"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    TravelFee string `json:"travelFee" comment:"旅行费"`
    FishingFee string `json:"fishingFee" comment:"钓鱼费(per_day)"`
    BasicLicenseReleased string `json:"basicLicenseReleased" comment:"基本执照"`
    AdvancedLicenseReleased string `json:"advancedLicenseReleased" comment:"高级执照"`
    EnFish string `json:"enFish" comment:"鱼英文"`
    CnFish string `json:"cnFish" comment:"鱼中文"`
    SpeciesRule string `json:"speciesRule" comment:"鱼规则"`
    GameList string `json:"gameList" comment:"比赛列表"`
    SceneUrl string `json:"sceneUrl" comment:"场景图片"`
    WeatherUrl string `json:"weatherUrl" comment:"天气图片"`
    BasicLicenseReleasedUrl string `json:"basicLicenseReleasedUrl" comment:"基本执照图片"`
    AdvancedLicenseReleasedUrl string `json:"advancedLicenseReleasedUrl" comment:"高级执照图片"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSceneInsertReq) Generate(model *models.TScene)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Scene = s.Scene
    model.RequiredLevel = s.RequiredLevel
    model.TravelFee = s.TravelFee
    model.FishingFee = s.FishingFee
    model.BasicLicenseReleased = s.BasicLicenseReleased
    model.AdvancedLicenseReleased = s.AdvancedLicenseReleased
    model.EnFish = s.EnFish
    model.CnFish = s.CnFish
    model.SpeciesRule = s.SpeciesRule
    model.GameList = s.GameList
    model.SceneUrl = s.SceneUrl
    model.WeatherUrl = s.WeatherUrl
    model.BasicLicenseReleasedUrl = s.BasicLicenseReleasedUrl
    model.AdvancedLicenseReleasedUrl = s.AdvancedLicenseReleasedUrl
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TSceneInsertReq) GetId() interface{} {
	return s.Id
}

type TSceneUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Scene string `json:"scene" comment:"场景"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    TravelFee string `json:"travelFee" comment:"旅行费"`
    FishingFee string `json:"fishingFee" comment:"钓鱼费(per_day)"`
    BasicLicenseReleased string `json:"basicLicenseReleased" comment:"基本执照"`
    AdvancedLicenseReleased string `json:"advancedLicenseReleased" comment:"高级执照"`
    EnFish string `json:"enFish" comment:"鱼英文"`
    CnFish string `json:"cnFish" comment:"鱼中文"`
    SpeciesRule string `json:"speciesRule" comment:"鱼规则"`
    GameList string `json:"gameList" comment:"比赛列表"`
    SceneUrl string `json:"sceneUrl" comment:"场景图片"`
    WeatherUrl string `json:"weatherUrl" comment:"天气图片"`
    BasicLicenseReleasedUrl string `json:"basicLicenseReleasedUrl" comment:"基本执照图片"`
    AdvancedLicenseReleasedUrl string `json:"advancedLicenseReleasedUrl" comment:"高级执照图片"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSceneUpdateReq) Generate(model *models.TScene)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Scene = s.Scene
    model.RequiredLevel = s.RequiredLevel
    model.TravelFee = s.TravelFee
    model.FishingFee = s.FishingFee
    model.BasicLicenseReleased = s.BasicLicenseReleased
    model.AdvancedLicenseReleased = s.AdvancedLicenseReleased
    model.EnFish = s.EnFish
    model.CnFish = s.CnFish
    model.SpeciesRule = s.SpeciesRule
    model.GameList = s.GameList
    model.SceneUrl = s.SceneUrl
    model.WeatherUrl = s.WeatherUrl
    model.BasicLicenseReleasedUrl = s.BasicLicenseReleasedUrl
    model.AdvancedLicenseReleasedUrl = s.AdvancedLicenseReleasedUrl
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TSceneUpdateReq) GetId() interface{} {
	return s.Id
}

// TSceneGetReq 功能获取请求参数
type TSceneGetReq struct {
     Id int `uri:"id"`
}
func (s *TSceneGetReq) GetId() interface{} {
	return s.Id
}

// TSceneDeleteReq 功能删除请求参数
type TSceneDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TSceneDeleteReq) GetId() interface{} {
	return s.Ids
}