package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TBoiliesPelletsBaitsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_boilies_pellets_baits" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_boilies_pellets_baits" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_boilies_pellets_baits" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_boilies_pellets_baits" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_boilies_pellets_baits" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_boilies_pellets_baits" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_boilies_pellets_baits" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_boilies_pellets_baits" comment:"品牌"`
    Size string `form:"size"  search:"type:contains;column:size;table:t_boilies_pellets_baits" comment:"尺寸(mm)"`
    TargetFish string `form:"targetFish"  search:"type:contains;column:target_fish;table:t_boilies_pellets_baits" comment:"目标鱼"`
    Flavour string `form:"flavour"  search:"type:contains;column:flavour;table:t_boilies_pellets_baits" comment:"味道"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_boilies_pellets_baits" comment:"颜色"`
    Buoyancy string `form:"buoyancy"  search:"type:contains;column:buoyancy;table:t_boilies_pellets_baits" comment:"浮力"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_boilies_pellets_baits" comment:"重量"`
    Quantity string `form:"quantity"  search:"type:contains;column:quantity;table:t_boilies_pellets_baits" comment:"数量"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_boilies_pellets_baits" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_boilies_pellets_baits" comment:"饵币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_boilies_pellets_baits" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_boilies_pellets_baits" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_boilies_pellets_baits" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_boilies_pellets_baits" comment:"创建者名称"`
    TBoiliesPelletsBaitsOrder
}

type TBoiliesPelletsBaitsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_boilies_pellets_baits"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_boilies_pellets_baits"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_boilies_pellets_baits"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_boilies_pellets_baits"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_boilies_pellets_baits"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_boilies_pellets_baits"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_boilies_pellets_baits"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_boilies_pellets_baits"`
    Size string `form:"sizeOrder"  search:"type:order;column:size;table:t_boilies_pellets_baits"`
    TargetFish string `form:"targetFishOrder"  search:"type:order;column:target_fish;table:t_boilies_pellets_baits"`
    Flavour string `form:"flavourOrder"  search:"type:order;column:flavour;table:t_boilies_pellets_baits"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_boilies_pellets_baits"`
    Buoyancy string `form:"buoyancyOrder"  search:"type:order;column:buoyancy;table:t_boilies_pellets_baits"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_boilies_pellets_baits"`
    Quantity string `form:"quantityOrder"  search:"type:order;column:quantity;table:t_boilies_pellets_baits"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_boilies_pellets_baits"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_boilies_pellets_baits"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_boilies_pellets_baits"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_boilies_pellets_baits"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_boilies_pellets_baits"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_boilies_pellets_baits"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_boilies_pellets_baits"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_boilies_pellets_baits"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_boilies_pellets_baits"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_boilies_pellets_baits"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_boilies_pellets_baits"`
    
}

func (m *TBoiliesPelletsBaitsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TBoiliesPelletsBaitsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Size string `json:"size" comment:"尺寸(mm)"`
    TargetFish string `json:"targetFish" comment:"目标鱼"`
    Flavour string `json:"flavour" comment:"味道"`
    Color string `json:"color" comment:"颜色"`
    Buoyancy string `json:"buoyancy" comment:"浮力"`
    Weight string `json:"weight" comment:"重量"`
    Quantity string `json:"quantity" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBoiliesPelletsBaitsInsertReq) Generate(model *models.TBoiliesPelletsBaits)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Size = s.Size
    model.TargetFish = s.TargetFish
    model.Flavour = s.Flavour
    model.Color = s.Color
    model.Buoyancy = s.Buoyancy
    model.Weight = s.Weight
    model.Quantity = s.Quantity
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TBoiliesPelletsBaitsInsertReq) GetId() interface{} {
	return s.Id
}

type TBoiliesPelletsBaitsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Size string `json:"size" comment:"尺寸(mm)"`
    TargetFish string `json:"targetFish" comment:"目标鱼"`
    Flavour string `json:"flavour" comment:"味道"`
    Color string `json:"color" comment:"颜色"`
    Buoyancy string `json:"buoyancy" comment:"浮力"`
    Weight string `json:"weight" comment:"重量"`
    Quantity string `json:"quantity" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBoiliesPelletsBaitsUpdateReq) Generate(model *models.TBoiliesPelletsBaits)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Size = s.Size
    model.TargetFish = s.TargetFish
    model.Flavour = s.Flavour
    model.Color = s.Color
    model.Buoyancy = s.Buoyancy
    model.Weight = s.Weight
    model.Quantity = s.Quantity
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TBoiliesPelletsBaitsUpdateReq) GetId() interface{} {
	return s.Id
}

// TBoiliesPelletsBaitsGetReq 功能获取请求参数
type TBoiliesPelletsBaitsGetReq struct {
     Id int `uri:"id"`
}
func (s *TBoiliesPelletsBaitsGetReq) GetId() interface{} {
	return s.Id
}

// TBoiliesPelletsBaitsDeleteReq 功能删除请求参数
type TBoiliesPelletsBaitsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TBoiliesPelletsBaitsDeleteReq) GetId() interface{} {
	return s.Ids
}