package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TCastReelsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_cast_reels" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_cast_reels" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_cast_reels" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_cast_reels" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_cast_reels" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_cast_reels" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_cast_reels" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_cast_reels" comment:"品牌"`
    GearRatio string `form:"gearRatio"  search:"type:contains;column:gear_ratio;table:t_cast_reels" comment:"传动比"`
    Recovery string `form:"recovery"  search:"type:contains;column:recovery;table:t_cast_reels" comment:"收线速度（cm）"`
    LineCapacity string `form:"lineCapacity"  search:"type:contains;column:line_capacity;table:t_cast_reels" comment:"绕线量(mm/m)"`
    MaxDrag string `form:"maxDrag"  search:"type:contains;column:max_drag;table:t_cast_reels" comment:"最大拉力（kg）"`
    BallBearings string `form:"ballBearings"  search:"type:contains;column:ball_bearings;table:t_cast_reels" comment:"滚珠轴承"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_cast_reels" comment:"重量（g）"`
    Brake string `form:"brake"  search:"type:contains;column:brake;table:t_cast_reels" comment:"制动器"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_cast_reels" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_cast_reels" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_cast_reels" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_cast_reels" comment:"俱乐部币"`
    Technology string `form:"technology"  search:"type:contains;column:technology;table:t_cast_reels" comment:"技术"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_cast_reels" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_cast_reels" comment:"描述翻译"`
    Remark string `form:"remark"  search:"type:contains;column:remark;table:t_cast_reels" comment:"备注"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_cast_reels" comment:"创建者名称"`
    TCastReelsOrder
}

type TCastReelsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_cast_reels"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_cast_reels"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_cast_reels"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_cast_reels"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_cast_reels"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_cast_reels"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_cast_reels"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_cast_reels"`
    GearRatio string `form:"gearRatioOrder"  search:"type:order;column:gear_ratio;table:t_cast_reels"`
    Recovery string `form:"recoveryOrder"  search:"type:order;column:recovery;table:t_cast_reels"`
    LineCapacity string `form:"lineCapacityOrder"  search:"type:order;column:line_capacity;table:t_cast_reels"`
    MaxDrag string `form:"maxDragOrder"  search:"type:order;column:max_drag;table:t_cast_reels"`
    BallBearings string `form:"ballBearingsOrder"  search:"type:order;column:ball_bearings;table:t_cast_reels"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_cast_reels"`
    Brake string `form:"brakeOrder"  search:"type:order;column:brake;table:t_cast_reels"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_cast_reels"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_cast_reels"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_cast_reels"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_cast_reels"`
    Technology string `form:"technologyOrder"  search:"type:order;column:technology;table:t_cast_reels"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_cast_reels"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_cast_reels"`
    Remark string `form:"remarkOrder"  search:"type:order;column:remark;table:t_cast_reels"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_cast_reels"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_cast_reels"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_cast_reels"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_cast_reels"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_cast_reels"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_cast_reels"`
    
}

func (m *TCastReelsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TCastReelsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    GearRatio string `json:"gearRatio" comment:"传动比"`
    Recovery string `json:"recovery" comment:"收线速度（cm）"`
    LineCapacity string `json:"lineCapacity" comment:"绕线量(mm/m)"`
    MaxDrag string `json:"maxDrag" comment:"最大拉力（kg）"`
    BallBearings string `json:"ballBearings" comment:"滚珠轴承"`
    Weight string `json:"weight" comment:"重量（g）"`
    Brake string `json:"brake" comment:"制动器"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCastReelsInsertReq) Generate(model *models.TCastReels)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.GearRatio = s.GearRatio
    model.Recovery = s.Recovery
    model.LineCapacity = s.LineCapacity
    model.MaxDrag = s.MaxDrag
    model.BallBearings = s.BallBearings
    model.Weight = s.Weight
    model.Brake = s.Brake
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TCastReelsInsertReq) GetId() interface{} {
	return s.Id
}

type TCastReelsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    GearRatio string `json:"gearRatio" comment:"传动比"`
    Recovery string `json:"recovery" comment:"收线速度（cm）"`
    LineCapacity string `json:"lineCapacity" comment:"绕线量(mm/m)"`
    MaxDrag string `json:"maxDrag" comment:"最大拉力（kg）"`
    BallBearings string `json:"ballBearings" comment:"滚珠轴承"`
    Weight string `json:"weight" comment:"重量（g）"`
    Brake string `json:"brake" comment:"制动器"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCastReelsUpdateReq) Generate(model *models.TCastReels)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.GearRatio = s.GearRatio
    model.Recovery = s.Recovery
    model.LineCapacity = s.LineCapacity
    model.MaxDrag = s.MaxDrag
    model.BallBearings = s.BallBearings
    model.Weight = s.Weight
    model.Brake = s.Brake
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TCastReelsUpdateReq) GetId() interface{} {
	return s.Id
}

// TCastReelsGetReq 功能获取请求参数
type TCastReelsGetReq struct {
     Id int `uri:"id"`
}
func (s *TCastReelsGetReq) GetId() interface{} {
	return s.Id
}

// TCastReelsDeleteReq 功能删除请求参数
type TCastReelsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TCastReelsDeleteReq) GetId() interface{} {
	return s.Ids
}