package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TCatapultsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_catapults" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_catapults" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_catapults" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_catapults" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_catapults" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_catapults" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_catapults" comment:"名称英文"`
    Capacity string `form:"capacity"  search:"type:contains;column:capacity;table:t_catapults" comment:"容量(kg)"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_catapults" comment:"材料"`
    Range string `form:"range"  search:"type:contains;column:range;table:t_catapults" comment:"射程"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_catapults" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_catapults" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_catapults" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_catapults" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_catapults" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_catapults" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_catapults" comment:"创建者名称"`
    TCatapultsOrder
}

type TCatapultsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_catapults"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_catapults"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_catapults"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_catapults"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_catapults"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_catapults"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_catapults"`
    Capacity string `form:"capacityOrder"  search:"type:order;column:capacity;table:t_catapults"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_catapults"`
    Range string `form:"rangeOrder"  search:"type:order;column:range;table:t_catapults"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_catapults"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_catapults"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_catapults"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_catapults"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_catapults"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_catapults"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_catapults"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_catapults"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_catapults"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_catapults"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_catapults"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_catapults"`
    
}

func (m *TCatapultsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TCatapultsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Capacity string `json:"capacity" comment:"容量(kg)"`
    Material string `json:"material" comment:"材料"`
    Range string `json:"range" comment:"射程"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCatapultsInsertReq) Generate(model *models.TCatapults)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Capacity = s.Capacity
    model.Material = s.Material
    model.Range = s.Range
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TCatapultsInsertReq) GetId() interface{} {
	return s.Id
}

type TCatapultsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Capacity string `json:"capacity" comment:"容量(kg)"`
    Material string `json:"material" comment:"材料"`
    Range string `json:"range" comment:"射程"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCatapultsUpdateReq) Generate(model *models.TCatapults)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Capacity = s.Capacity
    model.Material = s.Material
    model.Range = s.Range
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TCatapultsUpdateReq) GetId() interface{} {
	return s.Id
}

// TCatapultsGetReq 功能获取请求参数
type TCatapultsGetReq struct {
     Id int `uri:"id"`
}
func (s *TCatapultsGetReq) GetId() interface{} {
	return s.Id
}

// TCatapultsDeleteReq 功能删除请求参数
type TCatapultsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TCatapultsDeleteReq) GetId() interface{} {
	return s.Ids
}