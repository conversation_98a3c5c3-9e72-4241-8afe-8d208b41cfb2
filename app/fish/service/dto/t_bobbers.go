package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TBobbersGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_bobbers" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_bobbers" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_bobbers" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_bobbers" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_bobbers" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_bobbers" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_bobbers" comment:"名称英文"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_bobbers" comment:"颜色"`
    Size string `form:"size"  search:"type:contains;column:size;table:t_bobbers" comment:"规格(cm)"`
    Shape string `form:"shape"  search:"type:contains;column:shape;table:t_bobbers" comment:"形状"`
    MaxFloatingWeight string `form:"maxFloatingWeight"  search:"type:contains;column:max_floating_weight;table:t_bobbers" comment:"最大浮重"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_bobbers" comment:"材质"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_bobbers" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_bobbers" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_bobbers" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_bobbers" comment:"俱乐部币"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_bobbers" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_bobbers" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_bobbers" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_bobbers" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_bobbers" comment:"创建者名称"`
    TBobbersOrder
}

type TBobbersOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_bobbers"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_bobbers"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_bobbers"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_bobbers"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_bobbers"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_bobbers"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_bobbers"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_bobbers"`
    Size string `form:"sizeOrder"  search:"type:order;column:size;table:t_bobbers"`
    Shape string `form:"shapeOrder"  search:"type:order;column:shape;table:t_bobbers"`
    MaxFloatingWeight string `form:"maxFloatingWeightOrder"  search:"type:order;column:max_floating_weight;table:t_bobbers"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_bobbers"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_bobbers"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_bobbers"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_bobbers"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_bobbers"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_bobbers"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_bobbers"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_bobbers"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_bobbers"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_bobbers"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_bobbers"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_bobbers"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_bobbers"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_bobbers"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_bobbers"`
    
}

func (m *TBobbersGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TBobbersInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Color string `json:"color" comment:"颜色"`
    Size string `json:"size" comment:"规格(cm)"`
    Shape string `json:"shape" comment:"形状"`
    MaxFloatingWeight string `json:"maxFloatingWeight" comment:"最大浮重"`
    Material string `json:"material" comment:"材质"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBobbersInsertReq) Generate(model *models.TBobbers)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Color = s.Color
    model.Size = s.Size
    model.Shape = s.Shape
    model.MaxFloatingWeight = s.MaxFloatingWeight
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TBobbersInsertReq) GetId() interface{} {
	return s.Id
}

type TBobbersUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Color string `json:"color" comment:"颜色"`
    Size string `json:"size" comment:"规格(cm)"`
    Shape string `json:"shape" comment:"形状"`
    MaxFloatingWeight string `json:"maxFloatingWeight" comment:"最大浮重"`
    Material string `json:"material" comment:"材质"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBobbersUpdateReq) Generate(model *models.TBobbers)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Color = s.Color
    model.Size = s.Size
    model.Shape = s.Shape
    model.MaxFloatingWeight = s.MaxFloatingWeight
    model.Material = s.Material
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TBobbersUpdateReq) GetId() interface{} {
	return s.Id
}

// TBobbersGetReq 功能获取请求参数
type TBobbersGetReq struct {
     Id int `uri:"id"`
}
func (s *TBobbersGetReq) GetId() interface{} {
	return s.Id
}

// TBobbersDeleteReq 功能删除请求参数
type TBobbersDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TBobbersDeleteReq) GetId() interface{} {
	return s.Ids
}