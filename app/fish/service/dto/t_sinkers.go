package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TSinkersGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_sinkers" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_sinkers" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_sinkers" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_sinkers" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_sinkers" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_sinkers" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_sinkers" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_sinkers" comment:"品牌"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_sinkers" comment:"重量（g）"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_sinkers" comment:"材质"`
    Form string `form:"form"  search:"type:contains;column:form;table:t_sinkers" comment:"形"`
    Count string `form:"count"  search:"type:contains;column:count;table:t_sinkers" comment:"数量"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_sinkers" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_sinkers" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_sinkers" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_sinkers" comment:"解锁等级"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_sinkers" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_sinkers" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_sinkers" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_sinkers" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_sinkers" comment:"创建者名称"`
    TSinkersOrder
}

type TSinkersOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_sinkers"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_sinkers"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_sinkers"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_sinkers"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_sinkers"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_sinkers"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_sinkers"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_sinkers"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_sinkers"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_sinkers"`
    Form string `form:"formOrder"  search:"type:order;column:form;table:t_sinkers"`
    Count string `form:"countOrder"  search:"type:order;column:count;table:t_sinkers"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_sinkers"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_sinkers"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_sinkers"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_sinkers"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_sinkers"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_sinkers"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_sinkers"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_sinkers"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_sinkers"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_sinkers"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_sinkers"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_sinkers"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_sinkers"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_sinkers"`
    
}

func (m *TSinkersGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TSinkersInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Weight string `json:"weight" comment:"重量（g）"`
    Material string `json:"material" comment:"材质"`
    Form string `json:"form" comment:"形"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSinkersInsertReq) Generate(model *models.TSinkers)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Weight = s.Weight
    model.Material = s.Material
    model.Form = s.Form
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TSinkersInsertReq) GetId() interface{} {
	return s.Id
}

type TSinkersUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Weight string `json:"weight" comment:"重量（g）"`
    Material string `json:"material" comment:"材质"`
    Form string `json:"form" comment:"形"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSinkersUpdateReq) Generate(model *models.TSinkers)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Weight = s.Weight
    model.Material = s.Material
    model.Form = s.Form
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TSinkersUpdateReq) GetId() interface{} {
	return s.Id
}

// TSinkersGetReq 功能获取请求参数
type TSinkersGetReq struct {
     Id int `uri:"id"`
}
func (s *TSinkersGetReq) GetId() interface{} {
	return s.Id
}

// TSinkersDeleteReq 功能删除请求参数
type TSinkersDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TSinkersDeleteReq) GetId() interface{} {
	return s.Ids
}