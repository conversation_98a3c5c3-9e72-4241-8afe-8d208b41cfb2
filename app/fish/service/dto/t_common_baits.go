package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TCommonBaitsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_common_baits" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_common_baits" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_common_baits" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_common_baits" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_common_baits" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_common_baits" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_common_baits" comment:"名称英文"`
    TargetFish string `form:"targetFish"  search:"type:contains;column:target_fish;table:t_common_baits" comment:"目标鱼"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_common_baits" comment:"货币"`
    Quantity string `form:"quantity"  search:"type:contains;column:quantity;table:t_common_baits" comment:"数量"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_common_baits" comment:"重量"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_common_baits" comment:"解锁等级"`
    RecommendedHookSize string `form:"recommendedHookSize"  search:"type:contains;column:recommended_hook_size;table:t_common_baits" comment:"推荐挂钩尺寸"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_common_baits" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_common_baits" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_common_baits" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_common_baits" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_common_baits" comment:"创建者名称"`
    TCommonBaitsOrder
}

type TCommonBaitsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_common_baits"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_common_baits"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_common_baits"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_common_baits"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_common_baits"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_common_baits"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_common_baits"`
    TargetFish string `form:"targetFishOrder"  search:"type:order;column:target_fish;table:t_common_baits"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_common_baits"`
    Quantity string `form:"quantityOrder"  search:"type:order;column:quantity;table:t_common_baits"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_common_baits"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_common_baits"`
    RecommendedHookSize string `form:"recommendedHookSizeOrder"  search:"type:order;column:recommended_hook_size;table:t_common_baits"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_common_baits"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_common_baits"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_common_baits"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_common_baits"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_common_baits"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_common_baits"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_common_baits"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_common_baits"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_common_baits"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_common_baits"`
    
}

func (m *TCommonBaitsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TCommonBaitsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    TargetFish string `json:"targetFish" comment:"目标鱼"`
    Money string `json:"money" comment:"货币"`
    Quantity string `json:"quantity" comment:"数量"`
    Weight string `json:"weight" comment:"重量"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    RecommendedHookSize string `json:"recommendedHookSize" comment:"推荐挂钩尺寸"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCommonBaitsInsertReq) Generate(model *models.TCommonBaits)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.TargetFish = s.TargetFish
    model.Money = s.Money
    model.Quantity = s.Quantity
    model.Weight = s.Weight
    model.RequiredLevel = s.RequiredLevel
    model.RecommendedHookSize = s.RecommendedHookSize
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TCommonBaitsInsertReq) GetId() interface{} {
	return s.Id
}

type TCommonBaitsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    TargetFish string `json:"targetFish" comment:"目标鱼"`
    Money string `json:"money" comment:"货币"`
    Quantity string `json:"quantity" comment:"数量"`
    Weight string `json:"weight" comment:"重量"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    RecommendedHookSize string `json:"recommendedHookSize" comment:"推荐挂钩尺寸"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TCommonBaitsUpdateReq) Generate(model *models.TCommonBaits)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.TargetFish = s.TargetFish
    model.Money = s.Money
    model.Quantity = s.Quantity
    model.Weight = s.Weight
    model.RequiredLevel = s.RequiredLevel
    model.RecommendedHookSize = s.RecommendedHookSize
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TCommonBaitsUpdateReq) GetId() interface{} {
	return s.Id
}

// TCommonBaitsGetReq 功能获取请求参数
type TCommonBaitsGetReq struct {
     Id int `uri:"id"`
}
func (s *TCommonBaitsGetReq) GetId() interface{} {
	return s.Id
}

// TCommonBaitsDeleteReq 功能删除请求参数
type TCommonBaitsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TCommonBaitsDeleteReq) GetId() interface{} {
	return s.Ids
}