package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TRigsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_rigs" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_rigs" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_rigs" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_rigs" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_rigs" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_rigs" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_rigs" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_rigs" comment:"品牌"`
    Diameter string `form:"diameter"  search:"type:contains;column:diameter;table:t_rigs" comment:"直径（mm）"`
    TestWeight string `form:"testWeight"  search:"type:contains;column:test_weight;table:t_rigs" comment:"测试重量（kg）"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_rigs" comment:"长度（m）"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_rigs" comment:"颜色"`
    Count string `form:"count"  search:"type:contains;column:count;table:t_rigs" comment:"数量"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_rigs" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_rigs" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_rigs" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_rigs" comment:"俱乐部币"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_rigs" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_rigs" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_rigs" comment:"创建者名称"`
    TRigsOrder
}

type TRigsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_rigs"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_rigs"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_rigs"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_rigs"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_rigs"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_rigs"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_rigs"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_rigs"`
    Diameter string `form:"diameterOrder"  search:"type:order;column:diameter;table:t_rigs"`
    TestWeight string `form:"testWeightOrder"  search:"type:order;column:test_weight;table:t_rigs"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_rigs"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_rigs"`
    Count string `form:"countOrder"  search:"type:order;column:count;table:t_rigs"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_rigs"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_rigs"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_rigs"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_rigs"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_rigs"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_rigs"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_rigs"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_rigs"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_rigs"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_rigs"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_rigs"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_rigs"`
    
}

func (m *TRigsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TRigsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Diameter string `json:"diameter" comment:"直径（mm）"`
    TestWeight string `json:"testWeight" comment:"测试重量（kg）"`
    Length string `json:"length" comment:"长度（m）"`
    Color string `json:"color" comment:"颜色"`
    Count string `json:"count" comment:"数量"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TRigsInsertReq) Generate(model *models.TRigs)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Diameter = s.Diameter
    model.TestWeight = s.TestWeight
    model.Length = s.Length
    model.Color = s.Color
    model.Count = s.Count
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TRigsInsertReq) GetId() interface{} {
	return s.Id
}

type TRigsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Diameter string `json:"diameter" comment:"直径（mm）"`
    TestWeight string `json:"testWeight" comment:"测试重量（kg）"`
    Length string `json:"length" comment:"长度（m）"`
    Color string `json:"color" comment:"颜色"`
    Count string `json:"count" comment:"数量"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TRigsUpdateReq) Generate(model *models.TRigs)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Diameter = s.Diameter
    model.TestWeight = s.TestWeight
    model.Length = s.Length
    model.Color = s.Color
    model.Count = s.Count
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TRigsUpdateReq) GetId() interface{} {
	return s.Id
}

// TRigsGetReq 功能获取请求参数
type TRigsGetReq struct {
     Id int `uri:"id"`
}
func (s *TRigsGetReq) GetId() interface{} {
	return s.Id
}

// TRigsDeleteReq 功能删除请求参数
type TRigsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TRigsDeleteReq) GetId() interface{} {
	return s.Ids
}