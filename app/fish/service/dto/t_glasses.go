package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TGlassesGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_glasses" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_glasses" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_glasses" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_glasses" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_glasses" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_glasses" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_glasses" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_glasses" comment:"品牌"`
    Model string `form:"model"  search:"type:contains;column:model;table:t_glasses" comment:"型号"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_glasses" comment:"颜色"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_glasses" comment:"材料"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_glasses" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_glasses" comment:"饵币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_glasses" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_glasses" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_glasses" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_glasses" comment:"创建者名称"`
    TGlassesOrder
}

type TGlassesOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_glasses"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_glasses"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_glasses"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_glasses"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_glasses"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_glasses"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_glasses"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_glasses"`
    Model string `form:"modelOrder"  search:"type:order;column:model;table:t_glasses"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_glasses"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_glasses"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_glasses"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_glasses"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_glasses"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_glasses"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_glasses"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_glasses"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_glasses"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_glasses"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_glasses"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_glasses"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_glasses"`
    
}

func (m *TGlassesGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TGlassesInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    ModelType string `json:"modelType" comment:"型号"`
    Color string `json:"color" comment:"颜色"`
    Material string `json:"material" comment:"材料"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TGlassesInsertReq) Generate(model *models.TGlasses)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.ModelType = s.ModelType
    model.Color = s.Color
    model.Material = s.Material
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TGlassesInsertReq) GetId() interface{} {
	return s.Id
}

type TGlassesUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    ModelType string `json:"modelType" comment:"型号"`
    Color string `json:"color" comment:"颜色"`
    Material string `json:"material" comment:"材料"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TGlassesUpdateReq) Generate(model *models.TGlasses)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.ModelType = s.ModelType
    model.Color = s.Color
    model.Material = s.Material
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TGlassesUpdateReq) GetId() interface{} {
	return s.Id
}

// TGlassesGetReq 功能获取请求参数
type TGlassesGetReq struct {
     Id int `uri:"id"`
}
func (s *TGlassesGetReq) GetId() interface{} {
	return s.Id
}

// TGlassesDeleteReq 功能删除请求参数
type TGlassesDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TGlassesDeleteReq) GetId() interface{} {
	return s.Ids
}