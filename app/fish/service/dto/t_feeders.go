package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TFeedersGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_feeders" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_feeders" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_feeders" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_feeders" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_feeders" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_feeders" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_feeders" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_feeders" comment:"品牌"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_feeders" comment:"重量（g）"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_feeders" comment:"材质"`
    Type string `form:"type"  search:"type:contains;column:type;table:t_feeders" comment:"类型"`
    Capacity string `form:"capacity"  search:"type:contains;column:capacity;table:t_feeders" comment:"容量"`
    DissolutionTime string `form:"dissolutionTime"  search:"type:contains;column:dissolution_time;table:t_feeders" comment:"溶解时间"`
    Count string `form:"count"  search:"type:contains;column:count;table:t_feeders" comment:"数量"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_feeders" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_feeders" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_feeders" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_feeders" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_feeders" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_feeders" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_feeders" comment:"创建者名称"`
    TFeedersOrder
}

type TFeedersOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_feeders"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_feeders"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_feeders"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_feeders"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_feeders"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_feeders"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_feeders"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_feeders"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_feeders"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_feeders"`
    Type string `form:"typeOrder"  search:"type:order;column:type;table:t_feeders"`
    Capacity string `form:"capacityOrder"  search:"type:order;column:capacity;table:t_feeders"`
    DissolutionTime string `form:"dissolutionTimeOrder"  search:"type:order;column:dissolution_time;table:t_feeders"`
    Count string `form:"countOrder"  search:"type:order;column:count;table:t_feeders"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_feeders"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_feeders"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_feeders"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_feeders"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_feeders"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_feeders"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_feeders"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_feeders"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_feeders"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_feeders"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_feeders"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_feeders"`
    
}

func (m *TFeedersGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFeedersInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Weight string `json:"weight" comment:"重量（g）"`
    Material string `json:"material" comment:"材质"`
    Type string `json:"type" comment:"类型"`
    Capacity string `json:"capacity" comment:"容量"`
    DissolutionTime string `json:"dissolutionTime" comment:"溶解时间"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFeedersInsertReq) Generate(model *models.TFeeders)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Weight = s.Weight
    model.Material = s.Material
    model.Type = s.Type
    model.Capacity = s.Capacity
    model.DissolutionTime = s.DissolutionTime
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFeedersInsertReq) GetId() interface{} {
	return s.Id
}

type TFeedersUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Weight string `json:"weight" comment:"重量（g）"`
    Material string `json:"material" comment:"材质"`
    Type string `json:"type" comment:"类型"`
    Capacity string `json:"capacity" comment:"容量"`
    DissolutionTime string `json:"dissolutionTime" comment:"溶解时间"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TFeedersUpdateReq) Generate(model *models.TFeeders)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Weight = s.Weight
    model.Material = s.Material
    model.Type = s.Type
    model.Capacity = s.Capacity
    model.DissolutionTime = s.DissolutionTime
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TFeedersUpdateReq) GetId() interface{} {
	return s.Id
}

// TFeedersGetReq 功能获取请求参数
type TFeedersGetReq struct {
     Id int `uri:"id"`
}
func (s *TFeedersGetReq) GetId() interface{} {
	return s.Id
}

// TFeedersDeleteReq 功能删除请求参数
type TFeedersDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFeedersDeleteReq) GetId() interface{} {
	return s.Ids
}