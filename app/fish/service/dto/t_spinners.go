package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TSpinnersGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_spinners" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_spinners" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_spinners" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_spinners" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_spinners" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_spinners" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_spinners" comment:"名称英文"`
    Type string `form:"type"  search:"type:contains;column:type;table:t_spinners" comment:"类型"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_spinners" comment:"颜色"`
    Weight string `form:"weight"  search:"type:contains;column:weight;table:t_spinners" comment:"重量(g)"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_spinners" comment:"长度(cm)"`
    HookSize string `form:"hookSize"  search:"type:contains;column:hook_size;table:t_spinners" comment:"鱼钩"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_spinners" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_spinners" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_spinners" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_spinners" comment:"俱乐部币"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_spinners" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_spinners" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_spinners" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_spinners" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_spinners" comment:"创建者名称"`
    TSpinnersOrder
}

type TSpinnersOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_spinners"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_spinners"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_spinners"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_spinners"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_spinners"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_spinners"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_spinners"`
    Type string `form:"typeOrder"  search:"type:order;column:type;table:t_spinners"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_spinners"`
    Weight string `form:"weightOrder"  search:"type:order;column:weight;table:t_spinners"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_spinners"`
    HookSize string `form:"hookSizeOrder"  search:"type:order;column:hook_size;table:t_spinners"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_spinners"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_spinners"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_spinners"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_spinners"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_spinners"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_spinners"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_spinners"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_spinners"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_spinners"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_spinners"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_spinners"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_spinners"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_spinners"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_spinners"`
    
}

func (m *TSpinnersGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TSpinnersInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Type string `json:"type" comment:"类型"`
    Color string `json:"color" comment:"颜色"`
    Weight string `json:"weight" comment:"重量(g)"`
    Length string `json:"length" comment:"长度(cm)"`
    HookSize string `json:"hookSize" comment:"鱼钩"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSpinnersInsertReq) Generate(model *models.TSpinners)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Type = s.Type
    model.Color = s.Color
    model.Weight = s.Weight
    model.Length = s.Length
    model.HookSize = s.HookSize
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TSpinnersInsertReq) GetId() interface{} {
	return s.Id
}

type TSpinnersUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Type string `json:"type" comment:"类型"`
    Color string `json:"color" comment:"颜色"`
    Weight string `json:"weight" comment:"重量(g)"`
    Length string `json:"length" comment:"长度(cm)"`
    HookSize string `json:"hookSize" comment:"鱼钩"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TSpinnersUpdateReq) Generate(model *models.TSpinners)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Type = s.Type
    model.Color = s.Color
    model.Weight = s.Weight
    model.Length = s.Length
    model.HookSize = s.HookSize
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TSpinnersUpdateReq) GetId() interface{} {
	return s.Id
}

// TSpinnersGetReq 功能获取请求参数
type TSpinnersGetReq struct {
     Id int `uri:"id"`
}
func (s *TSpinnersGetReq) GetId() interface{} {
	return s.Id
}

// TSpinnersDeleteReq 功能删除请求参数
type TSpinnersDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TSpinnersDeleteReq) GetId() interface{} {
	return s.Ids
}