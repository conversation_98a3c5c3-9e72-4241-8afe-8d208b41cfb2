package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type THatsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_hats" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_hats" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_hats" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_hats" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_hats" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_hats" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_hats" comment:"名称英文"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_hats" comment:"材料"`
    Tackles string `form:"tackles"  search:"type:contains;column:tackles;table:t_hats" comment:"辅助"`
    Flashlight string `form:"flashlight"  search:"type:contains;column:flashlight;table:t_hats" comment:"手电筒"`
    FlashlightSlot string `form:"flashlightSlot"  search:"type:contains;column:flashlight_slot;table:t_hats" comment:"手电筒槽"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_hats" comment:"货币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_hats" comment:"解锁等级"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_hats" comment:"品牌"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_hats" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_hats" comment:"描述翻译"`
    Remark string `form:"remark"  search:"type:contains;column:remark;table:t_hats" comment:"备注"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_hats" comment:"创建者名称"`
    THatsOrder
}

type THatsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_hats"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_hats"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_hats"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_hats"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_hats"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_hats"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_hats"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_hats"`
    Tackles string `form:"tacklesOrder"  search:"type:order;column:tackles;table:t_hats"`
    Flashlight string `form:"flashlightOrder"  search:"type:order;column:flashlight;table:t_hats"`
    FlashlightSlot string `form:"flashlightSlotOrder"  search:"type:order;column:flashlight_slot;table:t_hats"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_hats"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_hats"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_hats"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_hats"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_hats"`
    Remark string `form:"remarkOrder"  search:"type:order;column:remark;table:t_hats"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_hats"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_hats"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_hats"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_hats"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_hats"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_hats"`
    
}

func (m *THatsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type THatsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Material string `json:"material" comment:"材料"`
    Tackles string `json:"tackles" comment:"辅助"`
    Flashlight string `json:"flashlight" comment:"手电筒"`
    FlashlightSlot string `json:"flashlightSlot" comment:"手电筒槽"`
    Money string `json:"money" comment:"货币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Brand string `json:"brand" comment:"品牌"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *THatsInsertReq) Generate(model *models.THats)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Material = s.Material
    model.Tackles = s.Tackles
    model.Flashlight = s.Flashlight
    model.FlashlightSlot = s.FlashlightSlot
    model.Money = s.Money
    model.RequiredLevel = s.RequiredLevel
    model.Brand = s.Brand
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *THatsInsertReq) GetId() interface{} {
	return s.Id
}

type THatsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Material string `json:"material" comment:"材料"`
    Tackles string `json:"tackles" comment:"辅助"`
    Flashlight string `json:"flashlight" comment:"手电筒"`
    FlashlightSlot string `json:"flashlightSlot" comment:"手电筒槽"`
    Money string `json:"money" comment:"货币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Brand string `json:"brand" comment:"品牌"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *THatsUpdateReq) Generate(model *models.THats)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Material = s.Material
    model.Tackles = s.Tackles
    model.Flashlight = s.Flashlight
    model.FlashlightSlot = s.FlashlightSlot
    model.Money = s.Money
    model.RequiredLevel = s.RequiredLevel
    model.Brand = s.Brand
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *THatsUpdateReq) GetId() interface{} {
	return s.Id
}

// THatsGetReq 功能获取请求参数
type THatsGetReq struct {
     Id int `uri:"id"`
}
func (s *THatsGetReq) GetId() interface{} {
	return s.Id
}

// THatsDeleteReq 功能删除请求参数
type THatsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *THatsDeleteReq) GetId() interface{} {
	return s.Ids
}