package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type THooksGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_hooks" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_hooks" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_hooks" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_hooks" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_hooks" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_hooks" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_hooks" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_hooks" comment:"品牌"`
    Size string `form:"size"  search:"type:contains;column:size;table:t_hooks" comment:"型号"`
    Type string `form:"type"  search:"type:contains;column:type;table:t_hooks" comment:"类型"`
    Color string `form:"color"  search:"type:contains;column:color;table:t_hooks" comment:"颜色"`
    Sharpening string `form:"sharpening"  search:"type:contains;column:sharpening;table:t_hooks" comment:"锐化"`
    Count string `form:"count"  search:"type:contains;column:count;table:t_hooks" comment:"数量"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_hooks" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_hooks" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_hooks" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_hooks" comment:"解锁等级"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_hooks" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_hooks" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_hooks" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_hooks" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_hooks" comment:"创建者名称"`
    THooksOrder
}

type THooksOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_hooks"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_hooks"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_hooks"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_hooks"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_hooks"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_hooks"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_hooks"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_hooks"`
    Size string `form:"sizeOrder"  search:"type:order;column:size;table:t_hooks"`
    Type string `form:"typeOrder"  search:"type:order;column:type;table:t_hooks"`
    Color string `form:"colorOrder"  search:"type:order;column:color;table:t_hooks"`
    Sharpening string `form:"sharpeningOrder"  search:"type:order;column:sharpening;table:t_hooks"`
    Count string `form:"countOrder"  search:"type:order;column:count;table:t_hooks"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_hooks"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_hooks"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_hooks"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_hooks"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_hooks"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_hooks"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_hooks"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_hooks"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_hooks"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_hooks"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_hooks"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_hooks"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_hooks"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_hooks"`
    
}

func (m *THooksGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type THooksInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Size string `json:"size" comment:"型号"`
    Type string `json:"type" comment:"类型"`
    Color string `json:"color" comment:"颜色"`
    Sharpening string `json:"sharpening" comment:"锐化"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *THooksInsertReq) Generate(model *models.THooks)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Size = s.Size
    model.Type = s.Type
    model.Color = s.Color
    model.Sharpening = s.Sharpening
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *THooksInsertReq) GetId() interface{} {
	return s.Id
}

type THooksUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Size string `json:"size" comment:"型号"`
    Type string `json:"type" comment:"类型"`
    Color string `json:"color" comment:"颜色"`
    Sharpening string `json:"sharpening" comment:"锐化"`
    Count string `json:"count" comment:"数量"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *THooksUpdateReq) Generate(model *models.THooks)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Size = s.Size
    model.Type = s.Type
    model.Color = s.Color
    model.Sharpening = s.Sharpening
    model.Count = s.Count
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *THooksUpdateReq) GetId() interface{} {
	return s.Id
}

// THooksGetReq 功能获取请求参数
type THooksGetReq struct {
     Id int `uri:"id"`
}
func (s *THooksGetReq) GetId() interface{} {
	return s.Id
}

// THooksDeleteReq 功能删除请求参数
type THooksDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *THooksDeleteReq) GetId() interface{} {
	return s.Ids
}