package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TBottomRodsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_bottom_rods" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_bottom_rods" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_bottom_rods" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_bottom_rods" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_bottom_rods" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_bottom_rods" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_bottom_rods" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_bottom_rods" comment:"品牌"`
    Length string `form:"length"  search:"type:contains;column:length;table:t_bottom_rods" comment:"长度(m)"`
    LureWeight string `form:"lureWeight"  search:"type:contains;column:lure_weight;table:t_bottom_rods" comment:"饵重（g）"`
    Power string `form:"power"  search:"type:contains;column:power;table:t_bottom_rods" comment:"硬度"`
    LineWeight string `form:"lineWeight"  search:"type:contains;column:line_weight;table:t_bottom_rods" comment:"钓重（kg）"`
    Action string `form:"action"  search:"type:contains;column:action;table:t_bottom_rods" comment:"调性"`
    Pieces string `form:"pieces"  search:"type:contains;column:pieces;table:t_bottom_rods" comment:"节数"`
    Guides string `form:"guides"  search:"type:contains;column:guides;table:t_bottom_rods" comment:"导环线"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_bottom_rods" comment:"解锁等级"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_bottom_rods" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_bottom_rods" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_bottom_rods" comment:"俱乐部币"`
    Technology string `form:"technology"  search:"type:contains;column:technology;table:t_bottom_rods" comment:"技术"`
    FitReel string `form:"fitReel"  search:"type:contains;column:fit_reel;table:t_bottom_rods" comment:"适配鱼轮"`
    FitBait string `form:"fitBait"  search:"type:contains;column:fit_bait;table:t_bottom_rods" comment:"适配饵类"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_bottom_rods" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_bottom_rods" comment:"描述翻译"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_bottom_rods" comment:"创建者名称"`
    TBottomRodsOrder
}

type TBottomRodsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_bottom_rods"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_bottom_rods"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_bottom_rods"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_bottom_rods"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_bottom_rods"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_bottom_rods"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_bottom_rods"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_bottom_rods"`
    Length string `form:"lengthOrder"  search:"type:order;column:length;table:t_bottom_rods"`
    LureWeight string `form:"lureWeightOrder"  search:"type:order;column:lure_weight;table:t_bottom_rods"`
    Power string `form:"powerOrder"  search:"type:order;column:power;table:t_bottom_rods"`
    LineWeight string `form:"lineWeightOrder"  search:"type:order;column:line_weight;table:t_bottom_rods"`
    Action string `form:"actionOrder"  search:"type:order;column:action;table:t_bottom_rods"`
    Pieces string `form:"piecesOrder"  search:"type:order;column:pieces;table:t_bottom_rods"`
    Guides string `form:"guidesOrder"  search:"type:order;column:guides;table:t_bottom_rods"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_bottom_rods"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_bottom_rods"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_bottom_rods"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_bottom_rods"`
    Technology string `form:"technologyOrder"  search:"type:order;column:technology;table:t_bottom_rods"`
    FitReel string `form:"fitReelOrder"  search:"type:order;column:fit_reel;table:t_bottom_rods"`
    FitBait string `form:"fitBaitOrder"  search:"type:order;column:fit_bait;table:t_bottom_rods"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_bottom_rods"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_bottom_rods"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_bottom_rods"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_bottom_rods"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_bottom_rods"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_bottom_rods"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_bottom_rods"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_bottom_rods"`
    
}

func (m *TBottomRodsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TBottomRodsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Length string `json:"length" comment:"长度(m)"`
    LureWeight string `json:"lureWeight" comment:"饵重（g）"`
    Power string `json:"power" comment:"硬度"`
    LineWeight string `json:"lineWeight" comment:"钓重（kg）"`
    Action string `json:"action" comment:"调性"`
    Pieces string `json:"pieces" comment:"节数"`
    Guides string `json:"guides" comment:"导环线"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBottomRodsInsertReq) Generate(model *models.TBottomRods)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Length = s.Length
    model.LureWeight = s.LureWeight
    model.Power = s.Power
    model.LineWeight = s.LineWeight
    model.Action = s.Action
    model.Pieces = s.Pieces
    model.Guides = s.Guides
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TBottomRodsInsertReq) GetId() interface{} {
	return s.Id
}

type TBottomRodsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Length string `json:"length" comment:"长度(m)"`
    LureWeight string `json:"lureWeight" comment:"饵重（g）"`
    Power string `json:"power" comment:"硬度"`
    LineWeight string `json:"lineWeight" comment:"钓重（kg）"`
    Action string `json:"action" comment:"调性"`
    Pieces string `json:"pieces" comment:"节数"`
    Guides string `json:"guides" comment:"导环线"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    Technology string `json:"technology" comment:"技术"`
    FitReel string `json:"fitReel" comment:"适配鱼轮"`
    FitBait string `json:"fitBait" comment:"适配饵类"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TBottomRodsUpdateReq) Generate(model *models.TBottomRods)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Length = s.Length
    model.LureWeight = s.LureWeight
    model.Power = s.Power
    model.LineWeight = s.LineWeight
    model.Action = s.Action
    model.Pieces = s.Pieces
    model.Guides = s.Guides
    model.RequiredLevel = s.RequiredLevel
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.Technology = s.Technology
    model.FitReel = s.FitReel
    model.FitBait = s.FitBait
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TBottomRodsUpdateReq) GetId() interface{} {
	return s.Id
}

// TBottomRodsGetReq 功能获取请求参数
type TBottomRodsGetReq struct {
     Id int `uri:"id"`
}
func (s *TBottomRodsGetReq) GetId() interface{} {
	return s.Id
}

// TBottomRodsDeleteReq 功能删除请求参数
type TBottomRodsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TBottomRodsDeleteReq) GetId() interface{} {
	return s.Ids
}