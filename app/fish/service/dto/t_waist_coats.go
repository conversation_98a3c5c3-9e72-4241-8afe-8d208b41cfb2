package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TWaistCoatsGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_waist_coats" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_waist_coats" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_waist_coats" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_waist_coats" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_waist_coats" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_waist_coats" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_waist_coats" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_waist_coats" comment:"品牌"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_waist_coats" comment:"材质"`
    StorageCapacity string `form:"storageCapacity"  search:"type:contains;column:storage_capacity;table:t_waist_coats" comment:"容量"`
    Reels string `form:"reels"  search:"type:contains;column:reels;table:t_waist_coats" comment:"卷线器"`
    Lines string `form:"lines"  search:"type:contains;column:lines;table:t_waist_coats" comment:"钓线"`
    Tackles string `form:"tackles"  search:"type:contains;column:tackles;table:t_waist_coats" comment:"渔具"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_waist_coats" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_waist_coats" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_waist_coats" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_waist_coats" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_waist_coats" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_waist_coats" comment:"描述翻译"`
    Remark string `form:"remark"  search:"type:contains;column:remark;table:t_waist_coats" comment:"备注"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_waist_coats" comment:"创建者名称"`
    TWaistCoatsOrder
}

type TWaistCoatsOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_waist_coats"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_waist_coats"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_waist_coats"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_waist_coats"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_waist_coats"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_waist_coats"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_waist_coats"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_waist_coats"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_waist_coats"`
    StorageCapacity string `form:"storageCapacityOrder"  search:"type:order;column:storage_capacity;table:t_waist_coats"`
    Reels string `form:"reelsOrder"  search:"type:order;column:reels;table:t_waist_coats"`
    Lines string `form:"linesOrder"  search:"type:order;column:lines;table:t_waist_coats"`
    Tackles string `form:"tacklesOrder"  search:"type:order;column:tackles;table:t_waist_coats"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_waist_coats"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_waist_coats"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_waist_coats"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_waist_coats"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_waist_coats"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_waist_coats"`
    Remark string `form:"remarkOrder"  search:"type:order;column:remark;table:t_waist_coats"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_waist_coats"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_waist_coats"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_waist_coats"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_waist_coats"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_waist_coats"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_waist_coats"`
    
}

func (m *TWaistCoatsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TWaistCoatsInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Material string `json:"material" comment:"材质"`
    StorageCapacity string `json:"storageCapacity" comment:"容量"`
    Reels string `json:"reels" comment:"卷线器"`
    Lines string `json:"lines" comment:"钓线"`
    Tackles string `json:"tackles" comment:"渔具"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TWaistCoatsInsertReq) Generate(model *models.TWaistCoats)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Material = s.Material
    model.StorageCapacity = s.StorageCapacity
    model.Reels = s.Reels
    model.Lines = s.Lines
    model.Tackles = s.Tackles
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TWaistCoatsInsertReq) GetId() interface{} {
	return s.Id
}

type TWaistCoatsUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Material string `json:"material" comment:"材质"`
    StorageCapacity string `json:"storageCapacity" comment:"容量"`
    Reels string `json:"reels" comment:"卷线器"`
    Lines string `json:"lines" comment:"钓线"`
    Tackles string `json:"tackles" comment:"渔具"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TWaistCoatsUpdateReq) Generate(model *models.TWaistCoats)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Material = s.Material
    model.StorageCapacity = s.StorageCapacity
    model.Reels = s.Reels
    model.Lines = s.Lines
    model.Tackles = s.Tackles
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TWaistCoatsUpdateReq) GetId() interface{} {
	return s.Id
}

// TWaistCoatsGetReq 功能获取请求参数
type TWaistCoatsGetReq struct {
     Id int `uri:"id"`
}
func (s *TWaistCoatsGetReq) GetId() interface{} {
	return s.Id
}

// TWaistCoatsDeleteReq 功能删除请求参数
type TWaistCoatsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TWaistCoatsDeleteReq) GetId() interface{} {
	return s.Ids
}