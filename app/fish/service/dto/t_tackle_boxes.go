package dto

import (
	"go-admin/app/fish/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TTackleBoxesGetPageReq struct {
	dto.Pagination     `search:"-"`
    Id int `form:"id"  search:"type:contains;column:id;table:t_tackle_boxes" comment:"主键id"`
    Category string `form:"category"  search:"type:contains;column:category;table:t_tackle_boxes" comment:"类别"`
    Resident string `form:"resident"  search:"type:contains;column:resident;table:t_tackle_boxes" comment:"常驻"`
    CnTaxon string `form:"cnTaxon"  search:"type:contains;column:cn_taxon;table:t_tackle_boxes" comment:"类名中文"`
    EnTaxon string `form:"enTaxon"  search:"type:contains;column:en_taxon;table:t_tackle_boxes" comment:"类名英文"`
    ImageUrl string `form:"imageUrl"  search:"type:contains;column:image_url;table:t_tackle_boxes" comment:"图片地址"`
    EnName string `form:"enName"  search:"type:contains;column:en_name;table:t_tackle_boxes" comment:"名称英文"`
    Brand string `form:"brand"  search:"type:contains;column:brand;table:t_tackle_boxes" comment:"品牌"`
    Material string `form:"material"  search:"type:contains;column:material;table:t_tackle_boxes" comment:"材质"`
    StorageCapacity string `form:"storageCapacity"  search:"type:contains;column:storage_capacity;table:t_tackle_boxes" comment:"容量"`
    Reels string `form:"reels"  search:"type:contains;column:reels;table:t_tackle_boxes" comment:"卷线器"`
    Lines string `form:"lines"  search:"type:contains;column:lines;table:t_tackle_boxes" comment:"钓线"`
    Tackles string `form:"tackles"  search:"type:contains;column:tackles;table:t_tackle_boxes" comment:"渔具"`
    GroundbaitComponents string `form:"groundbaitComponents"  search:"type:contains;column:groundbait_components;table:t_tackle_boxes" comment:"飞德钓鱼饵部件"`
    Money string `form:"money"  search:"type:contains;column:money;table:t_tackle_boxes" comment:"货币"`
    BaitCoin string `form:"baitCoin"  search:"type:contains;column:bait_coin;table:t_tackle_boxes" comment:"饵币"`
    ClubCurrency string `form:"clubCurrency"  search:"type:contains;column:club_currency;table:t_tackle_boxes" comment:"俱乐部币"`
    RequiredLevel string `form:"requiredLevel"  search:"type:contains;column:required_level;table:t_tackle_boxes" comment:"解锁等级"`
    Description string `form:"description"  search:"type:contains;column:description;table:t_tackle_boxes" comment:"描述"`
    CnDescriptive string `form:"cnDescriptive"  search:"type:contains;column:cn_descriptive;table:t_tackle_boxes" comment:"描述翻译"`
    Remark string `form:"remark"  search:"type:contains;column:remark;table:t_tackle_boxes" comment:"备注"`
    CreateName string `form:"createName"  search:"type:contains;column:create_name;table:t_tackle_boxes" comment:"创建者名称"`
    TTackleBoxesOrder
}

type TTackleBoxesOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_tackle_boxes"`
    Category string `form:"categoryOrder"  search:"type:order;column:category;table:t_tackle_boxes"`
    Resident string `form:"residentOrder"  search:"type:order;column:resident;table:t_tackle_boxes"`
    CnTaxon string `form:"cnTaxonOrder"  search:"type:order;column:cn_taxon;table:t_tackle_boxes"`
    EnTaxon string `form:"enTaxonOrder"  search:"type:order;column:en_taxon;table:t_tackle_boxes"`
    ImageUrl string `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_tackle_boxes"`
    EnName string `form:"enNameOrder"  search:"type:order;column:en_name;table:t_tackle_boxes"`
    Brand string `form:"brandOrder"  search:"type:order;column:brand;table:t_tackle_boxes"`
    Material string `form:"materialOrder"  search:"type:order;column:material;table:t_tackle_boxes"`
    StorageCapacity string `form:"storageCapacityOrder"  search:"type:order;column:storage_capacity;table:t_tackle_boxes"`
    Reels string `form:"reelsOrder"  search:"type:order;column:reels;table:t_tackle_boxes"`
    Lines string `form:"linesOrder"  search:"type:order;column:lines;table:t_tackle_boxes"`
    Tackles string `form:"tacklesOrder"  search:"type:order;column:tackles;table:t_tackle_boxes"`
    GroundbaitComponents string `form:"groundbaitComponentsOrder"  search:"type:order;column:groundbait_components;table:t_tackle_boxes"`
    Money string `form:"moneyOrder"  search:"type:order;column:money;table:t_tackle_boxes"`
    BaitCoin string `form:"baitCoinOrder"  search:"type:order;column:bait_coin;table:t_tackle_boxes"`
    ClubCurrency string `form:"clubCurrencyOrder"  search:"type:order;column:club_currency;table:t_tackle_boxes"`
    RequiredLevel string `form:"requiredLevelOrder"  search:"type:order;column:required_level;table:t_tackle_boxes"`
    Description string `form:"descriptionOrder"  search:"type:order;column:description;table:t_tackle_boxes"`
    CnDescriptive string `form:"cnDescriptiveOrder"  search:"type:order;column:cn_descriptive;table:t_tackle_boxes"`
    Remark string `form:"remarkOrder"  search:"type:order;column:remark;table:t_tackle_boxes"`
    CreateName string `form:"createNameOrder"  search:"type:order;column:create_name;table:t_tackle_boxes"`
    UpdateBy string `form:"updateByOrder"  search:"type:order;column:update_by;table:t_tackle_boxes"`
    CreateBy string `form:"createByOrder"  search:"type:order;column:create_by;table:t_tackle_boxes"`
    CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_tackle_boxes"`
    UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_tackle_boxes"`
    DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_tackle_boxes"`
    
}

func (m *TTackleBoxesGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TTackleBoxesInsertReq struct {
    Id int `json:"-" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Material string `json:"material" comment:"材质"`
    StorageCapacity string `json:"storageCapacity" comment:"容量"`
    Reels string `json:"reels" comment:"卷线器"`
    Lines string `json:"lines" comment:"钓线"`
    Tackles string `json:"tackles" comment:"渔具"`
    GroundbaitComponents string `json:"groundbaitComponents" comment:"飞德钓鱼饵部件"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TTackleBoxesInsertReq) Generate(model *models.TTackleBoxes)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Material = s.Material
    model.StorageCapacity = s.StorageCapacity
    model.Reels = s.Reels
    model.Lines = s.Lines
    model.Tackles = s.Tackles
    model.GroundbaitComponents = s.GroundbaitComponents
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TTackleBoxesInsertReq) GetId() interface{} {
	return s.Id
}

type TTackleBoxesUpdateReq struct {
    Id int `uri:"id" comment:"主键id"` // 主键id
    Category string `json:"category" comment:"类别"`
    Resident string `json:"resident" comment:"常驻"`
    CnTaxon string `json:"cnTaxon" comment:"类名中文"`
    EnTaxon string `json:"enTaxon" comment:"类名英文"`
    ImageUrl string `json:"imageUrl" comment:"图片地址"`
    EnName string `json:"enName" comment:"名称英文"`
    Brand string `json:"brand" comment:"品牌"`
    Material string `json:"material" comment:"材质"`
    StorageCapacity string `json:"storageCapacity" comment:"容量"`
    Reels string `json:"reels" comment:"卷线器"`
    Lines string `json:"lines" comment:"钓线"`
    Tackles string `json:"tackles" comment:"渔具"`
    GroundbaitComponents string `json:"groundbaitComponents" comment:"飞德钓鱼饵部件"`
    Money string `json:"money" comment:"货币"`
    BaitCoin string `json:"baitCoin" comment:"饵币"`
    ClubCurrency string `json:"clubCurrency" comment:"俱乐部币"`
    RequiredLevel string `json:"requiredLevel" comment:"解锁等级"`
    Description string `json:"description" comment:"描述"`
    CnDescriptive string `json:"cnDescriptive" comment:"描述翻译"`
    Remark string `json:"remark" comment:"备注"`
    CreateName string `json:"createName" comment:"创建者名称"`
    common.ControlBy
}

func (s *TTackleBoxesUpdateReq) Generate(model *models.TTackleBoxes)  {
    if s.Id == 0 {
        model.Model = common.Model{ Id: s.Id }
    }
    model.Category = s.Category
    model.Resident = s.Resident
    model.CnTaxon = s.CnTaxon
    model.EnTaxon = s.EnTaxon
    model.ImageUrl = s.ImageUrl
    model.EnName = s.EnName
    model.Brand = s.Brand
    model.Material = s.Material
    model.StorageCapacity = s.StorageCapacity
    model.Reels = s.Reels
    model.Lines = s.Lines
    model.Tackles = s.Tackles
    model.GroundbaitComponents = s.GroundbaitComponents
    model.Money = s.Money
    model.BaitCoin = s.BaitCoin
    model.ClubCurrency = s.ClubCurrency
    model.RequiredLevel = s.RequiredLevel
    model.Description = s.Description
    model.CnDescriptive = s.CnDescriptive
    model.Remark = s.Remark
    model.CreateName = s.CreateName
    model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TTackleBoxesUpdateReq) GetId() interface{} {
	return s.Id
}

// TTackleBoxesGetReq 功能获取请求参数
type TTackleBoxesGetReq struct {
     Id int `uri:"id"`
}
func (s *TTackleBoxesGetReq) GetId() interface{} {
	return s.Id
}

// TTackleBoxesDeleteReq 功能删除请求参数
type TTackleBoxesDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TTackleBoxesDeleteReq) GetId() interface{} {
	return s.Ids
}