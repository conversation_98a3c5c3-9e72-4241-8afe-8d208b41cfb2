package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TKeepnets struct {
	service.Service
}

// GetPage 获取TKeepnets列表
func (e *TKeepnets) GetPage(c *dto.TKeepnetsGetPageReq, p *actions.DataPermission, list *[]models.TKeepnets, count *int64) error {
	var err error
	var data models.TKeepnets

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TKeepnetsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TKeepnets对象
func (e *TKeepnets) Get(d *dto.TKeepnetsGetReq, p *actions.DataPermission, model *models.TKeepnets) error {
	var data models.TKeepnets

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTKeepnets error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TKeepnets对象
func (e *TKeepnets) Insert(c *dto.TKeepnetsInsertReq) error {
    var err error
    var data models.TKeepnets
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TKeepnetsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TKeepnets对象
func (e *TKeepnets) Update(c *dto.TKeepnetsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TKeepnets{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TKeepnetsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TKeepnets
func (e *TKeepnets) Remove(d *dto.TKeepnetsDeleteReq, p *actions.DataPermission) error {
	var data models.TKeepnets

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTKeepnets error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}