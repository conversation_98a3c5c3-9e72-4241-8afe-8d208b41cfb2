package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TRigs struct {
	service.Service
}

// GetPage 获取TRigs列表
func (e *TRigs) GetPage(c *dto.TRigsGetPageReq, p *actions.DataPermission, list *[]models.TRigs, count *int64) error {
	var err error
	var data models.TRigs

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TRigsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TRigs对象
func (e *TRigs) Get(d *dto.TRigsGetReq, p *actions.DataPermission, model *models.TRigs) error {
	var data models.TRigs

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTRigs error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TRigs对象
func (e *TRigs) Insert(c *dto.TRigsInsertReq) error {
    var err error
    var data models.TRigs
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TRigsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TRigs对象
func (e *TRigs) Update(c *dto.TRigsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TRigs{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TRigsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TRigs
func (e *TRigs) Remove(d *dto.TRigsDeleteReq, p *actions.DataPermission) error {
	var data models.TRigs

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTRigs error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}