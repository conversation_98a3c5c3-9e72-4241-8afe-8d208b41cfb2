package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCastReels struct {
	service.Service
}

// GetPage 获取TCastReels列表
func (e *TCastReels) GetPage(c *dto.TCastReelsGetPageReq, p *actions.DataPermission, list *[]models.TCastReels, count *int64) error {
	var err error
	var data models.TCastReels

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TCastReelsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCastReels对象
func (e *TCastReels) Get(d *dto.TCastReelsGetReq, p *actions.DataPermission, model *models.TCastReels) error {
	var data models.TCastReels

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCastReels error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCastReels对象
func (e *TCastReels) Insert(c *dto.TCastReelsInsertReq) error {
    var err error
    var data models.TCastReels
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCastReelsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCastReels对象
func (e *TCastReels) Update(c *dto.TCastReelsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCastReels{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCastReelsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCastReels
func (e *TCastReels) Remove(d *dto.TCastReelsDeleteReq, p *actions.DataPermission) error {
	var data models.TCastReels

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCastReels error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}