package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TMonoLeaders struct {
	service.Service
}

// GetPage 获取TMonoLeaders列表
func (e *TMonoLeaders) GetPage(c *dto.TMonoLeadersGetPageReq, p *actions.DataPermission, list *[]models.TMonoLeaders, count *int64) error {
	var err error
	var data models.TMonoLeaders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TMonoLeadersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TMonoLeaders对象
func (e *TMonoLeaders) Get(d *dto.TMonoLeadersGetReq, p *actions.DataPermission, model *models.TMonoLeaders) error {
	var data models.TMonoLeaders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTMonoLeaders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TMonoLeaders对象
func (e *TMonoLeaders) Insert(c *dto.TMonoLeadersInsertReq) error {
    var err error
    var data models.TMonoLeaders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMonoLeadersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TMonoLeaders对象
func (e *TMonoLeaders) Update(c *dto.TMonoLeadersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TMonoLeaders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TMonoLeadersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TMonoLeaders
func (e *TMonoLeaders) Remove(d *dto.TMonoLeadersDeleteReq, p *actions.DataPermission) error {
	var data models.TMonoLeaders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTMonoLeaders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}