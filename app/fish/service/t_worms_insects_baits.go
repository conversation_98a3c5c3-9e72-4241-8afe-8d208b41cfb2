package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TWormsInsectsBaits struct {
	service.Service
}

// GetPage 获取TWormsInsectsBaits列表
func (e *TWormsInsectsBaits) GetPage(c *dto.TWormsInsectsBaitsGetPageReq, p *actions.DataPermission, list *[]models.TWormsInsectsBaits, count *int64) error {
	var err error
	var data models.TWormsInsectsBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TWormsInsectsBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TWormsInsectsBaits对象
func (e *TWormsInsectsBaits) Get(d *dto.TWormsInsectsBaitsGetReq, p *actions.DataPermission, model *models.TWormsInsectsBaits) error {
	var data models.TWormsInsectsBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTWormsInsectsBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TWormsInsectsBaits对象
func (e *TWormsInsectsBaits) Insert(c *dto.TWormsInsectsBaitsInsertReq) error {
    var err error
    var data models.TWormsInsectsBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TWormsInsectsBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TWormsInsectsBaits对象
func (e *TWormsInsectsBaits) Update(c *dto.TWormsInsectsBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TWormsInsectsBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TWormsInsectsBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TWormsInsectsBaits
func (e *TWormsInsectsBaits) Remove(d *dto.TWormsInsectsBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TWormsInsectsBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTWormsInsectsBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}