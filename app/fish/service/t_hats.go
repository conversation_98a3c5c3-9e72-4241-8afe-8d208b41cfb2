package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type THats struct {
	service.Service
}

// GetPage 获取THats列表
func (e *THats) GetPage(c *dto.THatsGetPageReq, p *actions.DataPermission, list *[]models.THats, count *int64) error {
	var err error
	var data models.THats

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("THatsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取THats对象
func (e *THats) Get(d *dto.THatsGetReq, p *actions.DataPermission, model *models.THats) error {
	var data models.THats

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTHats error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建THats对象
func (e *THats) Insert(c *dto.THatsInsertReq) error {
    var err error
    var data models.THats
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("THatsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改THats对象
func (e *THats) Update(c *dto.THatsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.THats{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("THatsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除THats
func (e *THats) Remove(d *dto.THatsDeleteReq, p *actions.DataPermission) error {
	var data models.THats

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTHats error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}