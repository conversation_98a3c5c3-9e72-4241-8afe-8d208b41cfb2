package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TTitaniumLeaders struct {
	service.Service
}

// GetPage 获取TTitaniumLeaders列表
func (e *TTitaniumLeaders) GetPage(c *dto.TTitaniumLeadersGetPageReq, p *actions.DataPermission, list *[]models.TTitaniumLeaders, count *int64) error {
	var err error
	var data models.TTitaniumLeaders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TTitaniumLeadersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TTitaniumLeaders对象
func (e *TTitaniumLeaders) Get(d *dto.TTitaniumLeadersGetReq, p *actions.DataPermission, model *models.TTitaniumLeaders) error {
	var data models.TTitaniumLeaders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTTitaniumLeaders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TTitaniumLeaders对象
func (e *TTitaniumLeaders) Insert(c *dto.TTitaniumLeadersInsertReq) error {
    var err error
    var data models.TTitaniumLeaders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TTitaniumLeadersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TTitaniumLeaders对象
func (e *TTitaniumLeaders) Update(c *dto.TTitaniumLeadersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TTitaniumLeaders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TTitaniumLeadersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TTitaniumLeaders
func (e *TTitaniumLeaders) Remove(d *dto.TTitaniumLeadersDeleteReq, p *actions.DataPermission) error {
	var data models.TTitaniumLeaders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTTitaniumLeaders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}