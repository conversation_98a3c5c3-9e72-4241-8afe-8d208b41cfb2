package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFreshBaits struct {
	service.Service
}

// GetPage 获取TFreshBaits列表
func (e *TFreshBaits) GetPage(c *dto.TFreshBaitsGetPageReq, p *actions.DataPermission, list *[]models.TFreshBaits, count *int64) error {
	var err error
	var data models.TFreshBaits

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TFreshBaitsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFreshBaits对象
func (e *TFreshBaits) Get(d *dto.TFreshBaitsGetReq, p *actions.DataPermission, model *models.TFreshBaits) error {
	var data models.TFreshBaits

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFreshBaits error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFreshBaits对象
func (e *TFreshBaits) Insert(c *dto.TFreshBaitsInsertReq) error {
    var err error
    var data models.TFreshBaits
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFreshBaitsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFreshBaits对象
func (e *TFreshBaits) Update(c *dto.TFreshBaitsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFreshBaits{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFreshBaitsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFreshBaits
func (e *TFreshBaits) Remove(d *dto.TFreshBaitsDeleteReq, p *actions.DataPermission) error {
	var data models.TFreshBaits

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFreshBaits error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}