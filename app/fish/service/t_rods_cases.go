package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TRodsCases struct {
	service.Service
}

// GetPage 获取TRodsCases列表
func (e *TRodsCases) GetPage(c *dto.TRodsCasesGetPageReq, p *actions.DataPermission, list *[]models.TRodsCases, count *int64) error {
	var err error
	var data models.TRodsCases

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TRodsCasesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TRodsCases对象
func (e *TRodsCases) Get(d *dto.TRodsCasesGetReq, p *actions.DataPermission, model *models.TRodsCases) error {
	var data models.TRodsCases

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTRodsCases error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TRodsCases对象
func (e *TRodsCases) Insert(c *dto.TRodsCasesInsertReq) error {
    var err error
    var data models.TRodsCases
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TRodsCasesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TRodsCases对象
func (e *TRodsCases) Update(c *dto.TRodsCasesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TRodsCases{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TRodsCasesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TRodsCases
func (e *TRodsCases) Remove(d *dto.TRodsCasesDeleteReq, p *actions.DataPermission) error {
	var data models.TRodsCases

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTRodsCases error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}