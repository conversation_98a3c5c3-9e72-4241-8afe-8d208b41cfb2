package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TSpoons struct {
	service.Service
}

// GetPage 获取TSpoons列表
func (e *TSpoons) GetPage(c *dto.TSpoonsGetPageReq, p *actions.DataPermission, list *[]models.TSpoons, count *int64) error {
	var err error
	var data models.TSpoons

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TSpoonsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TSpoons对象
func (e *TSpoons) Get(d *dto.TSpoonsGetReq, p *actions.DataPermission, model *models.TSpoons) error {
	var data models.TSpoons

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTSpoons error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TSpoons对象
func (e *TSpoons) Insert(c *dto.TSpoonsInsertReq) error {
    var err error
    var data models.TSpoons
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TSpoonsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TSpoons对象
func (e *TSpoons) Update(c *dto.TSpoonsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TSpoons{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TSpoonsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TSpoons
func (e *TSpoons) Remove(d *dto.TSpoonsDeleteReq, p *actions.DataPermission) error {
	var data models.TSpoons

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTSpoons error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}