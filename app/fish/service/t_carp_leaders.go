package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TCarpLeaders struct {
	service.Service
}

// GetPage 获取TCarpLeaders列表
func (e *TCarpLeaders) GetPage(c *dto.TCarpLeadersGetPageReq, p *actions.DataPermission, list *[]models.TCarpLeaders, count *int64) error {
	var err error
	var data models.TCarpLeaders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TCarpLeadersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TCarpLeaders对象
func (e *TCarpLeaders) Get(d *dto.TCarpLeadersGetReq, p *actions.DataPermission, model *models.TCarpLeaders) error {
	var data models.TCarpLeaders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTCarpLeaders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TCarpLeaders对象
func (e *TCarpLeaders) Insert(c *dto.TCarpLeadersInsertReq) error {
    var err error
    var data models.TCarpLeaders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TCarpLeadersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TCarpLeaders对象
func (e *TCarpLeaders) Update(c *dto.TCarpLeadersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TCarpLeaders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TCarpLeadersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TCarpLeaders
func (e *TCarpLeaders) Remove(d *dto.TCarpLeadersDeleteReq, p *actions.DataPermission) error {
	var data models.TCarpLeaders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTCarpLeaders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}