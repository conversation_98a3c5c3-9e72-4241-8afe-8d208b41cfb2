package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFluorocarbonLines struct {
	service.Service
}

// GetPage 获取TFluorocarbonLines列表
func (e *TFluorocarbonLines) GetPage(c *dto.TFluorocarbonLinesGetPageReq, p *actions.DataPermission, list *[]models.TFluorocarbonLines, count *int64) error {
	var err error
	var data models.TFluorocarbonLines

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFluorocarbonLinesService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFluorocarbonLines对象
func (e *TFluorocarbonLines) Get(d *dto.TFluorocarbonLinesGetReq, p *actions.DataPermission, model *models.TFluorocarbonLines) error {
	var data models.TFluorocarbonLines

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFluorocarbonLines error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFluorocarbonLines对象
func (e *TFluorocarbonLines) Insert(c *dto.TFluorocarbonLinesInsertReq) error {
    var err error
    var data models.TFluorocarbonLines
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFluorocarbonLinesService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFluorocarbonLines对象
func (e *TFluorocarbonLines) Update(c *dto.TFluorocarbonLinesUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFluorocarbonLines{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFluorocarbonLinesService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFluorocarbonLines
func (e *TFluorocarbonLines) Remove(d *dto.TFluorocarbonLinesDeleteReq, p *actions.DataPermission) error {
	var data models.TFluorocarbonLines

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFluorocarbonLines error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}