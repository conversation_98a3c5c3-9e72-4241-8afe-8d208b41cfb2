package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFlashlights struct {
	service.Service
}

// GetPage 获取TFlashlights列表
func (e *TFlashlights) GetPage(c *dto.TFlashlightsGetPageReq, p *actions.DataPermission, list *[]models.TFlashlights, count *int64) error {
	var err error
	var data models.TFlashlights

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rro<PERSON>("TFlashlightsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFlashlights对象
func (e *TFlashlights) Get(d *dto.TFlashlightsGetReq, p *actions.DataPermission, model *models.TFlashlights) error {
	var data models.TFlashlights

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFlashlights error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFlashlights对象
func (e *TFlashlights) Insert(c *dto.TFlashlightsInsertReq) error {
    var err error
    var data models.TFlashlights
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFlashlightsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFlashlights对象
func (e *TFlashlights) Update(c *dto.TFlashlightsUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TFlashlights{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TFlashlightsService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TFlashlights
func (e *TFlashlights) Remove(d *dto.TFlashlightsDeleteReq, p *actions.DataPermission) error {
	var data models.TFlashlights

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTFlashlights error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}