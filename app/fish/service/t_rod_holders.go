package service

import (
	"errors"

    "go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TRodHolders struct {
	service.Service
}

// GetPage 获取TRodHolders列表
func (e *TRodHolders) GetPage(c *dto.TRodHoldersGetPageReq, p *actions.DataPermission, list *[]models.TRodHolders, count *int64) error {
	var err error
	var data models.TRodHolders

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TRodHoldersService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TRodHolders对象
func (e *TRodHolders) Get(d *dto.TRodHoldersGetReq, p *actions.DataPermission, model *models.TRodHolders) error {
	var data models.TRodHolders

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTRodHolders error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TRodHolders对象
func (e *TRodHolders) Insert(c *dto.TRodHoldersInsertReq) error {
    var err error
    var data models.TRodHolders
    c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TRodHoldersService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TRodHolders对象
func (e *TRodHolders) Update(c *dto.TRodHoldersUpdateReq, p *actions.DataPermission) error {
    var err error
    var data = models.TRodHolders{}
    e.Orm.Scopes(
            actions.Permission(data.TableName(), p),
        ).First(&data, c.GetId())
    c.Generate(&data)

    db := e.Orm.Save(&data)
    if db.Error != nil {
        e.Log.Errorf("TRodHoldersService Save error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权更新该数据")
    }
    return nil
}

// Remove 删除TRodHolders
func (e *TRodHolders) Remove(d *dto.TRodHoldersDeleteReq, p *actions.DataPermission) error {
	var data models.TRodHolders

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
        e.Log.Errorf("Service RemoveTRodHolders error:%s \r\n", err)
        return err
    }
    if db.RowsAffected == 0 {
        return errors.New("无权删除该数据")
    }
	return nil
}