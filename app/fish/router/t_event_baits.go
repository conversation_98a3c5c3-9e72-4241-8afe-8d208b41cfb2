package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/fish/apis"
	"go-admin/common/middleware"
	"go-admin/common/actions"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTEventBaitsRouter)
}

// registerTEventBaitsRouter
func registerTEventBaitsRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TEventBaits{}
	r := v1.Group("/t-event-baits").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
		r.DELETE("", api.Delete)
	}
}