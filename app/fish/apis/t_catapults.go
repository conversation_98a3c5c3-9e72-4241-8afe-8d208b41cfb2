package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TCatapults struct {
	api.Api
}

// GetPage 获取弹弓列表
// @Summary 获取弹弓列表
// @Description 获取弹弓列表
// @Tags 弹弓
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param capacity query string false "容量(kg)"
// @Param material query string false "材料"
// @Param range query string false "射程"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param clubCurrency query string false "俱乐部币"
// @Param requiredLevel query string false "解锁等级"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述翻译"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TCatapults}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-catapults [get]
// @Security Bearer
func (e TCatapults) GetPage(c *gin.Context) {
    req := dto.TCatapultsGetPageReq{}
    s := service.TCatapults{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TCatapults, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取弹弓 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取弹弓
// @Summary 获取弹弓
// @Description 获取弹弓
// @Tags 弹弓
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TCatapults} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-catapults/{id} [get]
// @Security Bearer
func (e TCatapults) Get(c *gin.Context) {
	req := dto.TCatapultsGetReq{}
	s := service.TCatapults{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TCatapults

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取弹弓失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建弹弓
// @Summary 创建弹弓
// @Description 创建弹弓
// @Tags 弹弓
// @Accept application/json
// @Product application/json
// @Param data body dto.TCatapultsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-catapults [post]
// @Security Bearer
func (e TCatapults) Insert(c *gin.Context) {
    req := dto.TCatapultsInsertReq{}
    s := service.TCatapults{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建弹弓  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改弹弓
// @Summary 修改弹弓
// @Description 修改弹弓
// @Tags 弹弓
// @Accept application/json
// @Product application/json
// @Param data body dto.TCatapultsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-catapults/{id} [put]
// @Security Bearer
func (e TCatapults) Update(c *gin.Context) {
    req := dto.TCatapultsUpdateReq{}
    s := service.TCatapults{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改弹弓 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除弹弓
// @Summary 删除弹弓
// @Description 删除弹弓
// @Tags 弹弓
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-catapults [delete]
// @Security Bearer
func (e TCatapults) Delete(c *gin.Context) {
    s := service.TCatapults{}
    req := dto.TCatapultsDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除弹弓失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}