package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TCastReels struct {
	api.Api
}

// GetPage 获取水滴轮列表
// @Summary 获取水滴轮列表
// @Description 获取水滴轮列表
// @Tags 水滴轮
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param brand query string false "品牌"
// @Param gearRatio query string false "传动比"
// @Param recovery query string false "收线速度（cm）"
// @Param lineCapacity query string false "绕线量(mm/m)"
// @Param maxDrag query string false "最大拉力（kg）"
// @Param ballBearings query string false "滚珠轴承"
// @Param weight query string false "重量（g）"
// @Param brake query string false "制动器"
// @Param requiredLevel query string false "解锁等级"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param clubCurrency query string false "俱乐部币"
// @Param technology query string false "技术"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述翻译"
// @Param remark query string false "备注"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TCastReels}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-cast-reels [get]
// @Security Bearer
func (e TCastReels) GetPage(c *gin.Context) {
    req := dto.TCastReelsGetPageReq{}
    s := service.TCastReels{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TCastReels, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取水滴轮 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取水滴轮
// @Summary 获取水滴轮
// @Description 获取水滴轮
// @Tags 水滴轮
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TCastReels} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-cast-reels/{id} [get]
// @Security Bearer
func (e TCastReels) Get(c *gin.Context) {
	req := dto.TCastReelsGetReq{}
	s := service.TCastReels{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TCastReels

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取水滴轮失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建水滴轮
// @Summary 创建水滴轮
// @Description 创建水滴轮
// @Tags 水滴轮
// @Accept application/json
// @Product application/json
// @Param data body dto.TCastReelsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-cast-reels [post]
// @Security Bearer
func (e TCastReels) Insert(c *gin.Context) {
    req := dto.TCastReelsInsertReq{}
    s := service.TCastReels{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建水滴轮  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改水滴轮
// @Summary 修改水滴轮
// @Description 修改水滴轮
// @Tags 水滴轮
// @Accept application/json
// @Product application/json
// @Param data body dto.TCastReelsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-cast-reels/{id} [put]
// @Security Bearer
func (e TCastReels) Update(c *gin.Context) {
    req := dto.TCastReelsUpdateReq{}
    s := service.TCastReels{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改水滴轮 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除水滴轮
// @Summary 删除水滴轮
// @Description 删除水滴轮
// @Tags 水滴轮
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-cast-reels [delete]
// @Security Bearer
func (e TCastReels) Delete(c *gin.Context) {
    s := service.TCastReels{}
    req := dto.TCastReelsDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除水滴轮失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}