package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TFishInformation struct {
	api.Api
}

// GetPage 获取鱼信息列表
// @Summary 获取鱼信息列表
// @Description 获取鱼信息列表
// @Tags 鱼信息
// @Param id query int false "主键id"
// @Param enName query string false "英文名称"
// @Param cnName query string false "中文名称"
// @Param species query string false "种类"
// @Param imageUrl query string false "图片地址"
// @Param wikiImageUrl query string false "wiki图片地址"
// @Param rule query string false "规则"
// @Param weight query string false "重量(max)"
// @Param reward query string false "单价(kg)"
// @Param length query string false "长度(cm)"
// @Param baits query string false "诱饵"
// @Param cnBaits query string false "诱饵翻译"
// @Param lures query string false "假饵"
// @Param cnLures query string false "假饵翻译"
// @Param haunt query string false "出没"
// @Param scene query string false "场景"
// @Param area query string false "区域"
// @Param water query string false "水域"
// @Param food query string false "食物"
// @Param feature query string false "特征"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TFishInformation}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fish-information [get]
// @Security Bearer
func (e TFishInformation) GetPage(c *gin.Context) {
    req := dto.TFishInformationGetPageReq{}
    s := service.TFishInformation{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFishInformation, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼信息 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取鱼信息
// @Summary 获取鱼信息
// @Description 获取鱼信息
// @Tags 鱼信息
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TFishInformation} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fish-information/{id} [get]
// @Security Bearer
func (e TFishInformation) Get(c *gin.Context) {
	req := dto.TFishInformationGetReq{}
	s := service.TFishInformation{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TFishInformation

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼信息失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建鱼信息
// @Summary 创建鱼信息
// @Description 创建鱼信息
// @Tags 鱼信息
// @Accept application/json
// @Product application/json
// @Param data body dto.TFishInformationInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-fish-information [post]
// @Security Bearer
func (e TFishInformation) Insert(c *gin.Context) {
    req := dto.TFishInformationInsertReq{}
    s := service.TFishInformation{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建鱼信息  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改鱼信息
// @Summary 修改鱼信息
// @Description 修改鱼信息
// @Tags 鱼信息
// @Accept application/json
// @Product application/json
// @Param data body dto.TFishInformationUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-fish-information/{id} [put]
// @Security Bearer
func (e TFishInformation) Update(c *gin.Context) {
    req := dto.TFishInformationUpdateReq{}
    s := service.TFishInformation{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改鱼信息 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除鱼信息
// @Summary 删除鱼信息
// @Description 删除鱼信息
// @Tags 鱼信息
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-fish-information [delete]
// @Security Bearer
func (e TFishInformation) Delete(c *gin.Context) {
    s := service.TFishInformation{}
    req := dto.TFishInformationDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除鱼信息失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}