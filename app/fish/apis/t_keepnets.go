package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TKeepnets struct {
	api.Api
}

// GetPage 获取鱼库列表
// @Summary 获取鱼库列表
// @Description 获取鱼库列表
// @Tags 鱼库
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param brand query string false "品牌"
// @Param maxSingleFish query string false "单条鱼最大重量（kg）"
// @Param maxTotalFish query string false "鱼总重量（kg）"
// @Param fishFriendly query string false "对鱼是否有害"
// @Param durability query string false "耐用性"
// @Param material query string false "材质"
// @Param requiredLevel query string false "解锁等级"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param clubCurrency query string false "俱乐部币"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述中文"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TKeepnets}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-keepnets [get]
// @Security Bearer
func (e TKeepnets) GetPage(c *gin.Context) {
    req := dto.TKeepnetsGetPageReq{}
    s := service.TKeepnets{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TKeepnets, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼库 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取鱼库
// @Summary 获取鱼库
// @Description 获取鱼库
// @Tags 鱼库
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TKeepnets} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-keepnets/{id} [get]
// @Security Bearer
func (e TKeepnets) Get(c *gin.Context) {
	req := dto.TKeepnetsGetReq{}
	s := service.TKeepnets{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TKeepnets

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼库失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建鱼库
// @Summary 创建鱼库
// @Description 创建鱼库
// @Tags 鱼库
// @Accept application/json
// @Product application/json
// @Param data body dto.TKeepnetsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-keepnets [post]
// @Security Bearer
func (e TKeepnets) Insert(c *gin.Context) {
    req := dto.TKeepnetsInsertReq{}
    s := service.TKeepnets{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建鱼库  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改鱼库
// @Summary 修改鱼库
// @Description 修改鱼库
// @Tags 鱼库
// @Accept application/json
// @Product application/json
// @Param data body dto.TKeepnetsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-keepnets/{id} [put]
// @Security Bearer
func (e TKeepnets) Update(c *gin.Context) {
    req := dto.TKeepnetsUpdateReq{}
    s := service.TKeepnets{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改鱼库 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除鱼库
// @Summary 删除鱼库
// @Description 删除鱼库
// @Tags 鱼库
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-keepnets [delete]
// @Security Bearer
func (e TKeepnets) Delete(c *gin.Context) {
    s := service.TKeepnets{}
    req := dto.TKeepnetsDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除鱼库失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}