package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TWormsInsectsBaits struct {
	api.Api
}

// GetPage 获取昆虫饵列表
// @Summary 获取昆虫饵列表
// @Description 获取昆虫饵列表
// @Tags 昆虫饵
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param targetFish query string false "目标鱼"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param quantity query string false "数量"
// @Param weight query string false "重量"
// @Param requiredLevel query string false "解锁等级"
// @Param recommendedHookSize query string false "推荐挂钩尺寸"
// @Param fitReel query string false "适配鱼轮"
// @Param fitBait query string false "适配饵类"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述翻译"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TWormsInsectsBaits}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-worms-insects-baits [get]
// @Security Bearer
func (e TWormsInsectsBaits) GetPage(c *gin.Context) {
    req := dto.TWormsInsectsBaitsGetPageReq{}
    s := service.TWormsInsectsBaits{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TWormsInsectsBaits, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取昆虫饵 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取昆虫饵
// @Summary 获取昆虫饵
// @Description 获取昆虫饵
// @Tags 昆虫饵
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TWormsInsectsBaits} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-worms-insects-baits/{id} [get]
// @Security Bearer
func (e TWormsInsectsBaits) Get(c *gin.Context) {
	req := dto.TWormsInsectsBaitsGetReq{}
	s := service.TWormsInsectsBaits{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TWormsInsectsBaits

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取昆虫饵失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建昆虫饵
// @Summary 创建昆虫饵
// @Description 创建昆虫饵
// @Tags 昆虫饵
// @Accept application/json
// @Product application/json
// @Param data body dto.TWormsInsectsBaitsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-worms-insects-baits [post]
// @Security Bearer
func (e TWormsInsectsBaits) Insert(c *gin.Context) {
    req := dto.TWormsInsectsBaitsInsertReq{}
    s := service.TWormsInsectsBaits{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建昆虫饵  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改昆虫饵
// @Summary 修改昆虫饵
// @Description 修改昆虫饵
// @Tags 昆虫饵
// @Accept application/json
// @Product application/json
// @Param data body dto.TWormsInsectsBaitsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-worms-insects-baits/{id} [put]
// @Security Bearer
func (e TWormsInsectsBaits) Update(c *gin.Context) {
    req := dto.TWormsInsectsBaitsUpdateReq{}
    s := service.TWormsInsectsBaits{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改昆虫饵 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除昆虫饵
// @Summary 删除昆虫饵
// @Description 删除昆虫饵
// @Tags 昆虫饵
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-worms-insects-baits [delete]
// @Security Bearer
func (e TWormsInsectsBaits) Delete(c *gin.Context) {
    s := service.TWormsInsectsBaits{}
    req := dto.TWormsInsectsBaitsDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除昆虫饵失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}