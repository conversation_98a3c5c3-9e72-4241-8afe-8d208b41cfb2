package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TWaistCoats struct {
	api.Api
}

// GetPage 获取马甲列表
// @Summary 获取马甲列表
// @Description 获取马甲列表
// @Tags 马甲
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param brand query string false "品牌"
// @Param material query string false "材质"
// @Param storageCapacity query string false "容量"
// @Param reels query string false "卷线器"
// @Param lines query string false "钓线"
// @Param tackles query string false "渔具"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param clubCurrency query string false "俱乐部币"
// @Param requiredLevel query string false "解锁等级"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述翻译"
// @Param remark query string false "备注"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TWaistCoats}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-waist-coats [get]
// @Security Bearer
func (e TWaistCoats) GetPage(c *gin.Context) {
    req := dto.TWaistCoatsGetPageReq{}
    s := service.TWaistCoats{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TWaistCoats, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取马甲 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取马甲
// @Summary 获取马甲
// @Description 获取马甲
// @Tags 马甲
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TWaistCoats} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-waist-coats/{id} [get]
// @Security Bearer
func (e TWaistCoats) Get(c *gin.Context) {
	req := dto.TWaistCoatsGetReq{}
	s := service.TWaistCoats{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TWaistCoats

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取马甲失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建马甲
// @Summary 创建马甲
// @Description 创建马甲
// @Tags 马甲
// @Accept application/json
// @Product application/json
// @Param data body dto.TWaistCoatsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-waist-coats [post]
// @Security Bearer
func (e TWaistCoats) Insert(c *gin.Context) {
    req := dto.TWaistCoatsInsertReq{}
    s := service.TWaistCoats{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建马甲  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改马甲
// @Summary 修改马甲
// @Description 修改马甲
// @Tags 马甲
// @Accept application/json
// @Product application/json
// @Param data body dto.TWaistCoatsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-waist-coats/{id} [put]
// @Security Bearer
func (e TWaistCoats) Update(c *gin.Context) {
    req := dto.TWaistCoatsUpdateReq{}
    s := service.TWaistCoats{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改马甲 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除马甲
// @Summary 删除马甲
// @Description 删除马甲
// @Tags 马甲
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-waist-coats [delete]
// @Security Bearer
func (e TWaistCoats) Delete(c *gin.Context) {
    s := service.TWaistCoats{}
    req := dto.TWaistCoatsDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除马甲失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}