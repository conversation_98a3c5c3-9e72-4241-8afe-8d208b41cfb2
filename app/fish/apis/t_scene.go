package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TScene struct {
	api.Api
}

// GetPage 获取场景列表
// @Summary 获取场景列表
// @Description 获取场景列表
// @Tags 场景
// @Param id query int false "主键id"
// @Param scene query string false "场景"
// @Param requiredLevel query string false "解锁等级"
// @Param travelFee query string false "旅行费"
// @Param fishingFee query string false "钓鱼费(per_day)"
// @Param basicLicenseReleased query string false "基本执照"
// @Param advancedLicenseReleased query string false "高级执照"
// @Param enFish query string false "鱼英文"
// @Param cnFish query string false "鱼中文"
// @Param speciesRule query string false "鱼规则"
// @Param gameList query string false "比赛列表"
// @Param sceneUrl query string false "场景图片"
// @Param weatherUrl query string false "天气图片"
// @Param basicLicenseReleasedUrl query string false "基本执照图片"
// @Param advancedLicenseReleasedUrl query string false "高级执照图片"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TScene}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-scene [get]
// @Security Bearer
func (e TScene) GetPage(c *gin.Context) {
    req := dto.TSceneGetPageReq{}
    s := service.TScene{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TScene, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取场景 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取场景
// @Summary 获取场景
// @Description 获取场景
// @Tags 场景
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TScene} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-scene/{id} [get]
// @Security Bearer
func (e TScene) Get(c *gin.Context) {
	req := dto.TSceneGetReq{}
	s := service.TScene{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TScene

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取场景失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建场景
// @Summary 创建场景
// @Description 创建场景
// @Tags 场景
// @Accept application/json
// @Product application/json
// @Param data body dto.TSceneInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-scene [post]
// @Security Bearer
func (e TScene) Insert(c *gin.Context) {
    req := dto.TSceneInsertReq{}
    s := service.TScene{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建场景  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改场景
// @Summary 修改场景
// @Description 修改场景
// @Tags 场景
// @Accept application/json
// @Product application/json
// @Param data body dto.TSceneUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-scene/{id} [put]
// @Security Bearer
func (e TScene) Update(c *gin.Context) {
    req := dto.TSceneUpdateReq{}
    s := service.TScene{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改场景 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除场景
// @Summary 删除场景
// @Description 删除场景
// @Tags 场景
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-scene [delete]
// @Security Bearer
func (e TScene) Delete(c *gin.Context) {
    s := service.TScene{}
    req := dto.TSceneDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除场景失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}