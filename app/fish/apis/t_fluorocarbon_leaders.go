package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/fish/models"
	"go-admin/app/fish/service"
	"go-admin/app/fish/service/dto"
	"go-admin/common/actions"
)

type TFluorocarbonLeaders struct {
	api.Api
}

// GetPage 获取碳氟前导线列表
// @Summary 获取碳氟前导线列表
// @Description 获取碳氟前导线列表
// @Tags 碳氟前导线
// @Param id query int false "主键id"
// @Param category query string false "类别"
// @Param resident query string false "常驻"
// @Param cnTaxon query string false "类名中文"
// @Param enTaxon query string false "类名英文"
// @Param imageUrl query string false "图片地址"
// @Param enName query string false "名称英文"
// @Param brand query string false "品牌"
// @Param thickness query string false "直径（mm）"
// @Param testWeight query string false "测试重量（kg）"
// @Param length query string false "长度（m）"
// @Param color query string false "颜色"
// @Param quantity query string false "数量"
// @Param requiredLevel query string false "解锁等级"
// @Param money query string false "货币"
// @Param baitCoin query string false "饵币"
// @Param clubCurrency query string false "俱乐部币"
// @Param description query string false "描述"
// @Param cnDescriptive query string false "描述翻译"
// @Param createName query string false "创建者名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TFluorocarbonLeaders}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fluorocarbon-leaders [get]
// @Security Bearer
func (e TFluorocarbonLeaders) GetPage(c *gin.Context) {
    req := dto.TFluorocarbonLeadersGetPageReq{}
    s := service.TFluorocarbonLeaders{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFluorocarbonLeaders, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取碳氟前导线 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取碳氟前导线
// @Summary 获取碳氟前导线
// @Description 获取碳氟前导线
// @Tags 碳氟前导线
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TFluorocarbonLeaders} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-fluorocarbon-leaders/{id} [get]
// @Security Bearer
func (e TFluorocarbonLeaders) Get(c *gin.Context) {
	req := dto.TFluorocarbonLeadersGetReq{}
	s := service.TFluorocarbonLeaders{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TFluorocarbonLeaders

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取碳氟前导线失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建碳氟前导线
// @Summary 创建碳氟前导线
// @Description 创建碳氟前导线
// @Tags 碳氟前导线
// @Accept application/json
// @Product application/json
// @Param data body dto.TFluorocarbonLeadersInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-fluorocarbon-leaders [post]
// @Security Bearer
func (e TFluorocarbonLeaders) Insert(c *gin.Context) {
    req := dto.TFluorocarbonLeadersInsertReq{}
    s := service.TFluorocarbonLeaders{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建碳氟前导线  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改碳氟前导线
// @Summary 修改碳氟前导线
// @Description 修改碳氟前导线
// @Tags 碳氟前导线
// @Accept application/json
// @Product application/json
// @Param data body dto.TFluorocarbonLeadersUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-fluorocarbon-leaders/{id} [put]
// @Security Bearer
func (e TFluorocarbonLeaders) Update(c *gin.Context) {
    req := dto.TFluorocarbonLeadersUpdateReq{}
    s := service.TFluorocarbonLeaders{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改碳氟前导线 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除碳氟前导线
// @Summary 删除碳氟前导线
// @Description 删除碳氟前导线
// @Tags 碳氟前导线
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-fluorocarbon-leaders [delete]
// @Security Bearer
func (e TFluorocarbonLeaders) Delete(c *gin.Context) {
    s := service.TFluorocarbonLeaders{}
    req := dto.TFluorocarbonLeadersDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除碳氟前导线失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}