package models

import (
	"go-admin/common/models"
)

type TFishInfo struct {
	models.Model

	ChName     string `json:"chName" gorm:"type:varchar(128);comment:中文名称"`
	EnName     string `json:"enName" gorm:"type:varchar(128);comment:英文名称"`
	Rule       string `json:"rule" gorm:"type:varchar(64);comment:规则"`
	Weight     string `json:"weight" gorm:"type:varchar(128);comment:最大KG(LB)"`
	Length     string `json:"length" gorm:"type:int(11);comment:长度(CM)"`
	Price      string `json:"price" gorm:"type:int(11);comment:价格/KG"`
	BaitType   string `json:"baitType" gorm:"type:varchar(128);comment:鱼饵类型"`
	LureType   string `json:"lureType" gorm:"type:varchar(128);comment:假饵类型"`
	LiveArea   string `json:"liveArea" gorm:"type:varchar(128);comment:生活区域"`
	Food       string `json:"food" gorm:"type:varchar(128);comment:食物"`
	Feature    string `json:"feature" gorm:"type:varchar(128);comment:其他特性"`
	Scene      string `json:"scene" gorm:"type:varchar(128);comment:出没场景"`
	ImageUrl   string `json:"imageUrl" gorm:"type:varchar(256);comment:图片地址"`
	CreateName string `json:"createName" gorm:"type:text;comment:创建者名称"`
	models.ModelTime
	models.ControlBy
}

func (TFishInfo) TableName() string {
	return "t_fish_info"
}

func (e *TFishInfo) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TFishInfo) GetId() interface{} {
	return e.Id
}
