package models

import (
	"go-admin/common/models"
)

type TPlantParams struct {
	models.Model

	PlantId    int64  `json:"plantId" gorm:"type:bigint;comment:plant"`
	ParamKey   string `json:"paramKey" gorm:"type:varchar(255);comment:参数主键"`
	ParamValue string `json:"paramValue" gorm:"type:varchar(255);comment:参数值"`
	models.ModelTime
	models.ControlBy
}

func (TPlantParams) TableName() string {
	return "t_plant_params"
}

func (e *TPlantParams) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TPlantParams) GetId() interface{} {
	return e.Id
}
