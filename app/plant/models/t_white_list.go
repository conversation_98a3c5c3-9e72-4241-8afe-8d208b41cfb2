package models

import (
	"go-admin/common/models"
)

type TWhiteList struct {
	models.Model

	PlanetId   int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	Uid        string `json:"uid" gorm:"type:varchar(20);comment:用户ID"`
	Developer  bool   `json:"developer" gorm:"type:tinyint(1);comment:开发者"`
	TesterPay  bool   `json:"testerPay" gorm:"type:tinyint(1);comment:测试人员_支付"`
	TesterGray bool   `json:"testerGray" gorm:"type:tinyint(1);comment:测试人员_灰度"`
	Remark     string `json:"remark" gorm:"type:varchar(255);comment:备注"`
	models.ModelTime
	models.ControlBy
}

func (TWhiteList) TableName() string {
	return "t_white_list"
}

func (e *TWhiteList) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TWhiteList) GetId() interface{} {
	return e.Id
}
