package apis

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/app/plant/models"
	"go-admin/app/plant/service"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"
	"io"
)

type TFishInfo struct {
	api.Api
}

// GetPage 获取鱼种信息列表
// @Summary 获取鱼种信息列表
// @Description 获取鱼种信息列表
// @Tags 鱼种信息
// @Param chName query string false "中文名称"
// @Param enName query string false "英文名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TFishInfo}} "{"code": 200, "data": [...]}"
// @Router /api/v1/fishinfo [get]
// @Security Bearer
func (e TFishInfo) GetPage(c *gin.Context) {
	req := dto.TFishInfoGetPageReq{}
	s := service.TFishInfo{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TFishInfo, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼种信息 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取鱼种信息
// @Summary 获取鱼种信息
// @Description 获取鱼种信息
// @Tags 鱼种信息
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TFishInfo} "{"code": 200, "data": [...]}"
// @Router /api/v1/fishinfo/{id} [get]
// @Security Bearer
func (e TFishInfo) Get(c *gin.Context) {
	req := dto.TFishInfoGetReq{}
	s := service.TFishInfo{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TFishInfo

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取鱼种信息失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建鱼种信息
// @Summary 创建鱼种信息
// @Description 创建鱼种信息
// @Tags 鱼种信息
// @Accept application/json
// @Product application/json
// @Param data body dto.TFishInfoInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/fishinfo [post]
// @Security Bearer
func (e TFishInfo) Insert(c *gin.Context) {
	req := dto.TFishInfoInsertReq{}
	s := service.TFishInfo{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建鱼种信息  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改鱼种信息
// @Summary 修改鱼种信息
// @Description 修改鱼种信息
// @Tags 鱼种信息
// @Accept application/json
// @Product application/json
// @Param data body dto.TFishInfoUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/fishinfo/{id} [put]
// @Security Bearer
func (e TFishInfo) Update(c *gin.Context) {
	req := dto.TFishInfoUpdateReq{}
	s := service.TFishInfo{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改鱼种信息 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除鱼种信息
// @Summary 删除鱼种信息
// @Description 删除鱼种信息
// @Tags 鱼种信息
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/fishinfo [delete]
// @Security Bearer
func (e TFishInfo) Delete(c *gin.Context) {
	s := service.TFishInfo{}
	req := dto.TFishInfoDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除鱼种信息失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}

// excel的数据导入到数据库
func (e TFishInfo) InsertExcelData(c *gin.Context) {
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, fmt.Sprintf("导入数据失败，\r\n失败信息 %s", err.Error()))
		return
	}

	dataList := make([]dto.TFishInfoInsertReq, 100)
	err = json.Unmarshal([]byte(body), &dataList)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, fmt.Sprintf("解析请求数据失败，\r\n失败信息 %s", err.Error()))
		return
	}

	db, err := e.MakeContext(c).MakeOrm().GetOrm()
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, fmt.Sprintf("获取db链接失败，\r\n失败信息 %s", err.Error()))
		return
	}

	var data models.TFishInfo
	db = db.Table(data.TableName())
	result := db.Create(&dataList)
	if result.Error != nil {
		e.Logger.Error(result.Error)
		e.Error(500, err, fmt.Sprintf("导入数据失败，\r\n失败信息 %s", result.Error.Error()))
		return
	}

	req := dto.TFishInfoInsertReq{}
	e.OK(req.GetId(), fmt.Sprintf("导入成功%d条数据", result.RowsAffected))
}
