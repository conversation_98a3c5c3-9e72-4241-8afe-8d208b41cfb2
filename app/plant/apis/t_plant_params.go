package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/plant/models"
	"go-admin/app/plant/service"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
)

type TPlantParams struct {
	api.Api
}

// GetPage 获取TPlantParams列表
// @Summary 获取TPlantParams列表
// @Description 获取TPlantParams列表
// @Tags TPlantParams
// @Param paramKey query string false "参数主键"
// @Param paramValue query string false "参数值"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TPlantParams}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-plant-params [get]
// @Security Bearer
func (e TPlantParams) GetPage(c *gin.Context) {
	req := dto.TPlantParamsGetPageReq{}
	s := service.TPlantParams{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TPlantParams, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TPlantParams 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TPlantParams
// @Summary 获取TPlantParams
// @Description 获取TPlantParams
// @Tags TPlantParams
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TPlantParams} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-plant-params/{id} [get]
// @Security Bearer
func (e TPlantParams) Get(c *gin.Context) {
	req := dto.TPlantParamsGetReq{}
	s := service.TPlantParams{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TPlantParams

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TPlantParams失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TPlantParams
// @Summary 创建TPlantParams
// @Description 创建TPlantParams
// @Tags TPlantParams
// @Accept application/json
// @Product application/json
// @Param data body dto.TPlantParamsInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-plant-params [post]
// @Security Bearer
func (e TPlantParams) Insert(c *gin.Context) {
	req := dto.TPlantParamsInsertReq{}
	s := service.TPlantParams{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TPlantParams  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TPlantParams
// @Summary 修改TPlantParams
// @Description 修改TPlantParams
// @Tags TPlantParams
// @Accept application/json
// @Product application/json
// @Param data body dto.TPlantParamsUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-plant-params/{id} [put]
// @Security Bearer
func (e TPlantParams) Update(c *gin.Context) {
	req := dto.TPlantParamsUpdateReq{}
	s := service.TPlantParams{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TPlantParams 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TPlantParams
// @Summary 删除TPlantParams
// @Description 删除TPlantParams
// @Tags TPlantParams
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-plant-params [delete]
// @Security Bearer
func (e TPlantParams) Delete(c *gin.Context) {
	s := service.TPlantParams{}
	req := dto.TPlantParamsDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TPlantParams失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
