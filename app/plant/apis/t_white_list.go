package apis

import (
	"fmt"
	"go-admin/common/rpc"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/plant/models"
	"go-admin/app/plant/service"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
)

type TWhiteList struct {
	api.Api
}

// GetPage 获取TWhiteList列表
// @Summary 获取TWhiteList列表
// @Description 获取TWhiteList列表
// @Tags TWhiteList
// @Param plantId query string false "plant"
// @Param uid query string false "用户ID"
// @Param developer query string false "开发者"
// @Param testerPay query string false "测试人员_支付"
// @Param testerGray query string false "测试人员_灰度"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TWhiteList}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-white-list [get]
// @Security Bearer
func (e TWhiteList) GetPage(c *gin.Context) {
	req := dto.TWhiteListGetPageReq{}
	s := service.TWhiteList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TWhiteList, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TWhiteList 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TWhiteList
// @Summary 获取TWhiteList
// @Description 获取TWhiteList
// @Tags TWhiteList
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TWhiteList} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-white-list/{id} [get]
// @Security Bearer
func (e TWhiteList) Get(c *gin.Context) {
	req := dto.TWhiteListGetReq{}
	s := service.TWhiteList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TWhiteList

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TWhiteList失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TWhiteList
// @Summary 创建TWhiteList
// @Description 创建TWhiteList
// @Tags TWhiteList
// @Accept application/json
// @Product application/json
// @Param data body dto.TWhiteListInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-white-list [post]
// @Security Bearer
func (e TWhiteList) Insert(c *gin.Context) {
	req := dto.TWhiteListInsertReq{}
	s := service.TWhiteList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("新建白名单失败，\r\n失败信息 %s", err.Error()))
		return
	}

	rpcErr := e.RpcGameGm(req.Uid, req.Developer, req.TesterPay, req.TesterGray, c)
	if rpcErr != nil {
		e.Error(500, err, err.Error())
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TWhiteList
// @Summary 修改TWhiteList
// @Description 修改TWhiteList
// @Tags TWhiteList
// @Accept application/json
// @Product application/json
// @Param data body dto.TWhiteListUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-white-list/{id} [put]
// @Security Bearer
func (e TWhiteList) Update(c *gin.Context) {
	req := dto.TWhiteListUpdateReq{}
	s := service.TWhiteList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	rpcErr := e.RpcGameGm(req.Uid, req.Developer, req.TesterPay, req.TesterGray, c)
	if rpcErr != nil {
		e.Error(500, err, err.Error())
		return
	}

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TWhiteList 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TWhiteList
// @Summary 删除TWhiteList
// @Description 删除TWhiteList
// @Tags TWhiteList
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-white-list [delete]
// @Security Bearer
func (e TWhiteList) Delete(c *gin.Context) {
	s := service.TWhiteList{}
	req := dto.TWhiteListDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	reqQuery := dto.TWhiteListGetReq{}
	reqQuery.Id = req.Ids[0] //不支持全选删除
	var object models.TWhiteList
	err = s.Get(&reqQuery, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TWhiteList失败，\r\n失败信息 %s", err.Error()))
		return
	}

	rpcErr := e.RpcGameGm(object.Uid, false, false, false, c)
	if rpcErr != nil {
		e.Error(500, err, err.Error())
		return
	}

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TWhiteList失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}

func (e *TWhiteList) RpcGameGm(uid string, isDev bool, isPay bool, isGray bool, c *gin.Context) error {
	// RPC到游戏服
	_, object, err := rpc.RpcGm2Game(rpc.Req{
		GmCmd:        "4000073",
		GmUid:        "0",
		UID:          uid,
		IsDevTester:  isDev,
		IsPayTester:  isPay,
		IsGrayTester: isGray,
	}, c)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("Rpc Game服务失败，\r\n信息 %s", err.Error()))
		return err
	}

	e.Logger.Info(object, "GM执行成功")

	return nil
}
