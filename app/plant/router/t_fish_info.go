package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/plant/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTFishInfoRouter)
}

// registerTFishInfoRouter
func registerTFishInfoRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TFishInfo{}
	r := v1.Group("/fishinfo").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
		r.DELETE("", api.Delete)
	}

	//导入db
	r1 := v1.Group("/fishinfo2Db").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r1.POST("", api.InsertExcelData)
	}
}
