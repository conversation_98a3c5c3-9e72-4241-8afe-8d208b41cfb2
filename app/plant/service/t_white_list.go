package service

import (
	"errors"
	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/plant/models"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TWhiteList struct {
	service.Service
}

// GetPage 获取TWhiteList列表
func (e *TWhiteList) GetPage(c *dto.TWhiteListGetPageReq, p *actions.DataPermission, list *[]models.TWhiteList, count *int64) error {
	var err error
	var data models.TWhiteList

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TWhiteListService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TWhiteList对象
func (e *TWhiteList) Get(d *dto.TWhiteListGetReq, p *actions.DataPermission, model *models.TWhiteList) error {
	var data models.TWhiteList

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTWhiteList error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TWhiteList对象
func (e *TWhiteList) Insert(c *dto.TWhiteListInsertReq) error {
	var err error
	var data models.TWhiteList
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TWhiteListService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TWhiteList对象
func (e *TWhiteList) Update(c *dto.TWhiteListUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TWhiteList{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TWhiteListService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TWhiteList
func (e *TWhiteList) Remove(d *dto.TWhiteListDeleteReq, p *actions.DataPermission) error {
	var data models.TWhiteList

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTWhiteList error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
