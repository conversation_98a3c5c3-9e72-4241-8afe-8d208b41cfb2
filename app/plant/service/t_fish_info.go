package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/plant/models"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TFishInfo struct {
	service.Service
}

// GetPage 获取TFishInfo列表
func (e *TFishInfo) GetPage(c *dto.TFishInfoGetPageReq, p *actions.DataPermission, list *[]models.TFishInfo, count *int64) error {
	var err error
	var data models.TFishInfo

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TFishInfoService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TFishInfo对象
func (e *TFishInfo) Get(d *dto.TFishInfoGetReq, p *actions.DataPermission, model *models.TFishInfo) error {
	var data models.TFishInfo

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTFishInfo error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TFishInfo对象
func (e *TFishInfo) Insert(c *dto.TFishInfoInsertReq) error {
	var err error
	var data models.TFishInfo
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TFishInfoService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TFishInfo对象
func (e *TFishInfo) Update(c *dto.TFishInfoUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TFishInfo{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TFishInfoService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TFishInfo
func (e *TFishInfo) Remove(d *dto.TFishInfoDeleteReq, p *actions.DataPermission) error {
	var data models.TFishInfo

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTFishInfo error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
