package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/plant/models"
	"go-admin/app/plant/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TPlantParams struct {
	service.Service
}

// GetPage 获取TPlantParams列表
func (e *TPlantParams) GetPage(c *dto.TPlantParamsGetPageReq, p *actions.DataPermission, list *[]models.TPlantParams, count *int64) error {
	var err error
	var data models.TPlantParams

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TPlantParamsService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TPlantParams对象
func (e *TPlantParams) Get(d *dto.TPlantParamsGetReq, p *actions.DataPermission, model *models.TPlantParams) error {
	var data models.TPlantParams

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTPlantParams error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TPlantParams对象
func (e *TPlantParams) Insert(c *dto.TPlantParamsInsertReq) error {
	var err error
	var data models.TPlantParams
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TPlantParamsService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TPlantParams对象
func (e *TPlantParams) Update(c *dto.TPlantParamsUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TPlantParams{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TPlantParamsService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TPlantParams
func (e *TPlantParams) Remove(d *dto.TPlantParamsDeleteReq, p *actions.DataPermission) error {
	var data models.TPlantParams

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTPlantParams error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
