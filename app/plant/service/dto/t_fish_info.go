package dto

import (
	"time"

	"go-admin/app/plant/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TFishInfoGetPageReq struct {
	dto.Pagination `search:"-"`
	ChName         string `form:"chName"  search:"type:contains;column:ch_name;table:t_fish_info" comment:"中文名称"`
	EnName         string `form:"enName"  search:"type:contains;column:en_name;table:t_fish_info" comment:"英文名称"`
	TFishInfoOrder
}

type TFishInfoOrder struct {
	Id         int       `form:"idOrder"  search:"type:order;column:id;table:t_fish_info"`
	ChName     string    `form:"chNameOrder"  search:"type:order;column:ch_name;table:t_fish_info"`
	EnName     string    `form:"enNameOrder"  search:"type:order;column:en_name;table:t_fish_info"`
	Rule       string    `form:"ruleOrder"  search:"type:order;column:rule;table:t_fish_info"`
	Weight     string    `form:"weightOrder"  search:"type:order;column:weight;table:t_fish_info"`
	Length     string    `form:"lengthOrder"  search:"type:order;column:length;table:t_fish_info"`
	Price      string    `form:"priceOrder"  search:"type:order;column:price;table:t_fish_info"`
	BaitType   string    `form:"baitTypeOrder"  search:"type:order;column:bait_type;table:t_fish_info"`
	LureType   string    `form:"lureTypeOrder"  search:"type:order;column:lure_type;table:t_fish_info"`
	LiveArea   string    `form:"liveAreaOrder"  search:"type:order;column:live_area;table:t_fish_info"`
	Food       string    `form:"foodOrder"  search:"type:order;column:food;table:t_fish_info"`
	Feature    string    `form:"featureOrder"  search:"type:order;column:feature;table:t_fish_info"`
	Scene      string    `form:"sceneOrder"  search:"type:order;column:scene;table:t_fish_info"`
	ImageUrl   string    `form:"imageUrlOrder"  search:"type:order;column:image_url;table:t_fish_info"`
	CreateName string    `form:"createNameOrder"  search:"type:order;column:create_name;table:t_fish_info"`
	UpdateBy   string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_fish_info"`
	CreateBy   string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_fish_info"`
	CreatedAt  time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_fish_info"`
	UpdatedAt  time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_fish_info"`
	DeletedAt  time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_fish_info"`
}

func (m *TFishInfoGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TFishInfoInsertReq struct {
	Id         int    `json:"-" comment:"主键编码"` // 主键编码
	ChName     string `json:"chName" comment:"中文名称"`
	EnName     string `json:"enName" comment:"英文名称"`
	Rule       string `json:"rule" comment:"规则"`
	Weight     string `json:"weight" comment:"最大KG(LB)"`
	Length     string `json:"length" comment:"长度(CM)"`
	Price      string `json:"price" comment:"价格/KG"`
	BaitType   string `json:"baitType" comment:"鱼饵类型"`
	LureType   string `json:"lureType" comment:"假饵类型"`
	LiveArea   string `json:"liveArea" comment:"生活区域"`
	Food       string `json:"food" comment:"食物"`
	Feature    string `json:"feature" comment:"其他特性"`
	Scene      string `json:"scene" comment:"出没场景"`
	ImageUrl   string `json:"imageUrl" comment:"图片地址"`
	CreateName string `json:"createName" comment:"创建者名称"`
	common.ControlBy
}

func (s *TFishInfoInsertReq) Generate(model *models.TFishInfo) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.ChName = s.ChName
	model.EnName = s.EnName
	model.Rule = s.Rule
	model.Weight = s.Weight
	model.Length = s.Length
	model.Price = s.Price
	model.BaitType = s.BaitType
	model.LureType = s.LureType
	model.LiveArea = s.LiveArea
	model.Food = s.Food
	model.Feature = s.Feature
	model.Scene = s.Scene
	model.ImageUrl = s.ImageUrl
	model.CreateName = s.CreateName
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TFishInfoInsertReq) GetId() interface{} {
	return s.Id
}

type TFishInfoUpdateReq struct {
	Id         int    `uri:"id" comment:"主键编码"` // 主键编码
	ChName     string `json:"chName" comment:"中文名称"`
	EnName     string `json:"enName" comment:"英文名称"`
	Rule       string `json:"rule" comment:"规则"`
	Weight     string `json:"weight" comment:"最大KG(LB)"`
	Length     string `json:"length" comment:"长度(CM)"`
	Price      string `json:"price" comment:"价格/KG"`
	BaitType   string `json:"baitType" comment:"鱼饵类型"`
	LureType   string `json:"lureType" comment:"假饵类型"`
	LiveArea   string `json:"liveArea" comment:"生活区域"`
	Food       string `json:"food" comment:"食物"`
	Feature    string `json:"feature" comment:"其他特性"`
	Scene      string `json:"scene" comment:"出没场景"`
	ImageUrl   string `json:"imageUrl" comment:"图片地址"`
	CreateName string `json:"createName" comment:"创建者名称"`
	common.ControlBy
}

func (s *TFishInfoUpdateReq) Generate(model *models.TFishInfo) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.ChName = s.ChName
	model.EnName = s.EnName
	model.Rule = s.Rule
	model.Weight = s.Weight
	model.Length = s.Length
	model.Price = s.Price
	model.BaitType = s.BaitType
	model.LureType = s.LureType
	model.LiveArea = s.LiveArea
	model.Food = s.Food
	model.Feature = s.Feature
	model.Scene = s.Scene
	model.ImageUrl = s.ImageUrl
	model.CreateName = s.CreateName
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TFishInfoUpdateReq) GetId() interface{} {
	return s.Id
}

// TFishInfoGetReq 功能获取请求参数
type TFishInfoGetReq struct {
	Id int `uri:"id"`
}

func (s *TFishInfoGetReq) GetId() interface{} {
	return s.Id
}

// TFishInfoDeleteReq 功能删除请求参数
type TFishInfoDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TFishInfoDeleteReq) GetId() interface{} {
	return s.Ids
}
