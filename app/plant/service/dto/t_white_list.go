package dto

import (
	"time"

	"go-admin/app/plant/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TWhiteListGetPageReq struct {
	dto.Pagination `search:"-"`
	PlantId        int64  `form:"planetId"  search:"type:exact;column:planet_id;table:t_white_list" comment:"plant"`
	Uid            string `form:"uid"  search:"type:exact;column:uid;table:t_white_list" comment:"用户ID"`
	Developer      bool   `form:"developer"  search:"type:exact;column:developer;table:t_white_list" comment:"开发者"`
	TesterPay      bool   `form:"testerPay"  search:"type:exact;column:tester_pay;table:t_white_list" comment:"测试人员_支付"`
	TesterGray     bool   `form:"testerGray"  search:"type:exact;column:tester_gray;table:t_white_list" comment:"测试人员_灰度"`
	TWhiteListOrder
}

type TWhiteListOrder struct {
	Id         int       `form:"idOrder"  search:"type:order;column:id;table:t_white_list"`
	PlanetId   int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_white_list"`
	Uid        string    `form:"uidOrder"  search:"type:order;column:uid;table:t_white_list"`
	Developer  bool      `form:"developerOrder"  search:"type:order;column:developer;table:t_white_list"`
	TesterPay  bool      `form:"testerPayOrder"  search:"type:order;column:tester_pay;table:t_white_list"`
	TesterGray bool      `form:"testerGrayOrder"  search:"type:order;column:tester_gray;table:t_white_list"`
	Remark     string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_white_list"`
	CreateBy   string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_white_list"`
	UpdateBy   string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_white_list"`
	CreatedAt  time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_white_list"`
	UpdatedAt  time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_white_list"`
	DeletedAt  time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_white_list"`
}

func (m *TWhiteListGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TWhiteListInsertReq struct {
	Id         int    `json:"-" comment:""` //
	PlantId    int64  `json:"planetId" comment:"plant"`
	Uid        string `json:"uid" comment:"用户ID"`
	Developer  bool   `json:"developer" comment:"开发者"`
	TesterPay  bool   `json:"testerPay" comment:"测试人员_支付"`
	TesterGray bool   `json:"testerGray" comment:"测试人员_灰度"`
	Remark     string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TWhiteListInsertReq) Generate(model *models.TWhiteList) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlantId
	model.Uid = s.Uid
	model.Developer = s.Developer
	model.TesterPay = s.TesterPay
	model.TesterGray = s.TesterGray
	model.Remark = s.Remark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TWhiteListInsertReq) GetId() interface{} {
	return s.Id
}

type TWhiteListUpdateReq struct {
	Id         int    `uri:"id" comment:""` //
	PlantId    int64  `json:"planetId" comment:"plant"`
	Uid        string `json:"uid" comment:"用户ID"`
	Developer  bool   `json:"developer" comment:"开发者"`
	TesterPay  bool   `json:"testerPay" comment:"测试人员_支付"`
	TesterGray bool   `json:"testerGray" comment:"测试人员_灰度"`
	Remark     string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TWhiteListUpdateReq) Generate(model *models.TWhiteList) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlantId
	model.Uid = s.Uid
	model.Developer = s.Developer
	model.TesterPay = s.TesterPay
	model.TesterGray = s.TesterGray
	model.Remark = s.Remark
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TWhiteListUpdateReq) GetId() interface{} {
	return s.Id
}

// TWhiteListGetReq 功能获取请求参数
type TWhiteListGetReq struct {
	Id int `uri:"id"`
}

func (s *TWhiteListGetReq) GetId() interface{} {
	return s.Id
}

// TWhiteListDeleteReq 功能删除请求参数
type TWhiteListDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TWhiteListDeleteReq) GetId() interface{} {
	return s.Ids
}
