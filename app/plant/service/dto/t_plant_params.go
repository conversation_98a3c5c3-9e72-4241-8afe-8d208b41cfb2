package dto

import (
	"time"

	"go-admin/app/plant/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TPlantParamsGetPageReq struct {
	dto.Pagination `search:"-"`
	PlantId        int64  `form:"plantId"  search:"type:exact;column:plant_id;table:t_plant_params" comment:"planet"`
	ParamKey       string `form:"paramKey"  search:"type:contains;column:param_key;table:t_plant_params" comment:"参数主键"`
	ParamValue     string `form:"paramValue"  search:"type:contains;column:param_value;table:t_plant_params" comment:"参数值"`
	TPlantParamsOrder
}

type TPlantParamsOrder struct {
	Id         int       `form:"idOrder"  search:"type:order;column:id;table:t_plant_params"`
	PlantId    string    `form:"plantIdOrder"  search:"type:order;column:planet_id;table:t_plant_params"`
	ParamKey   string    `form:"paramKeyOrder"  search:"type:order;column:param_key;table:t_plant_params"`
	ParamValue string    `form:"paramValueOrder"  search:"type:order;column:param_value;table:t_plant_params"`
	CreateBy   string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_plant_params"`
	UpdateBy   string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_plant_params"`
	CreatedAt  time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_plant_params"`
	UpdatedAt  time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_plant_params"`
	DeletedAt  time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_plant_params"`
}

func (m *TPlantParamsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TPlantParamsInsertReq struct {
	Id         int    `json:"-" comment:""` //
	PlantId    int64  `json:"plantId" comment:"plant"`
	ParamKey   string `json:"paramKey" comment:"参数主键"`
	ParamValue string `json:"paramValue" comment:"参数值"`
	common.ControlBy
}

func (s *TPlantParamsInsertReq) Generate(model *models.TPlantParams) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlantId = s.PlantId
	model.ParamKey = s.ParamKey
	model.ParamValue = s.ParamValue
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TPlantParamsInsertReq) GetId() interface{} {
	return s.Id
}

type TPlantParamsUpdateReq struct {
	Id         int    `uri:"id" comment:""` //
	PlantId    int64  `json:"plantId" comment:"plant"`
	ParamKey   string `json:"paramKey" comment:"参数主键"`
	ParamValue string `json:"paramValue" comment:"参数值"`
	common.ControlBy
}

func (s *TPlantParamsUpdateReq) Generate(model *models.TPlantParams) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlantId = s.PlantId
	model.ParamKey = s.ParamKey
	model.ParamValue = s.ParamValue
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TPlantParamsUpdateReq) GetId() interface{} {
	return s.Id
}

// TPlantParamsGetReq 功能获取请求参数
type TPlantParamsGetReq struct {
	Id int `uri:"id"`
}

func (s *TPlantParamsGetReq) GetId() interface{} {
	return s.Id
}

// TPlantParamsDeleteReq 功能删除请求参数
type TPlantParamsDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TPlantParamsDeleteReq) GetId() interface{} {
	return s.Ids
}
