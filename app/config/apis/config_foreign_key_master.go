package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"
)

type ConfigForeignKeyMaster struct {
	api.Api
}

// GetPage 获取ConfigForeignKeyMaster列表
// @Summary 获取ConfigForeignKeyMaster列表
// @Description 获取ConfigForeignKeyMaster列表
// @Tags ConfigForeignKeyMaster
// @Param sheet query string false "sheet"
// @Param sheetName query string false "sheet"
// @Param field query string false "field"
// @Param fieldName query string false "field name"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.ConfigForeignKeyMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-foreign-key-master [get]
// @Security Bearer
func (e ConfigForeignKeyMaster) GetPage(c *gin.Context) {
	req := dto.ConfigForeignKeyMasterGetPageReq{}
	s := service.ConfigForeignKeyMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.ConfigForeignKeyMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigForeignKeyMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取ConfigForeignKeyMaster
// @Summary 获取ConfigForeignKeyMaster
// @Description 获取ConfigForeignKeyMaster
// @Tags ConfigForeignKeyMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.ConfigForeignKeyMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-foreign-key-master/{id} [get]
// @Security Bearer
func (e ConfigForeignKeyMaster) Get(c *gin.Context) {
	req := dto.ConfigForeignKeyMasterGetReq{}
	s := service.ConfigForeignKeyMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.ConfigForeignKeyMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigForeignKeyMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建ConfigForeignKeyMaster
// @Summary 创建ConfigForeignKeyMaster
// @Description 创建ConfigForeignKeyMaster
// @Tags ConfigForeignKeyMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigForeignKeyMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/config-foreign-key-master [post]
// @Security Bearer
func (e ConfigForeignKeyMaster) Insert(c *gin.Context) {
	req := dto.ConfigForeignKeyMasterInsertReq{}
	s := service.ConfigForeignKeyMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建ConfigForeignKeyMaster  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改ConfigForeignKeyMaster
// @Summary 修改ConfigForeignKeyMaster
// @Description 修改ConfigForeignKeyMaster
// @Tags ConfigForeignKeyMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigForeignKeyMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/config-foreign-key-master/{id} [put]
// @Security Bearer
func (e ConfigForeignKeyMaster) Update(c *gin.Context) {
	req := dto.ConfigForeignKeyMasterUpdateReq{}
	s := service.ConfigForeignKeyMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改ConfigForeignKeyMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除ConfigForeignKeyMaster
// @Summary 删除ConfigForeignKeyMaster
// @Description 删除ConfigForeignKeyMaster
// @Tags ConfigForeignKeyMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/config-foreign-key-master [delete]
// @Security Bearer
func (e ConfigForeignKeyMaster) Delete(c *gin.Context) {
	s := service.ConfigForeignKeyMaster{}
	req := dto.ConfigForeignKeyMasterDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除ConfigForeignKeyMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}

func (e ConfigForeignKeyMaster) ExcelImport(c *gin.Context) {
	req := dto.ConfigForeignKeyMasterExcelImpReq{}
	s := service.ConfigForeignKeyMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// 查询字段规则情况
	list := make([]models.ConfigForeignKeyMaster2, 0)

	for i, field := range req.Results {
		var object models.ConfigForeignKeyMaster

		exist := s.IsExist(req.Sheet, field, req.PlanetId, &object)
		if !exist {
			if req.IsEnum {
				object.Field = req.Hearer[i]
			} else {
				object.Field = req.Results[i]
			}

			object.FieldName = req.Hearer[i]
			//if req.ParamJsons != nil && len(req.ParamJsons) > 0 {
			//	object.ParamJson = req.ParamJsons[i]
			//}
		}

		var object2 models.ConfigForeignKeyMaster2

		object2.Id = object.Id
		object2.PlanetId = object.PlanetId
		object2.Sheet = object.Sheet
		object2.FileName = object.FileName
		object2.Field = object.Field
		object2.FieldName = object.FieldName
		object2.ParamJson = object.ParamJson
		if len(req.ParamJsons) > 0 {
			object2.ParamJsonReq = req.ParamJsons[i]
		}

		list = append(list, object2)
	}

	e.OK(list, "成功根据Excel匹配所有字段的规则")
}
