package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/core/sdk/api"
)

type ConfigCheckWolong struct {
	api.Api
}

func (e ConfigCheckWolong) GetALL(c *gin.Context) {
	req := dto.ConfigCheckGetReq{}
	s := service.ConfigCheckWolong{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	dbRes, err := s.GetConfigCheckByChannel(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigCheckWolong 失败，\r\n失败信息 %s", err.<PERSON>rro<PERSON>()))
		return
	}

	e.OK(dbRes, "查询成功")
}
