package apis

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	models2 "go-admin/app/admin/models"
	"go-admin/app/config/define"
	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cModels "go-admin/common/models"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/fssdk"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"
	"go-admin/core/tools/utils"
	"gorm.io/gorm"
	"net/http"
	"strings"
	"time"
)

type TConfigOperateMaster struct {
	api.Api
}

// GetPage 获取TConfigOperateMaster列表
// @Summary 获取TConfigOperateMaster列表
// @Description 获取TConfigOperateMaster列表
// @Tags TConfigOperateMaster
// @Param id query int false ""
// @Param serialNo query string false "流水号"
// @Param clientIp query string false "终端IP"
// @Param endpointName query string false "终端名"
// @Param envType query string false "环境"
// @Param productId query string false "产品ID"
// @Param channelId query string false "渠道ID"
// @Param fileName query string false "文件名"
// @Param sheetName query string false "Sheet名"
// @Param md5 query string false "MD5"
// @Param ctxJson query string false "Json内容"
// @Param remark query string false "备注"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TConfigOperateMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-config-operate-master [get]
// @Security Bearer
func (e TConfigOperateMaster) GetPage(c *gin.Context) {
	req := dto.TConfigOperateMasterGetPageReq{}
	s := service.TConfigOperateMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TConfigOperateMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigOperateMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	if count <= 0 {
		e.PageOK([]models.TConfigOperateMaster{}, 0, req.GetPageIndex(), req.GetPageSize(), "查询成功")
		return
	}

	// 获取有修改的配置
	dupParams := &modifyConfigParams{
		Service:    &s,
		Req:        &req,
		Permission: p,
		List:       &list,
	}
	newList, err := dupParams.getModifyConfig()
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigAuditMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 分页
	ll := len(newList)
	start := (req.GetPageIndex() - 1) * req.GetPageSize()
	end := req.GetPageIndex() * req.GetPageSize()
	if ll > end {
		newList = newList[start:end]
	} else if ll > start {
		newList = newList[start:]
	}

	e.PageOK(newList, ll, req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TConfigOperateMaster
// @Summary 获取TConfigOperateMaster
// @Description 获取TConfigOperateMaster
// @Tags TConfigOperateMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TConfigOperateMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-config-operate-master/{id} [get]
// @Security Bearer
func (e TConfigOperateMaster) Get(c *gin.Context) {
	req := dto.TConfigOperateMasterGetReq{}
	s := service.TConfigOperateMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TConfigOperateMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigOperateMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 获取覆盖记录
	list := make([]models.TConfigOperateMaster, 0)
	var count int64

	reqRecord := dto.TConfigOperateMasterGetPageReq{
		ProductId: object.ProductId,
		ChannelId: object.ChannelId,
		EnvType:   object.EnvType,
		FileName:  object.FileName,
		SheetName: object.SheetName,
		Status:    define.StatusOverwrite,
	}
	err = s.GetOperateRecord(&reqRecord, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigOperateMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 显示最近10条
	listRecord := make([]models.OperateRecord, 0)
	for i, master := range list {
		if i >= 10 {
			break
		}
		listRecord = append(listRecord, models.OperateRecord{
			EndpointName: master.EndpointName,
			CreatedAt:    master.CreatedAt,
			Status:       master.Status,
		})
	}
	object.Record = listRecord

	// 发布版本
	listRelease := make([]models.TConfigAuditMaster, 0)
	reqRelease := dto.TConfigOperateMasterGetPageReq{
		ProductId: object.ProductId,
		ChannelId: object.ChannelId,
		EnvType:   object.EnvType,
		FileName:  object.FileName,
		SheetName: object.SheetName,
		Status:    define.StatusPublished,
	}
	var count2 int64

	// 查询已经审核的配置
	err = s.GetAuditConfigRelease(&reqRelease, p, &listRelease, &count2)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigOperateMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	if len(listRelease) > 0 {
		object.Release = listRelease[0].ToConfigOperator()
	}

	e.OK(object, "查询成功")
}

// Insert 创建TConfigOperateMaster
// @Summary 创建TConfigOperateMaster
// @Description 创建TConfigOperateMaster
// @Tags TConfigOperateMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TConfigOperateMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-config-operate-master [post]
// @Security Bearer
func (e TConfigOperateMaster) Insert(c *gin.Context) {
	req := dto.TConfigOperateMasterInsertReq{}
	s := service.TConfigOperateMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TConfigOperateMaster  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TConfigOperateMaster
// @Summary 修改TConfigOperateMaster
// @Description 修改TConfigOperateMaster
// @Tags TConfigOperateMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TConfigOperateMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-config-operate-master/{id} [put]
// @Security Bearer
func (e TConfigOperateMaster) Update(c *gin.Context) {
	req := dto.TConfigOperateMasterUpdateReq{}
	s := service.TConfigOperateMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TConfigOperateMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TConfigOperateMaster
// @Summary 删除TConfigOperateMaster
// @Description 删除TConfigOperateMaster
// @Tags TConfigOperateMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-config-operate-master [delete]
// @Security Bearer
func (e TConfigOperateMaster) Delete(c *gin.Context) {
	s := service.TConfigOperateMaster{}
	req := dto.TConfigOperateMasterDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TConfigOperateMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}

func (e TConfigOperateMaster) CommitDone(c *gin.Context) {
	dataQuery := dto.AuditInfo{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&dataQuery).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	checkQuery := models.TConfigAuditMaster{}
	checkQuery.ProductId = dataQuery.ProductId
	checkQuery.ChannelId = dataQuery.ChannelId
	checkQuery.EnvType = dataQuery.EnvType

	listCheckAudit := make([]models.TConfigAuditMaster, 0)

	err = e.Orm.Model(&checkQuery).
		Where("status=? and product_id=? and channel_id=? and env_type=? and deleted_at is null",
			define.StatusWaitAudit, dataQuery.ProductId, dataQuery.ChannelId, dataQuery.EnvType).
		Order("file_name asc").Find(&listCheckAudit).Limit(-1).Offset(-1).Error
	if err != nil {
		e.Error(500, err, err.Error())
		return
	}

	if len(listCheckAudit) > 0 {
		auditor := listCheckAudit[0].Auditor
		listUserInfo := make([]models2.SysUser, 0)
		userName, remark := "", ""
		_ = e.Orm.Model(&models2.SysUser{}).Where("user_id = ?", auditor).
			Find(&listUserInfo).Limit(-1).Offset(-1).Error
		if len(listUserInfo) > 0 {
			userName = listUserInfo[0].Username
			remark = listUserInfo[0].Remark
		}

		if userName != "" {
			if userName == "admin" {
				userName = remark
			}
			txt := fmt.Sprintf("💩【配置引擎消息】审核提示  %s\n"+
				"👉产品ID：%d\n"+
				"👉ChannelID:%d\n"+
				"👉环境：%s\n"+
				"<at user_id=\"%s\"></at>，在管理平台有你的配置审批待处理，请知晓！\n"+
				"- 来自管理平台的善意提示\n"+
				"\n",
				time.Now().Format("2006-01-02 15:04:05"),
				checkQuery.ProductId,
				checkQuery.ChannelId,
				utils.GetEnvName(int(checkQuery.EnvType)),
				userName)
			fssdk.Notify(int(checkQuery.EnvType), txt)
		}

		e.Error(500, errors.New("有审核流程，请先处理当次审批"), "有审核流程，请先处理当次审批")
		return
	}

	var dataListQuery models.TConfigOperateMaster
	listCommit := make([]models.TConfigOperateMaster, 0)

	query := e.Orm.Model(&dataListQuery).
		Where("status=? and product_id=? and channel_id=? and env_type=? and deleted_at is null",
			define.StatusWaitPublish, dataQuery.ProductId, dataQuery.ChannelId, dataQuery.EnvType)

	// 如果指定了选中的ID列表，则只处理选中的项目
	if len(dataQuery.SelectedIds) > 0 {
		query = query.Where("id IN ?", cast.ToIntSlice(strings.Split(dataQuery.SelectedIds, ",")))
	}

	err = query.Order("file_name asc").
		Find(&listCommit).Limit(-1).Offset(-1).Error
	if err != nil {
		e.Error(500, err, err.Error())
		return
	}

	// 插入审核表
	listAudit := make([]*models.TConfigAuditMaster, 0)
	for _, master := range listCommit {
		auditObj := &models.TConfigAuditMaster{
			SerialNo:     master.SerialNo,
			ClientIp:     master.ClientIp,
			EndpointName: master.EndpointName,
			EnvType:      master.EnvType,
			ProductId:    master.ProductId,
			ChannelId:    master.ChannelId,
			FileName:     master.FileName,
			SheetName:    master.SheetName,
			GitLog:       master.GitLog,
			GitName:      master.GitName,
			Md5:          master.Md5,
			CtxJson:      master.CtxJson,
			Status:       define.StatusWaitAudit,
			Remark:       dataQuery.Remark,
			Submitter:    int32(user.GetUserId(c)),
			Auditor:      dataQuery.AuditBy,
			ModelTime:    cModels.ModelTime{},
			ControlBy:    cModels.ControlBy{},
			TableType:    master.TableType,
			UserName:     user.GetUserNick(c),
		}
		listAudit = append(listAudit, auditObj)
	}

	errCreate := e.Orm.Create(&listAudit).Error
	if errCreate != nil {
		e.Error(http.StatusBadRequest, errCreate, errCreate.Error())
		return
	}
	if len(listCommit) == 0 {
		err = errors.New("newList为空")
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}

	// 将待发布区数据，修改为2：待审核
	if len(listCommit) == 0 {
		e.Error(http.StatusBadRequest, errors.New("没有找到需要提交的项目"), "没有找到需要提交的项目")
		return
	}

	ids := make([]int, 0, len(listCommit))
	for _, master := range listCommit {
		ids = append(ids, master.Id)
	}
	updateBean := &models.TConfigOperateMaster{}
	errModify := e.Orm.Model(updateBean).Where("id in ?", ids).Update("status", define.StatusWaitAudit).Error
	if errModify != nil {
		e.Error(http.StatusBadRequest, errModify, errModify.Error())
		return
	}

	// 根据玩家ID查询玩家名称
	userInfo := &models2.SysUser{}
	err = e.Orm.Model(&models2.SysUser{}).Where("user_id = ?", dataQuery.AuditBy).
		Find(&userInfo).Error
	if err != nil {
		e.Logger.Errorf("Failed to query user information，err=%s", err.Error())
	}

	// 获取地址连接
	var object models2.TEnv
	err = e.Orm.Model(&models2.TEnv{}).Where("product_id=? AND planet_id=? AND env_type=? and deleted_at is NULL",
		dataQuery.ProductId, dataQuery.ChannelId, dataQuery.EnvType).First(&object).Error
	if err != nil {
		e.Logger.Errorf("Failed to query environment data，err=%s", err.Error())
	}

	// 发送通知
	tableRaw := make([]fssdk.TableRaw, 0, len(listCommit))
	pmap := make(map[string]string)
	cmap := make(map[string]string)
	var product, channel string
	for i, master := range listCommit {
		// 去重
		pid := cast.ToString(master.ProductId)
		cid := cast.ToString(master.ChannelId)
		if pmap[pid] == "" {
			pmap[pid] = pid
			product = product + pid
		}
		if cmap[cid] == "" {
			cmap[cid] = cid
			channel = channel + cid
		}
		tableRaw = append(tableRaw, fssdk.TableRaw{
			ID:        i + 1,
			FileName:  master.FileName,
			SheetName: master.SheetName,
		})
	}

	// 如果用户是admin 则使用remark
	// 发飞书通知必须要一个用户 Remark目前绑定的是会议小助手
	if userInfo.Username == "admin" {
		userInfo.Username = userInfo.Remark
	}
	msg := fssdk.TemplateVariable{
		Topic:         "【配置引擎】审核提醒",
		TopicColor:    fssdk.GetColorByStatus(define.StatusWaitAudit),
		TableRawArray: tableRaw,
		Date:          time.Now().Format(time.DateTime),
		Env:           object.Name,
		Product:       product,
		Channel:       channel,
		CommitUser:    []string{user.GetUserName(c)},
		AuditUser:     []string{userInfo.Username},
		Remark:        dataQuery.Remark,
		PlatformUrl:   object.Host + "/config/t-config-operate-master",
		At:            userInfo.Username,
	}
	fssdk.NotifyTemplate(int(dataQuery.EnvType), msg)

	e.OK("", "提交理配置成功")
}

type modifyConfigParams struct {
	Service    *service.TConfigOperateMaster
	Req        *dto.TConfigOperateMasterGetPageReq
	Permission *actions.DataPermission
	List       *[]models.TConfigOperateMaster
}

// 返回有修改的配置的函数
func (p *modifyConfigParams) getModifyConfig() (newList []models.TConfigOperateMaster, err error) {
	newList = make([]models.TConfigOperateMaster, 0)
	// 抽取已发布数据进行对比
	var countRelease int64
	reqRelease := dto.TConfigAuditMasterGetPageReq{}
	reqRelease.ProductId = p.Req.ProductId
	reqRelease.ChannelId = p.Req.ChannelId
	reqRelease.EnvType = p.Req.EnvType
	rlMap := make(map[string]*models.TConfigAuditMaster)
	err = p.Service.GetPageRelease(&reqRelease, p.Permission, &rlMap, &countRelease)
	if err != nil {
		return
	}
	var md5List []string

	// 对比如果线上有发布版本则进行对比，没有则是新增修改
	for _, mn := range *p.List {
		// 如果有变化 或者之前没有发布过 这里都展示出来
		key := dto.GenerateConfigKey(mn.EnvType, mn.ProductId, mn.ChannelId, mn.FileName, mn.SheetName, mn.Md5, mn.TableType)
		if _, ok := rlMap[key]; !ok {
			mn.Change = define.HadModifyVal
			newList = append(newList, mn)
			md5List = append(md5List, mn.Md5)
		}
	}

	listRecord := make([]models.TConfigOperateMaster, 0)
	reqRecord := &dto.TConfigOperateMasterGetPageReq{
		ProductId: p.Req.ProductId,
		ChannelId: p.Req.ChannelId,
		EnvType:   p.Req.EnvType,
	}

	err = p.Service.GetOperateRecordByMd5(reqRecord, md5List, &listRecord)
	if err != nil {
		return nil, err
	}
	lrm := make(map[string]models.TConfigOperateMaster)
	for _, v := range listRecord {
		if _, ok := lrm[v.Md5]; !ok {
			lrm[v.Md5] = v
		}
	}

	for i, nl := range newList {
		if v, ok := lrm[nl.Md5]; ok {
			v.Change = define.HadModifyVal
			newList[i].GitName = v.GitName
			newList[i].EndpointName = v.EndpointName
		}
	}

	return
}

func (e TConfigOperateMaster) BatchUpload(c *gin.Context) {
	err := e.MakeContext(c).
		MakeOrm().
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	var dataExcelList []dto.UploadInfo

	// 绑定Json数据到切片
	if err := c.ShouldBindJSON(&dataExcelList); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var serialNo string

	logrus.Infof("dataExcelList:%v", dataExcelList)

	for i, dataExcel := range dataExcelList {
		if i == 0 {
			serialNo = dataExcel.SerialNo
		}
		if utils.GetEnvName(dataExcel.EnvType) == "Unknown" {
			errRsp := errors.New(fmt.Sprintf("不存在的环境分支:%d", dataExcel.EnvType))
			e.Error(http.StatusBadRequest, errRsp, errRsp.Error())
			return
		}

		dbRow := &models.TConfigOperateMaster{
			SerialNo:     dataExcel.SerialNo,
			ClientIp:     dataExcel.ClientIp,
			EndpointName: dataExcel.EndpointName,
			EnvType:      int32(dataExcel.EnvType),
			ProductId:    int32(dataExcel.ProductId),
			ChannelId:    dataExcel.ChannelId,
			FileName:     dataExcel.ExcelSheet.FileName,
			SheetName:    dataExcel.ExcelSheet.SheetName,
			GitName:      dataExcel.GitName,
			GitLog:       dataExcel.GitLog,
			Md5:          dataExcel.ExcelSheet.Md5,
			CtxJson:      dataExcel.ExcelSheet.CtxJson,
			Status:       define.StatusWaitPublish,
			TableType:    dataExcel.TableType,
		}

		// 开始事务
		txErr := e.Orm.Transaction(func(tx *gorm.DB) error {
			// 更新待发布区数据,覆盖待发布区数据，修改1覆盖状态
			updateBean := &models.TConfigOperateMaster{}
			err = tx.Model(updateBean).Where("product_id = ? and channel_id = ? and file_name = ? and sheet_name = ? and env_type=? and status=?",
				dbRow.ProductId, dbRow.ChannelId, dbRow.FileName, dbRow.SheetName, dbRow.EnvType, define.StatusWaitPublish).Update("status", define.StatusOverwrite).Error
			if err != nil {
				return err
			}
			// 插入待发布区数据
			return tx.Create(&dbRow).Error
		})

		if txErr != nil {
			e.Error(http.StatusBadRequest, txErr, txErr.Error())
			return
		}

		if dataExcel.RowIndex == dataExcel.RowCnt {
			e.Logger.Infof("完成一次提交：RowCnt:%d RowIndex:%d", dataExcel.RowCnt, dataExcel.RowIndex)
			txt := fmt.Sprintf("🐶【配置引擎消息】配置提交  %s\n"+
				"👉产品ID：%d\n"+
				"👉渠道列表：%s\n"+
				"👉环境：%s\n"+
				"%s提交到待发区，请知晓！\n"+
				"👉内容：%s\n"+
				"- 来自管理平台的自动消息\n"+
				"\n",
				time.Now().Format("2006-01-02 15:04:05"),
				dataExcel.ProductId,
				dataExcel.ChannelStr,
				utils.GetEnvName(dataExcel.EnvType),
				dataExcel.GitName,
				dataExcel.GitLog)
			fssdk.Notify(int(dataExcel.EnvType), txt)
		}
	}

	e.OK(serialNo, "上传处理配置成功")
}
