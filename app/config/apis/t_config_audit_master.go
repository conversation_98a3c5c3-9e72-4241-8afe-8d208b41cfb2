package apis

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	m2 "go-admin/app/admin/models"
	service2 "go-admin/app/admin/service"
	dto2 "go-admin/app/admin/service/dto"
	"go-admin/app/config/define"
	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/crypto/aes"
	"go-admin/core/sdk/pkg/fssdk"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"
	"go-admin/core/tools/utils"
	"gorm.io/gorm"
	"net/http"
	"strings"
	"time"
)

type TConfigAuditMaster struct {
	api.Api
}

// GetPage 获取TConfigAuditMaster列表
// @Summary 获取TConfigAuditMaster列表
// @Description 获取TConfigAuditMaster列表
// @Tags TConfigAuditMaster
// @Param id query int false ""
// @Param serialNo query string false "流水号"
// @Param clientIp query string false "终端IP"
// @Param endpointName query string false "终端名"
// @Param envType query string false "环境"
// @Param productId query string false "产品ID"
// @Param channelId query string false "渠道ID"
// @Param fileName query string false "文件名"
// @Param sheetName query string false "Sheet名"
// @Param md5 query string false "MD5"
// @Param ctxJson query string false "json内容"
// @Param status query string false "状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃"
// @Param remark query string false "备注"
// @Param submitter query string false "提交人"
// @Param auditor query string false "审核人"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TConfigAuditMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-config-audit-master [get]
// @Security Bearer
func (e TConfigAuditMaster) GetPage(c *gin.Context) {
	req := dto.TConfigAuditMasterGetPageReq{}
	s := service.TConfigAuditMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TConfigAuditMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigAuditMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Insert 创建TConfigAuditMaster
// @Summary 创建TConfigAuditMaster
// @Description 创建TConfigAuditMaster
// @Tags TConfigAuditMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TConfigAuditMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-config-audit-master [post]
// @Security Bearer
func (e TConfigAuditMaster) Insert(c *gin.Context) {
	req := dto.TConfigAuditMasterInsertReq{}
	s := service.TConfigAuditMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TConfigAuditMaster  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

func (e TConfigAuditMaster) Get(c *gin.Context) {
	req := dto.TConfigAuditMasterGetReq{}
	s := service.TConfigAuditMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TConfigAuditMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TConfigOperateMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Update 修改TConfigAuditMaster
// @Summary 修改TConfigAuditMaster
// @Description 修改TConfigAuditMaster
// @Tags TConfigAuditMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TConfigAuditMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-config-audit-master/{id} [put]
// @Security Bearer
func (e TConfigAuditMaster) Update(c *gin.Context) {
	req := dto.TConfigAuditMasterUpdateReq{}
	s := service.TConfigAuditMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))

	// 1. 查询待审核的部署列表
	deployList, err := e.getDeployList(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("查询发布数据失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 2. 验证审批人权限
	if err = e.validateAuditor(c, &req, deployList); err != nil {
		e.Error(500, err, err.Error())
		return
	}

	// 3. 获取环境配置
	envConfig, err := e.getEnvConfig(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("查询环境配置失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 3.1 获取已发布配置并生成manifest文件
	publishList, err := e.getPublishedList(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("查询已发布配置失败，\r\n失败信息 %s", err.Error()))
		return
	}

	manifestList, err := e.generateManifestFiles(req.EnvType, req.ProductId, req.ChannelId, publishList, deployList)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("生成manifest失败，\r\n失败信息 %s", err.Error()))
		return
	}

	// 部署列表追加manifest
	deployList = append(deployList, manifestList...)

	// 4. 部署到存储（S3或OSS）
	if err := e.deployToStorage(&req, deployList, envConfig.ConfigOssAddr); err != nil {
		e.Error(500, err, err.Error())
		return
	}

	// 5. 部署到Consul
	if err := e.deployToConsul(&req, deployList, envConfig.ConsulAddr); err != nil {
		e.Error(500, err, err.Error())
		return
	}

	// 6. 更新数据库记录
	if err := e.updateDatabaseRecords(&req); err != nil {
		e.Error(http.StatusBadRequest, err, err.Error())
		return
	}

	// 7. 发送通知
	e.sendNotification(c, &req, deployList, envConfig)

	e.OK("", "审核并且发布成功")
}

// getDeployList 获取待审核的部署列表
func (e TConfigAuditMaster) getDeployList(req *dto.TConfigAuditMasterUpdateReq) ([]models.TConfigAuditMaster, error) {
	queryBean := &models.TConfigAuditMaster{}
	deployList := make([]models.TConfigAuditMaster, 0)

	query := e.Orm.Model(&queryBean).
		Where("product_id = ? and channel_id = ? and env_type=? and status= ?",
			req.ProductId, req.ChannelId, req.EnvType, define.StatusWaitAudit)

	// 如果指定了选中的ID列表，则只处理选中的项目
	if len(req.SelectedIds) > 0 {
		query = query.Where("id IN ?", cast.ToIntSlice(strings.Split(req.SelectedIds, ",")))
	}

	err := query.Find(&deployList).Limit(-1).Offset(-1).Error

	return deployList, err
}

// getPublishedList 获取已发布的配置列表（上一个版本）
func (e TConfigAuditMaster) getPublishedList(req *dto.TConfigAuditMasterUpdateReq) ([]models.TConfigAuditMaster, error) {
	queryBean := &models.TConfigAuditMaster{}
	publishList := make([]models.TConfigAuditMaster, 0)

	err := e.Orm.Model(&queryBean).
		Where("product_id = ? and channel_id = ? and env_type=? and status= ? and deleted_at is NULL",
			req.ProductId, req.ChannelId, req.EnvType, define.StatusPublished).
		Find(&publishList).Limit(-1).Offset(-1).Error

	return publishList, err
}

// generateManifestFiles 根据历史发布配置与待审核配置生成 manifest 文件，返回额外需要部署的列表
func (e TConfigAuditMaster) generateManifestFiles(envType, productID, channelID int32, publishList, auditList []models.TConfigAuditMaster) ([]models.TConfigAuditMaster, error) {
	// 构造 sheet_name -> md5 的最终映射
	manifestMap := make(map[string]models.TConfigAuditMaster)

	// 先放入历史发布
	for _, v := range publishList {
		manifestMap[v.SheetName] = v
	}

	// 使用待审核配置覆盖
	for _, v := range auditList {
		manifestMap[v.SheetName] = v
	}

	// 构造最终的两个 manifest
	finalManifest := make(map[string]string)
	finalManifestRelease := make(map[string]string)
	aesCli := aes.New(define.AesKey, define.AesKey)

	for sheet, v := range manifestMap {
		encodeStr, err := aesCli.Encrypt(v.CtxJson)
		if err != nil {
			return nil, err
		}
		finalManifestRelease[sheet] = utils.Json2MD5Hex([]byte(encodeStr))
		finalManifest[sheet+".json"] = v.Md5
	}

	b1, err := json.Marshal(finalManifest)
	if err != nil {
		return nil, err
	}

	b2, err := json.Marshal(finalManifestRelease)
	if err != nil {
		return nil, err
	}

	manifest1 := models.TConfigAuditMaster{
		EnvType:   envType,
		ProductId: productID,
		ChannelId: channelID,
		SheetName: define.ConfigManifest,
		CtxJson:   string(b1),
	}

	manifest2 := models.TConfigAuditMaster{
		EnvType:   envType,
		ProductId: productID,
		ChannelId: channelID,
		SheetName: define.ConfigManifestRelease,
		CtxJson:   string(b2),
	}

	return []models.TConfigAuditMaster{manifest1, manifest2}, nil
}

// validateAuditor 验证审批人权限
func (e TConfigAuditMaster) validateAuditor(c *gin.Context, req *dto.TConfigAuditMasterUpdateReq, deployList []models.TConfigAuditMaster) error {
	if len(deployList) == 0 {
		return nil
	}

	currentU := user.GetUserId(c)
	if req.EnvType == utils.Develop {
		// dev 环境不校验
		return nil
	}

	if currentU != define.AdminUid && deployList[0].Auditor != int32(currentU) {
		return errors.New("失败:不是审批人")
	}

	return nil
}

// getEnvConfig 获取环境配置
func (e TConfigAuditMaster) getEnvConfig(req *dto.TConfigAuditMasterUpdateReq) (*m2.TEnv, error) {
	var object m2.TEnv
	err := e.Orm.Model(&m2.TEnv{}).
		Where("product_id=? AND planet_id=? AND env_type=? and deleted_at is NULL",
			req.ProductId, req.ChannelId, req.EnvType).
		First(&object).Error

	if err != nil {
		return nil, err
	}

	return &object, nil
}

// deployToStorage 部署到存储（S3或OSS）
func (e TConfigAuditMaster) deployToStorage(req *dto.TConfigAuditMasterUpdateReq, deployList []models.TConfigAuditMaster, configOssAddr string) error {
	if configOssAddr == "" {
		return errors.New("失败:没有配置OSS信息")
	}

	logrus.Infof("ossInfoFromDb =%s", configOssAddr)
	envList := strings.Split(configOssAddr, "|")
	if len(envList) < 4 {
		return errors.New("失败:OSS信息配置格式不对")
	}

	ossAddr := envList[0]
	ossUser := envList[1]
	ossPwd := envList[2]
	ossBasePath := envList[3]

	// 根据配置选择部署方式
	if envList[0] == "s3" {
		_, err := service.Deploy2S3(req.ProductId, req.ChannelId, deployList)
		if err != nil {
			return fmt.Errorf("上传s3失败，失败信息 %s", err.Error())
		}
	} else {
		_, err := service.Deploy2Oss(req.ProductId, req.ChannelId, ossAddr, ossUser, ossPwd, ossBasePath, deployList)
		if err != nil {
			return fmt.Errorf("上传OSS失败，失败信息 %s", err.Error())
		}
	}

	return nil
}

// deployToConsul 部署到Consul
func (e TConfigAuditMaster) deployToConsul(req *dto.TConfigAuditMasterUpdateReq, deployList []models.TConfigAuditMaster, consulAddr string) error {
	if consulAddr == "" {
		return errors.New("失败:Consul信息配置格式不对")
	}

	consulList := strings.Split(consulAddr, "|")
	if len(consulList) < 2 {
		return errors.New("失败:Consul信息错误")
	}

	consulHost := consulList[0]
	consulBasicDir := consulList[1]

	if consulHost == "" || consulBasicDir == "" {
		return errors.New("失败:Consul信息错误")
	}

	// 发布到Consul
	_, err := service.Deploy2Consul(consulHost, consulBasicDir, req.ProductId, req.ChannelId, deployList)
	if err != nil {
		return fmt.Errorf("consul发布失败，失败信息 %s", err.Error())
	}

	return nil
}

// updateDatabaseRecords 更新数据库记录
func (e TConfigAuditMaster) updateDatabaseRecords(req *dto.TConfigAuditMasterUpdateReq) error {
	// 获取需要处理的项目列表
	deployList, err := e.getDeployList(req)
	if err != nil {
		return err
	}

	if len(deployList) == 0 {
		return errors.New("没有找到需要处理的项目")
	}

	// 更新待发布区数据,覆盖待发布区数据，修改为已发布状态
	updateBean := &models.TConfigOperateMaster{}

	operateQuery := e.Orm.Model(updateBean).
		Where("product_id = ? and channel_id = ? and env_type=? and status=? and deleted_at is NULL",
			req.ProductId, req.ChannelId, req.EnvType, define.StatusWaitAudit)

	// 如果指定了选中的ID列表，需要根据部署列表的文件和sheet进行匹配
	if len(req.SelectedIds) > 0 {
		var conditions []string
		var args []interface{}
		for _, item := range deployList {
			conditions = append(conditions, "(file_name = ? and sheet_name = ?)")
			args = append(args, item.FileName, item.SheetName)
		}
		if len(conditions) > 0 {
			operateQuery = operateQuery.Where(strings.Join(conditions, " OR "), args...)
		}
	}

	errModify := operateQuery.Update("status", define.StatusPublished).Error
	if errModify != nil {
		return errModify
	}

	// 在事务中更新审核区数据和软删除旧版本
	return e.Orm.Transaction(func(tx *gorm.DB) error {
		// 更新审核区数据，修改为已发布状态
		updateAuditBean := &models.TConfigAuditMaster{}
		auditQuery := tx.Model(updateAuditBean).
			Where("product_id = ? and channel_id = ? and env_type=? and deleted_at is NULL",
				req.ProductId, req.ChannelId, req.EnvType)

		// 如果指定了选中的ID列表，只更新选中的项目
		if len(req.SelectedIds) > 0 {
			auditQuery = auditQuery.Where("id IN ?", cast.ToIntSlice(strings.Split(req.SelectedIds, ",")))
		}

		err = auditQuery.Update("status", define.StatusPublished).Error
		if err != nil {
			return err
		}

		// 软删除上一个版本的配置
		for _, v := range deployList {
			err = tx.Where("product_id = ? and channel_id = ? and env_type=? "+
				"and file_name=? and sheet_name=? and id !=? and deleted_at is NULL ",
				v.ProductId, v.ChannelId, v.EnvType, v.FileName, v.SheetName, v.Id).
				Delete(&updateAuditBean).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// sendNotification 发送通知
func (e TConfigAuditMaster) sendNotification(c *gin.Context, req *dto.TConfigAuditMasterUpdateReq, deployList []models.TConfigAuditMaster, envConfig *m2.TEnv) {
	// 构建通知数据
	tableRaw := make([]fssdk.TableRaw, 0, len(deployList))
	pmap := make(map[string]string)
	cmap := make(map[string]string)
	var product, channel string
	var uid int32
	var remark string

	for i, master := range deployList {
		if master.SheetName == define.ConfigManifest || master.SheetName == define.ConfigManifestRelease {
			continue
		}
		// 获取一个用户
		if uid == 0 {
			remark = master.Remark
			uid = master.Submitter
		}
		// 去重
		pid := cast.ToString(master.ProductId)
		cid := cast.ToString(master.ChannelId)
		if pmap[pid] == "" {
			pmap[pid] = pid
			product = product + pid
		}
		if cmap[cid] == "" {
			cmap[cid] = cid
			channel = channel + cid
		}
		tableRaw = append(tableRaw, fssdk.TableRaw{
			ID:        i + 1,
			FileName:  master.FileName,
			SheetName: master.SheetName,
		})
	}

	// 获取提交用户信息
	submitUser := e.getSubmitUser(uid, c)

	// 构建并发送通知消息
	msg := fssdk.TemplateVariable{
		Topic:         "👍【配置引擎】发布成功 🎉😄",
		TopicColor:    fssdk.GetColorByStatus(define.StatusPublished),
		TableRawArray: tableRaw,
		Date:          time.Now().Format(time.DateTime),
		Env:           envConfig.Name,
		Product:       product,
		Channel:       channel,
		CommitUser:    []string{submitUser.Username},
		AuditUser:     []string{user.GetUserName(c)},
		Remark:        remark,
		PlatformUrl:   envConfig.Host + "/config/t-config-operate-master",
		At:            submitUser.Username,
	}
	fssdk.NotifyTemplate(int(req.EnvType), msg)
}

// getSubmitUser 获取提交用户信息
func (e TConfigAuditMaster) getSubmitUser(uid int32, c *gin.Context) *m2.SysUser {
	su := service2.SysUser{}
	e.MakeService(&su.Service)
	u := &m2.SysUser{}

	err := su.Get(&dto2.SysUserById{
		ObjectById: cDto.ObjectById{Id: int(uid)},
	}, actions.GetPermissionFromContext(c), u)

	if err != nil {
		logrus.Errorf("Failed to obtain the commit user. Procedure：%v", err)
		u = &m2.SysUser{Username: "unknown"}
	}

	// admin 不是飞书用户 会推送失败，remark是测试的飞书账号
	if u.Username == "admin" {
		u.Username = u.Remark
	}

	return u
}

func (e TConfigAuditMaster) Delete(c *gin.Context) {
	req := dto.TConfigAuditMasterUpdateReq{}
	s := service.TConfigAuditMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))

	// 获取需要拒绝的项目列表
	deployList, err := e.getDeployList(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("查询待拒绝项目失败，\r\n失败信息 %s", err.Error()))
		return
	}

	if len(deployList) == 0 {
		e.Error(http.StatusBadRequest, errors.New("没有找到需要拒绝的项目"), "没有找到需要拒绝的项目")
		return
	}

	// 修改为待发布状态
	updateBean := &models.TConfigOperateMaster{}
	operateQuery := e.Orm.Model(updateBean).Where("product_id = ? and channel_id = ? and env_type=? "+
		"and deleted_at is NULL and status =? ",
		req.ProductId, req.ChannelId, req.EnvType, define.StatusWaitAudit)

	// 如果指定了选中的ID列表，需要根据部署列表的文件和sheet进行匹配
	if len(req.SelectedIds) > 0 {
		var conditions []string
		var args []interface{}
		for _, item := range deployList {
			conditions = append(conditions, "(file_name = ? and sheet_name = ?)")
			args = append(args, item.FileName, item.SheetName)
		}
		if len(conditions) > 0 {
			operateQuery = operateQuery.Where(strings.Join(conditions, " OR "), args...)
		}
	}

	errModify := operateQuery.Update("status", define.StatusWaitPublish).Error
	if errModify != nil {
		e.Error(http.StatusBadRequest, errModify, errModify.Error())
		return
	}

	// 更新审核区数据，修改为废弃状态
	updateAuditBean := &models.TConfigAuditMaster{}
	auditQuery := e.Orm.Model(updateAuditBean).Where("product_id = ? and channel_id = ? and env_type=? "+
		"and deleted_at is NULL and status =? ",
		req.ProductId, req.ChannelId, req.EnvType, define.StatusWaitAudit)

	// 如果指定了选中的ID列表，只更新选中的项目
	if len(req.SelectedIds) > 0 {
		auditQuery = auditQuery.Where("id IN ?", req.SelectedIds)
	}

	errModifyAudit := auditQuery.Update("status", define.StatusDiscard).Error
	if errModifyAudit != nil {
		e.Error(http.StatusBadRequest, errModifyAudit, errModifyAudit.Error())
		return
	}

	e.OK("", "拒绝并且发布成功")
}
