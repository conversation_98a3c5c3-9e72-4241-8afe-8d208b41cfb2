package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
)

type ConfigCheckMaster struct {
	api.Api
}

// GetPage 获取ConfigCheckMaster列表
// @Summary 获取ConfigCheckMaster列表
// @Description 获取ConfigCheckMaster列表
// @Tags ConfigCheckMaster
// @Param checkName query string false "check name"
// @Param checkType query string false "check类型"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.ConfigCheckMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-check-master [get]
// @Security Bearer
func (e ConfigCheckMaster) GetPage(c *gin.Context) {
    req := dto.ConfigCheckMasterGetPageReq{}
    s := service.ConfigCheckMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.ConfigCheckMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigCheckMaster 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取ConfigCheckMaster
// @Summary 获取ConfigCheckMaster
// @Description 获取ConfigCheckMaster
// @Tags ConfigCheckMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.ConfigCheckMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-check-master/{id} [get]
// @Security Bearer
func (e ConfigCheckMaster) Get(c *gin.Context) {
	req := dto.ConfigCheckMasterGetReq{}
	s := service.ConfigCheckMaster{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.ConfigCheckMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigCheckMaster失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建ConfigCheckMaster
// @Summary 创建ConfigCheckMaster
// @Description 创建ConfigCheckMaster
// @Tags ConfigCheckMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigCheckMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/config-check-master [post]
// @Security Bearer
func (e ConfigCheckMaster) Insert(c *gin.Context) {
    req := dto.ConfigCheckMasterInsertReq{}
    s := service.ConfigCheckMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建ConfigCheckMaster  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改ConfigCheckMaster
// @Summary 修改ConfigCheckMaster
// @Description 修改ConfigCheckMaster
// @Tags ConfigCheckMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigCheckMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/config-check-master/{id} [put]
// @Security Bearer
func (e ConfigCheckMaster) Update(c *gin.Context) {
    req := dto.ConfigCheckMasterUpdateReq{}
    s := service.ConfigCheckMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改ConfigCheckMaster 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除ConfigCheckMaster
// @Summary 删除ConfigCheckMaster
// @Description 删除ConfigCheckMaster
// @Tags ConfigCheckMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/config-check-master [delete]
// @Security Bearer
func (e ConfigCheckMaster) Delete(c *gin.Context) {
    s := service.ConfigCheckMaster{}
    req := dto.ConfigCheckMasterDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除ConfigCheckMaster失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}