package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
)

type ConfigFieldMaster struct {
	api.Api
}

// GetPage 获取ConfigFieldMaster列表
// @Summary 获取ConfigFieldMaster列表
// @Description 获取ConfigFieldMaster列表
// @Tags ConfigFieldMaster
// @Param planetId query string false "planet"
// @Param field query string false "字段"
// @Param fieldName query string false "字段名"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.ConfigFieldMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-field-master [get]
// @Security Bearer
func (e ConfigFieldMaster) GetPage(c *gin.Context) {
	req := dto.ConfigFieldMasterGetPageReq{}
	s := service.ConfigFieldMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.ConfigFieldMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigFieldMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取ConfigFieldMaster
// @Summary 获取ConfigFieldMaster
// @Description 获取ConfigFieldMaster
// @Tags ConfigFieldMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.ConfigFieldMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-field-master/{id} [get]
// @Security Bearer
func (e ConfigFieldMaster) Get(c *gin.Context) {
	req := dto.ConfigFieldMasterGetReq{}
	s := service.ConfigFieldMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.ConfigFieldMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigFieldMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建ConfigFieldMaster
// @Summary 创建ConfigFieldMaster
// @Description 创建ConfigFieldMaster
// @Tags ConfigFieldMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigFieldMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/config-field-master [post]
// @Security Bearer
func (e ConfigFieldMaster) Insert(c *gin.Context) {
	req := dto.ConfigFieldMasterInsertReq{}
	s := service.ConfigFieldMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建ConfigFieldMaster  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

func (e ConfigFieldMaster) ExcelImport(c *gin.Context) {
	req := dto.ConfigFieldMasterExcelImpReq{}
	s := service.ConfigFieldMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// 查询字段规则情况
	list := make([]models.ConfigFieldMaster, 0)

	for i, field := range req.Results {
		var object models.ConfigFieldMaster

		_ = s.IsExist(req.Sheet, field, req.PlanetId, &object)
		object.Field = field
		object.FieldName = req.Hearer[i]
		//object.FieldRuleId = fieldRuleIDs
		//if !isExist {
		list = append(list, object)
		//}
	}

	e.OK(list, "成功根据Excel匹配所有字段的规则")
}

// Update 修改ConfigFieldMaster
// @Summary 修改ConfigFieldMaster
// @Description 修改ConfigFieldMaster
// @Tags ConfigFieldMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigFieldMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/config-field-master/{id} [put]
// @Security Bearer
func (e ConfigFieldMaster) Update(c *gin.Context) {
	req := dto.ConfigFieldMasterUpdateReq{}
	s := service.ConfigFieldMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改ConfigFieldMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除ConfigFieldMaster
// @Summary 删除ConfigFieldMaster
// @Description 删除ConfigFieldMaster
// @Tags ConfigFieldMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/config-field-master [delete]
// @Security Bearer
func (e ConfigFieldMaster) Delete(c *gin.Context) {
	s := service.ConfigFieldMaster{}
	req := dto.ConfigFieldMasterDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除ConfigFieldMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
