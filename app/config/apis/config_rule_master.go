package apis

import (
    "fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/config/models"
	"go-admin/app/config/service"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
)

type ConfigRuleMaster struct {
	api.Api
}

// GetPage 获取ConfigRuleMaster列表
// @Summary 获取ConfigRuleMaster列表
// @Description 获取ConfigRuleMaster列表
// @Tags ConfigRuleMaster
// @Param ruleName query string false "rule name"
// @Param ruleType query string false "规则类型"
// @Param checkId query string false "检查ID"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.ConfigRuleMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-rule-master [get]
// @Security Bearer
func (e ConfigRuleMaster) GetPage(c *gin.Context) {
    req := dto.ConfigRuleMasterGetPageReq{}
    s := service.ConfigRuleMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.ConfigRuleMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigRuleMaster 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取ConfigRuleMaster
// @Summary 获取ConfigRuleMaster
// @Description 获取ConfigRuleMaster
// @Tags ConfigRuleMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.ConfigRuleMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/config-rule-master/{id} [get]
// @Security Bearer
func (e ConfigRuleMaster) Get(c *gin.Context) {
	req := dto.ConfigRuleMasterGetReq{}
	s := service.ConfigRuleMaster{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.ConfigRuleMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取ConfigRuleMaster失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}

// Insert 创建ConfigRuleMaster
// @Summary 创建ConfigRuleMaster
// @Description 创建ConfigRuleMaster
// @Tags ConfigRuleMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigRuleMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/config-rule-master [post]
// @Security Bearer
func (e ConfigRuleMaster) Insert(c *gin.Context) {
    req := dto.ConfigRuleMasterInsertReq{}
    s := service.ConfigRuleMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建ConfigRuleMaster  失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改ConfigRuleMaster
// @Summary 修改ConfigRuleMaster
// @Description 修改ConfigRuleMaster
// @Tags ConfigRuleMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.ConfigRuleMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/config-rule-master/{id} [put]
// @Security Bearer
func (e ConfigRuleMaster) Update(c *gin.Context) {
    req := dto.ConfigRuleMasterUpdateReq{}
    s := service.ConfigRuleMaster{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改ConfigRuleMaster 失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "修改成功")
}

// Delete 删除ConfigRuleMaster
// @Summary 删除ConfigRuleMaster
// @Description 删除ConfigRuleMaster
// @Tags ConfigRuleMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/config-rule-master [delete]
// @Security Bearer
func (e ConfigRuleMaster) Delete(c *gin.Context) {
    s := service.ConfigRuleMaster{}
    req := dto.ConfigRuleMasterDeleteReq{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
    if err != nil {
        e.Logger.Error(err)
        e.Error(500, err, err.Error())
        return
    }

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除ConfigRuleMaster失败，\r\n失败信息 %s", err.Error()))
        return
	}
	e.OK( req.GetId(), "删除成功")
}