package service

import (
	"errors"
	"go-admin/app/config/define"

	"go-admin/core/sdk/service"

	"gorm.io/gorm"

	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TConfigOperateMaster struct {
	service.Service
}

// GetPage 获取TConfigOperateMaster列表
func (e *TConfigOperateMaster) GetPage(c *dto.TConfigOperateMasterGetPageReq, p *actions.DataPermission, list *[]models.TConfigOperateMaster, count *int64) error {
	var err error
	var data models.TConfigOperateMaster

	err = e.Orm.Model(&data).
		Scopes(cDto.MakeCondition(c.GetNeedSearch())).
		Where("status=?", c.Status).
		Order("created_at desc").
		Find(list).
		Limit(-1).Offset(-1).
		Count(count).
		Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// GetPageRelease 获取TConfigAuditMaster列表 已发布数据
func (e *TConfigOperateMaster) GetPageRelease(c *dto.TConfigAuditMasterGetPageReq, p *actions.DataPermission, rlMap *map[string]*models.TConfigAuditMaster, count *int64) error {
	var err error
	var data models.TConfigAuditMaster

	list := new([]models.TConfigAuditMaster)
	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
		).Where("status in (?)", []int32{define.StatusPublished, define.StatusWaitAudit}).Order("created_at desc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}

	// 将list中的数据存入map中
	if rlMap == nil {
		return errors.New("rlMap is nil")
	}
	for _, v := range *list {
		key := dto.GenerateConfigKey(v.EnvType, v.ProductId, v.ChannelId, v.FileName, v.SheetName, v.Md5, v.TableType)
		(*rlMap)[key] = &v
	}

	return nil
}

// GetOperateRecord 获取待发布区域覆盖操作记录
func (e *TConfigOperateMaster) GetOperateRecord(c *dto.TConfigOperateMasterGetPageReq, p *actions.DataPermission, list *[]models.TConfigOperateMaster, count *int64) error {
	var err error
	var data models.TConfigOperateMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			actions.Permission(data.TableName(), p),
		).Order("created_at asc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

func (e *TConfigOperateMaster) GetOperateRelease(c *dto.TConfigOperateMasterGetPageReq, p *actions.DataPermission, list *[]models.TConfigOperateMaster, count *int64) error {
	var err error
	var data models.TConfigOperateMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Where("status=?", define.StatusPublished).Order("file_name asc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

func (e *TConfigOperateMaster) GetAuditConfigRelease(c *dto.TConfigOperateMasterGetPageReq, p *actions.DataPermission, list *[]models.TConfigAuditMaster, count *int64) error {
	var err error
	var data models.TConfigAuditMaster

	err = e.Orm.Model(&data).Where("product_id = ? and channel_id = ? and env_type=? and file_name=? and sheet_name=? and status=? and deleted_at is NULL",
		c.ProductId, c.ChannelId, c.EnvType, c.FileName, c.SheetName, define.StatusPublished).Order("created_at desc").Find(list).Limit(-1).Offset(-1).
		Count(count).Error

	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TConfigOperateMaster对象
func (e *TConfigOperateMaster) Get(d *dto.TConfigOperateMasterGetReq, p *actions.DataPermission, model *models.TConfigOperateMaster) error {
	var data models.TConfigOperateMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTConfigOperateMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TConfigOperateMaster对象
func (e *TConfigOperateMaster) Insert(c *dto.TConfigOperateMasterInsertReq) error {
	var err error
	var data models.TConfigOperateMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TConfigOperateMaster对象
func (e *TConfigOperateMaster) Update(c *dto.TConfigOperateMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TConfigOperateMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TConfigOperateMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TConfigOperateMaster
func (e *TConfigOperateMaster) Remove(d *dto.TConfigOperateMasterDeleteReq, p *actions.DataPermission) error {
	var data models.TConfigOperateMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTConfigOperateMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}

func (e *TConfigOperateMaster) GetOperateRecordByMd5(c *dto.TConfigOperateMasterGetPageReq, md5List []string, list *[]models.TConfigOperateMaster) error {
	var err error
	var data models.TConfigOperateMaster

	err = e.Orm.Model(&data).
		Where("env_type=? and product_id=? and channel_id=? and status in(0,1) and md5 in ?", c.EnvType, c.ProductId, c.ChannelId, md5List).
		Order("created_at asc").
		Find(list).Limit(-1).Offset(-1).Error
	if err != nil {
		e.Log.Errorf("TConfigOperateMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}
