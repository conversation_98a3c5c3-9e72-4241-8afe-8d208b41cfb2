package service

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/panjf2000/ants/v2"
	"go-admin/app/config/define"
	"go-admin/app/config/models"
	"go-admin/core/logger"
	"go-admin/core/sdk/pkg/awssdk"
	"go-admin/core/sdk/pkg/crypto/aes"
	"go-admin/core/tools/consul"
	"go-admin/core/tools/ftpx"
	"go-admin/core/tools/utils"
	"log"
	"sync"
)

func Deploy2Oss(productID, channelID int32, host, user, pwd, baseDir string, list []models.TConfigAuditMaster) ([]models.TConfigAuditMaster, error) {
	// 上传文件到OSS
	ftpServer := &ftpx.FtpServer{}
	err := ftpServer.Login(
		ftpx.WithHost(host),
		ftpx.WithUserName(user),
		ftpx.WithPassword(pwd),
		ftpx.WithBaseDir(baseDir))
	if err != nil {
		logger.Error(err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.GetRemoteBasePath()
	if err != nil {
		return nil, err
	}

	deployList := make([]models.TConfigAuditMaster, 0)
	aesCli := aes.New(define.AesKey, define.AesKey)

	for _, master := range list {
		if master.TableType != "" && master.TableType != define.TableTypeClientOnly {
			continue
		}
		fileName := master.SheetName
		jsonStr := master.CtxJson

		if errEnsure := ftpServer.EnsureRemoteDirExists(basePath, baseDir); errEnsure != nil {
			logger.Errorf("%s path err:", errEnsure, fileName)
			return nil, errEnsure
		}

		fileNamePath := fmt.Sprintf("%d/%d/%s.json", productID, channelID, master.SheetName)

		errUpload := ftpServer.UploadJsonStr(basePath, baseDir, fileNamePath, jsonStr)
		if errUpload != nil {
			logger.Errorf("UploadJsonStr %s err:%+v", fileName, errUpload)
			return nil, errUpload
		}

		// 上传加密文件
		{
			if master.SheetName == define.ConfigManifest || master.SheetName == define.ConfigManifestRelease {
				deployList = append(deployList, master)
				continue
			}
			fileNamePath := fmt.Sprintf("%d/%d/%s", productID, channelID, master.SheetName)
			encodeStr, err := aesCli.Encrypt(jsonStr)
			if err != nil {
				logger.Errorf("encodeJsonStr %s err:%+v", fileName, err)
				return nil, err
			}

			errUpload := ftpServer.UploadJsonStr(basePath, baseDir, fileNamePath, encodeStr)
			if errUpload != nil {
				logger.Errorf("UploadJsonStr %s err:%+v", fileName, errUpload)
				return nil, errUpload
			}
		}

		deployList = append(deployList, master)
	}

	logger.Info("File deploy oss successfully")
	return deployList, nil
}

const ConsulPrefix = "fancy"

func Deploy2Consul(consulAddr, baseDir string, productID, channelID int32, list []models.TConfigAuditMaster) ([]models.TConfigAuditMaster, error) {
	c := consul.NewConfig(
		consul.WithPrefix(ConsulPrefix),
		consul.WithAddress(consulAddr))
	err := c.Init()
	if err != nil {
		return nil, err
	}

	// 创建一个 goroutine 池
	p, _ := ants.NewPool(10) // 最多 10 个并发任务
	defer p.Release()

	deployList := make([]models.TConfigAuditMaster, 0)
	errChan := make(chan error, len(list))
	doneChan := make(chan struct{}, len(list))

	for _, master := range list {
		if master.TableType != "" && master.TableType != define.TableTypeServerOnly {
			doneChan <- struct{}{}
			continue
		}
		m := master
		err = p.Submit(func() {
			k := fmt.Sprintf("%s/%d/%d/%s", baseDir, productID, channelID, m.SheetName)
			errPut := c.PutJson(k, []byte(m.CtxJson))
			if errPut != nil {
				errChan <- errPut
				return
			}
			doneChan <- struct{}{}
		})
		if err != nil {
			return nil, err
		}
	}

	// 等待所有任务完成或出现错误
	for i := 0; i < len(list); i++ {
		select {
		case err = <-errChan:
			logger.Errorf("Put to consul JsonStr err: %v", err)
			return nil, err
		case <-doneChan:
			deployList = append(deployList, list[i])
		}
	}
	logger.Info("File deploy consul successfully")
	return deployList, nil
}

func Deploy2S3(productID, channelID int32, list []models.TConfigAuditMaster) ([]models.TConfigAuditMaster, error) {

	ctx := context.Background()
	cfg, err := awssdk.LoadS3Cfg()
	if err != nil {
		return nil, err
	}

	// 创建S3客户端
	s3Client := s3.NewFromConfig(cfg)
	uploader := manager.NewUploader(s3Client)

	sem := make(chan struct{}, 10)
	var wg sync.WaitGroup
	var gErr error
	aesCli := aes.New(define.AesKey, define.AesKey)

	// 遍历结构体上传
	for _, data := range list {
		if data.TableType != "" && data.TableType != define.TableTypeClientOnly {
			continue
		}
		var key string
		// 生成唯一S3 key
		if data.EnvType == utils.Prod {
			key = fmt.Sprintf("%d/%d/%s", productID, channelID, data.SheetName)
		} else {
			key = fmt.Sprintf("%s/%d/%d/%d/%s", define.S3Prefix, data.EnvType, productID, channelID, data.SheetName)
		}
		wg.Add(1)
		// 获取信号量
		sem <- struct{}{}

		go func(ele models.TConfigAuditMaster, key string) {
			defer wg.Done()
			defer func() {
				// 释放信号量
				<-sem
			}()
			encodeStr := ""
			if ele.SheetName == define.ConfigManifest || ele.SheetName == define.ConfigManifestRelease {
				encodeStr = ele.CtxJson
			} else {
				encodeStr, err = aesCli.Encrypt(ele.CtxJson)
				if err != nil {
					gErr = err
					log.Printf("Encrypt error for key %s: %v", key, err)
					return
				}
			}
			// 序列化并上传
			if err := awssdk.UploadJSON(ctx, uploader, encodeStr, key); err != nil {
				gErr = err
				logger.Errorf("Upload error for key %s: %v", key, err)
			}
		}(data, key)
	}

	wg.Wait()

	// 刷新cloudfront的缓存 创建失效
	if gErr == nil && len(list) > 0 {
		// 构建失效路径 - 直接失效整个目录
		var invalidationPath string
		if list[0].EnvType == utils.Prod {
			invalidationPath = fmt.Sprintf("/%d/%d/*", productID, channelID)
		} else {
			invalidationPath = fmt.Sprintf("/%s/%d/%d/%d/*", define.S3Prefix, list[0].EnvType, productID, channelID)
		}

		// 调用CloudFront失效
		InvalidateCloudFront(invalidationPath)
	}

	logger.Info("File deploy s3 successfully")
	return list, gErr
}
