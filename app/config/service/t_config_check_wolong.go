package service

import (
	"go-admin/app/config/service/dto"
	"go-admin/core/sdk/service"
)

type ConfigCheckWolong struct {
	service.Service
}

// GetConfigCheckByChannel  获取配置检查
func (e *ConfigCheckWolong) GetConfigCheckByChannel(req *dto.ConfigCheckGetReq) (res *dto.ConfigCheckGetRsp, err error) {
	// 获取配置字段
	cfm := ConfigFieldMaster{e.Service}
	cfmRes, err := cfm.Find()
	if err != nil {
		return
	}

	// 获取规则
	crm := ConfigRuleMaster{e.Service}
	crmRes, err := crm.Find()
	if err != nil {
		return
	}

	// 获取检查
	ccm := ConfigCheckMaster{e.Service}
	ccmRes, err := ccm.Find()
	if err != nil {
		return
	}

	// 转换
	res = dto.ConvertByWolong(cfmRes, crmRes, ccmRes)

	return
}
