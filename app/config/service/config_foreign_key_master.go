package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type ConfigForeignKeyMaster struct {
	service.Service
}

// GetPage 获取ConfigForeignKeyMaster列表
func (e *ConfigForeignKeyMaster) GetPage(c *dto.ConfigForeignKeyMasterGetPageReq, p *actions.DataPermission, list *[]models.ConfigForeignKeyMaster, count *int64) error {
	var err error
	var data models.ConfigForeignKeyMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("ConfigForeignKeyMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取ConfigForeignKeyMaster对象
func (e *ConfigForeignKeyMaster) Get(d *dto.ConfigForeignKeyMasterGetReq, p *actions.DataPermission, model *models.ConfigForeignKeyMaster) error {
	var data models.ConfigForeignKeyMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetConfigForeignKeyMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建ConfigForeignKeyMaster对象
func (e *ConfigForeignKeyMaster) Insert(c *dto.ConfigForeignKeyMasterInsertReq) error {
	var err error
	var data models.ConfigForeignKeyMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("ConfigForeignKeyMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// IsExist Field是否存在
func (e *ConfigForeignKeyMaster) IsExist(sheet string, field string, planetID int64, model *models.ConfigForeignKeyMaster) bool {
	var data models.ConfigForeignKeyMaster

	err := e.Orm.Model(&data).Where("planet_id = ? and field = ? and sheet = ?", planetID, field, sheet).
		First(model).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return false
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return false
	}
	return true
}

// Update 修改ConfigForeignKeyMaster对象
func (e *ConfigForeignKeyMaster) Update(c *dto.ConfigForeignKeyMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.ConfigForeignKeyMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("ConfigForeignKeyMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除ConfigForeignKeyMaster
func (e *ConfigForeignKeyMaster) Remove(d *dto.ConfigForeignKeyMasterDeleteReq, p *actions.DataPermission) error {
	var data models.ConfigForeignKeyMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveConfigForeignKeyMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
