package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type ConfigRuleMaster struct {
	service.Service
}

// GetPage 获取ConfigRuleMaster列表
func (e *ConfigRuleMaster) GetPage(c *dto.ConfigRuleMasterGetPageReq, p *actions.DataPermission, list *[]models.ConfigRuleMaster, count *int64) error {
	var err error
	var data models.ConfigRuleMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("ConfigRuleMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取ConfigRuleMaster对象
func (e *ConfigRuleMaster) Get(d *dto.ConfigRuleMasterGetReq, p *actions.DataPermission, model *models.ConfigRuleMaster) error {
	var data models.ConfigRuleMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetConfigRuleMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建ConfigRuleMaster对象
func (e *ConfigRuleMaster) Insert(c *dto.ConfigRuleMasterInsertReq) error {
	var err error
	var data models.ConfigRuleMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("ConfigRuleMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改ConfigRuleMaster对象
func (e *ConfigRuleMaster) Update(c *dto.ConfigRuleMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.ConfigRuleMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("ConfigRuleMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除ConfigRuleMaster
func (e *ConfigRuleMaster) Remove(d *dto.ConfigRuleMasterDeleteReq, p *actions.DataPermission) error {
	var data models.ConfigRuleMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveConfigRuleMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}

// Find ConfigFieldMaster by channel
func (e *ConfigRuleMaster) Find() (data []models.ConfigRuleMaster, err error) {

	err = e.Orm.Find(&data).Error

	return data, err
}
