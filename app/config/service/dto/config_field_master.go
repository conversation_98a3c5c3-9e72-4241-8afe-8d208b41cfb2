package dto

import (
	"time"

	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type ConfigFieldMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	PlanetId       int64  `form:"planetId"  search:"type:exact;column:planet_id;table:config_field_master" comment:"planet"`
	Sheet          string `form:"sheet"  search:"type:exact;column:sheet;table:config_field_master" comment:"sheet"`
	Field          string `form:"field"  search:"type:exact;column:field;table:config_field_master" comment:"字段"`
	FieldName      string `form:"fieldName"  search:"type:contains;column:field_name;table:config_field_master" comment:"字段名"`
	ConfigFieldMasterOrder
}

type ConfigFieldMasterOrder struct {
	Id          int       `form:"idOrder"  search:"type:order;column:id;table:config_field_master"`
	PlanetId    string    `form:"planetIdOrder"  search:"type:order;column:planet_id;table:config_field_master"`
	Sheet       string    `form:"sheetOrder"  search:"type:order;column:sheet;table:config_field_master"`
	Field       string    `form:"fieldOrder"  search:"type:order;column:field;table:config_field_master"`
	FieldName   string    `form:"fieldNameOrder"  search:"type:order;column:field_name;table:config_field_master"`
	FieldType   string    `form:"fieldTypeOrder"  search:"type:order;column:field_type;table:config_field_master"`
	FieldRuleId string    `form:"fieldRuleIdOrder"  search:"type:order;column:field_rule_id;table:config_field_master"`
	CreatedAt   time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:config_field_master"`
	UpdatedAt   time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:config_field_master"`
	CreatedBy   string    `form:"createdByOrder"  search:"type:order;column:created_by;table:config_field_master"`
	UpdatedBy   string    `form:"updatedByOrder"  search:"type:order;column:updated_by;table:config_field_master"`
	DeletedAt   time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:config_field_master"`
}

func (m *ConfigFieldMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type ConfigFieldMasterInsertReq struct {
	Id          int    `json:"-" comment:""` //
	PlanetId    int64  `json:"planetId" comment:"planet"`
	Sheet       string `json:"sheet" comment:"sheet"`
	Field       string `json:"field" comment:"字段"`
	FieldName   string `json:"fieldName" comment:"字段名"`
	FieldType   string `json:"fieldType" comment:"字段类型"`
	FieldRuleId string `json:"fieldRuleId" comment:"规则ID"`
	CreatedBy   string `json:"createdBy" comment:""`
	UpdatedBy   string `json:"updatedBy" comment:""`
	common.ControlBy
}

type ConfigFieldMasterExcelImpReq struct {
	PlanetId int64    `json:"planetId" gorm:"type:bigint;comment:planet"`
	Sheet    string   `json:"sheet" comment:"sheet"`
	Hearer   []string `json:"header" comment:"header"`
	Results  []string `json:"results" comment:"results"`
}

func (s *ConfigFieldMasterInsertReq) Generate(model *models.ConfigFieldMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Sheet = s.Sheet
	model.Field = s.Field
	model.FieldName = s.FieldName
	model.FieldType = s.FieldType
	model.FieldRuleId = s.FieldRuleId
}

func (s *ConfigFieldMasterInsertReq) GetId() interface{} {
	return s.Id
}

type ConfigFieldMasterUpdateReq struct {
	Id          int    `uri:"id" comment:""` //
	PlanetId    int64  `json:"planetId" comment:"planet"`
	Sheet       string `json:"sheet" comment:"sheet"`
	Field       string `json:"field" comment:"字段"`
	FieldName   string `json:"fieldName" comment:"字段名"`
	FieldType   string `json:"fieldType" comment:"字段类型"`
	FieldRuleId string `json:"fieldRuleId" comment:"规则ID"`
	CreatedBy   string `json:"createdBy" comment:""`
	UpdatedBy   string `json:"updatedBy" comment:""`
	common.ControlBy
}

func (s *ConfigFieldMasterUpdateReq) Generate(model *models.ConfigFieldMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Sheet = s.Sheet
	model.Field = s.Field
	model.FieldName = s.FieldName
	model.FieldType = s.FieldType
	model.FieldRuleId = s.FieldRuleId
}

func (s *ConfigFieldMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// ConfigFieldMasterGetReq 功能获取请求参数
type ConfigFieldMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *ConfigFieldMasterGetReq) GetId() interface{} {
	return s.Id
}

// ConfigFieldMasterDeleteReq 功能删除请求参数
type ConfigFieldMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *ConfigFieldMasterDeleteReq) GetId() interface{} {
	return s.Ids
}
