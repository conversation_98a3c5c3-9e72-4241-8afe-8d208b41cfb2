package dto

import (
	"time"

	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type ConfigCheckMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	CheckName      string `form:"checkName"  search:"type:contains;column:check_name;table:config_check_master" comment:"check name"`
	CheckType      int32  `form:"checkType"  search:"type:exact;column:check_type;table:config_check_master" comment:"check类型"`
	PlanetId       int64  `form:"planetId"  search:"type:exact;column:planet_id;table:config_check_master" comment:"planet"`
	ConfigCheckMasterOrder
}

type ConfigCheckMasterOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:config_check_master"`
	PlanetId  int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:config_check_master"`
	CheckName string    `form:"checkNameOrder"  search:"type:order;column:check_name;table:config_check_master"`
	CheckType int32     `form:"checkTypeOrder"  search:"type:order;column:check_type;table:config_check_master"`
	ParamJson string    `form:"paramJsonOrder"  search:"type:order;column:param_json;table:config_check_master"`
	CreateBy  string    `form:"createByOrder"  search:"type:order;column:create_by;table:config_check_master"`
	UpdateBy  string    `form:"updateByOrder"  search:"type:order;column:update_by;table:config_check_master"`
	CreatedBy string    `form:"createdByOrder"  search:"type:order;column:created_by;table:config_check_master"`
	UpdatedBy string    `form:"updatedByOrder"  search:"type:order;column:updated_by;table:config_check_master"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:config_check_master"`
}

func (m *ConfigCheckMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type ConfigCheckMasterInsertReq struct {
	Id        int    `json:"-" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	CheckName string `json:"checkName" comment:"check name"`
	CheckType int32  `json:"checkType" comment:"check类型"`
	ParamJson string `json:"paramJson" comment:"检查属性Json"`
	CreatedBy string `json:"createdBy" comment:""`
	UpdatedBy string `json:"updatedBy" comment:""`
	common.ControlBy
}

func (s *ConfigCheckMasterInsertReq) Generate(model *models.ConfigCheckMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.CheckName = s.CheckName
	model.CheckType = s.CheckType
	model.ParamJson = s.ParamJson
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *ConfigCheckMasterInsertReq) GetId() interface{} {
	return s.Id
}

type ConfigCheckMasterUpdateReq struct {
	Id        int    `uri:"id" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	CheckName string `json:"checkName" comment:"check name"`
	CheckType int32  `json:"checkType" comment:"check类型"`
	ParamJson string `json:"paramJson" comment:"检查属性Json"`
	CreatedBy string `json:"createdBy" comment:""`
	UpdatedBy string `json:"updatedBy" comment:""`
	common.ControlBy
}

func (s *ConfigCheckMasterUpdateReq) Generate(model *models.ConfigCheckMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.CheckName = s.CheckName
	model.CheckType = s.CheckType
	model.ParamJson = s.ParamJson
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *ConfigCheckMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// ConfigCheckMasterGetReq 功能获取请求参数
type ConfigCheckMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *ConfigCheckMasterGetReq) GetId() interface{} {
	return s.Id
}

// ConfigCheckMasterDeleteReq 功能删除请求参数
type ConfigCheckMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *ConfigCheckMasterDeleteReq) GetId() interface{} {
	return s.Ids
}
