package dto

import (
	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
	"time"
)

type TConfigAuditMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	Id             int    `form:"id"  search:"type:contains;column:id;table:t_config_audit_master" comment:""`
	SerialNo       string `form:"serialNo"  search:"type:contains;column:serial_no;table:t_config_audit_master" comment:"流水号"`
	ClientIp       string `form:"clientIp"  search:"type:contains;column:client_ip;table:t_config_audit_master" comment:"终端IP"`
	EndpointName   string `form:"endpointName"  search:"type:contains;column:endpoint_name;table:t_config_audit_master" comment:"终端名"`
	EnvType        int32  `form:"envType"  search:"type:exact;column:env_type;table:t_config_audit_master" comment:"环境"`
	ProductId      int32  `form:"productId"  search:"type:exact;column:product_id;table:t_config_audit_master" comment:"产品ID"`
	ChannelId      int32  `form:"channelId"  search:"type:exact;column:channel_id;table:t_config_audit_master" comment:"渠道ID"`
	FileName       string `form:"fileName"  search:"type:contains;column:file_name;table:t_config_audit_master" comment:"文件名"`
	SheetName      string `form:"sheetName"  search:"type:contains;column:sheet_name;table:t_config_audit_master" comment:"Sheet名"`
	GitName        string `form:"gitName"  search:"type:contains;column:git_name;table:t_config_audit_master" comment:"GIT名"`
	GitLog         string `form:"gitLog"  search:"type:contains;column:git_name;table:t_config_audit_master" comment:"GIT日志"`
	Md5            string `form:"md5"  search:"type:contains;column:md5;table:t_config_audit_master" comment:"MD5"`
	CtxJson        string `form:"ctxJson"  search:"type:contains;column:ctx_json;table:t_config_audit_master" comment:"json内容"`
	Status         int32  `form:"status"  search:"type:exact;column:status;table:t_config_audit_master" comment:"状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃"`
	Remark         string `form:"remark"  search:"type:contains;column:remark;table:t_config_audit_master" comment:"备注"`
	Submitter      int32  `form:"submitter"  search:"type:contains;column:submitter;table:t_config_audit_master" comment:"提交人"`
	Auditor        int32  `form:"auditor"  search:"type:contains;column:auditor;table:t_config_audit_master" comment:"审核人"`
	TConfigAuditMasterOrder
}

type TConfigAuditMasterOrder struct {
	Id           int       `form:"idOrder"  search:"type:order;column:id;table:t_config_audit_master"`
	SerialNo     string    `form:"serialNoOrder"  search:"type:order;column:serial_no;table:t_config_audit_master"`
	ClientIp     string    `form:"clientIpOrder"  search:"type:order;column:client_ip;table:t_config_audit_master"`
	EndpointName string    `form:"endpointNameOrder"  search:"type:order;column:endpoint_name;table:t_config_audit_master"`
	EnvType      int32     `form:"envTypeOrder"  search:"type:order;column:env_type;table:t_config_audit_master"`
	ProductId    int32     `form:"productIdOrder"  search:"type:order;column:product_id;table:t_config_audit_master"`
	ChannelId    int32     `form:"channelIdOrder"  search:"type:order;column:channel_id;table:t_config_audit_master"`
	FileName     string    `form:"fileNameOrder"  search:"type:order;column:file_name;table:t_config_audit_master"`
	SheetName    string    `form:"sheetNameOrder"  search:"type:order;column:sheet_name;table:t_config_audit_master"`
	GitName      string    `form:"gitNameOrder"  search:"type:order;column:git_name;table:t_config_audit_master"`
	GitLog       string    `form:"gitLogOrder"  search:"type:order;column:git_log;table:t_config_audit_master"`
	Md5          string    `form:"md5Order"  search:"type:order;column:md5;table:t_config_audit_master"`
	CtxJson      string    `form:"ctxJsonOrder"  search:"type:order;column:ctx_json;table:t_config_audit_master"`
	Status       int32     `form:"statusOrder"  search:"type:order;column:status;table:t_config_audit_master"`
	Remark       string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_config_audit_master"`
	Submitter    string    `form:"submitterOrder"  search:"type:order;column:submitter;table:t_config_audit_master"`
	Auditor      int32     `form:"auditorOrder"  search:"type:order;column:auditor;table:t_config_audit_master"`
	CreateBy     string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_config_audit_master"`
	UpdateBy     string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_config_audit_master"`
	CreatedAt    time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_config_audit_master"`
	UpdatedAt    time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_config_audit_master"`
	DeletedAt    time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_config_audit_master"`
}

func (m *TConfigAuditMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TConfigAuditMasterInsertReq struct {
	Id           int    `json:"-" comment:""` //
	SerialNo     string `json:"serialNo" comment:"流水号"`
	ClientIp     string `json:"clientIp" comment:"终端IP"`
	EndpointName string `json:"endpointName" comment:"终端名"`
	EnvType      int32  `json:"envType" comment:"环境"`
	ProductId    int32  `json:"productId" comment:"产品ID"`
	ChannelId    int32  `json:"channelId" comment:"渠道ID"`
	FileName     string `json:"fileName" comment:"文件名"`
	SheetName    string `json:"sheetName" comment:"Sheet名"`
	GitName      string `json:"gitName" comment:"Git名"`
	GitLog       string `json:"gitLog" comment:"Git日志"`
	Md5          string `json:"md5" comment:"MD5"`
	CtxJson      string `json:"ctxJson" comment:"json内容"`
	Status       int32  `json:"status" comment:"状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃"`
	Remark       string `json:"remark" comment:"备注"`
	Submitter    int32  `json:"submitter" comment:"提交人"`
	Auditor      int32  `json:"auditor" comment:"审核人"`
	common.ControlBy
}

func (s *TConfigAuditMasterInsertReq) Generate(model *models.TConfigAuditMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.SerialNo = s.SerialNo
	model.ClientIp = s.ClientIp
	model.EndpointName = s.EndpointName
	model.EnvType = s.EnvType
	model.ProductId = s.ProductId
	model.ChannelId = s.ChannelId
	model.FileName = s.FileName
	model.SheetName = s.SheetName
	model.GitName = s.GitName
	model.GitLog = s.GitLog
	model.Md5 = s.Md5
	model.CtxJson = s.CtxJson
	model.Status = s.Status
	model.Remark = s.Remark
	model.Submitter = s.Submitter
	model.Auditor = s.Auditor
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TConfigAuditMasterInsertReq) GetId() interface{} {
	return s.Id
}

type TConfigAuditMasterUpdateReq struct {
	SerialNo     string `json:"serialNo" comment:"流水号"`
	ClientIp     string `json:"clientIp" comment:"终端IP"`
	EndpointName string `json:"endpointName" comment:"终端名"`
	EnvType      int32  `json:"envType" comment:"环境"`
	ProductId    int32  `json:"productId" comment:"产品ID"`
	ChannelId    int32  `json:"channelId" comment:"渠道ID"`
	FileName     string `json:"fileName" comment:"文件名"`
	SheetName    string `json:"sheetName" comment:"Sheet名"`
	GitName      string `json:"gitName" comment:"GitName"`
	GitLog       string `json:"gitLog" comment:"GIT日志"`
	Md5          string `json:"md5" comment:"MD5"`
	CtxJson      string `json:"ctxJson" comment:"json内容"`
	Status       int32  `json:"status" comment:"状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃"`
	Remark       string `json:"remark" comment:"备注"`
	Submitter    int32  `json:"submitter" comment:"提交人"`
	Auditor      int32  `json:"auditor" comment:"审核人"`
	SelectedIds  string `json:"selectedIds" comment:"选中的ID列表"`
	common.ControlBy
}

// TConfigAuditMasterGetReq 功能获取请求参数
type TConfigAuditMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *TConfigAuditMasterGetReq) GetId() interface{} {
	return s.Id
}

func (s *TConfigAuditMasterUpdateReq) Generate(model *models.TConfigAuditMaster) {
	model.SerialNo = s.SerialNo
	model.ClientIp = s.ClientIp
	model.EndpointName = s.EndpointName
	model.EnvType = s.EnvType
	model.ProductId = s.ProductId
	model.ChannelId = s.ChannelId
	model.FileName = s.FileName
	model.SheetName = s.SheetName
	model.GitName = s.GitName
	model.GitLog = s.GitLog
	model.Md5 = s.Md5
	model.CtxJson = s.CtxJson
	model.Status = s.Status
	model.Remark = s.Remark
	model.Submitter = s.Submitter
	model.Auditor = s.Auditor
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}
