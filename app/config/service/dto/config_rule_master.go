package dto

import (
	"time"

	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type ConfigRuleMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	RuleName       string `form:"ruleName"  search:"type:exact;column:rule_name;table:config_rule_master" comment:"rule name"`
	RuleType       string `form:"ruleType"  search:"type:exact;column:rule_type;table:config_rule_master" comment:"规则类型"`
	CheckId        string `form:"checkId"  search:"type:exact;column:check_id;table:config_rule_master" comment:"检查ID"`
	PlanetId       int64  `form:"planetId"  search:"type:exact;column:planet_id;table:config_rule_master" comment:"planet"`
	ConfigRuleMasterOrder
}

type ConfigRuleMasterOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:config_rule_master"`
	PlanetId  string    `form:"planetIdOrder"  search:"type:order;column:planet_id;table:config_rule_master"`
	RuleName  string    `form:"ruleNameOrder"  search:"type:order;column:rule_name;table:config_rule_master"`
	RuleType  string    `form:"ruleTypeOrder"  search:"type:order;column:rule_type;table:config_rule_master"`
	CheckId   string    `form:"checkIdOrder"  search:"type:order;column:check_id;table:config_rule_master"`
	CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:config_rule_master"`
	UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:config_rule_master"`
	CreatedBy string    `form:"createdByOrder"  search:"type:order;column:created_by;table:config_rule_master"`
	UpdatedBy string    `form:"updatedByOrder"  search:"type:order;column:updated_by;table:config_rule_master"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:config_rule_master"`
}

func (m *ConfigRuleMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type ConfigRuleMasterInsertReq struct {
	Id        int    `json:"-" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	RuleName  string `json:"ruleName" comment:"rule name"`
	RuleType  string `json:"ruleType" comment:"规则类型"`
	CheckId   string `json:"checkId" comment:"检查ID"`
	CreatedBy string `json:"createdBy" comment:""`
	UpdatedBy string `json:"updatedBy" comment:""`
	common.ControlBy
}

func (s *ConfigRuleMasterInsertReq) Generate(model *models.ConfigRuleMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.RuleName = s.RuleName
	model.RuleType = s.RuleType
	model.CheckId = s.CheckId
}

func (s *ConfigRuleMasterInsertReq) GetId() interface{} {
	return s.Id
}

type ConfigRuleMasterUpdateReq struct {
	Id        int    `uri:"id" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	RuleName  string `json:"ruleName" comment:"rule name"`
	RuleType  string `json:"ruleType" comment:"规则类型"`
	CheckId   string `json:"checkId" comment:"检查ID"`
	CreatedBy string `json:"createdBy" comment:""`
	UpdatedBy string `json:"updatedBy" comment:""`
	common.ControlBy
}

func (s *ConfigRuleMasterUpdateReq) Generate(model *models.ConfigRuleMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.RuleName = s.RuleName
	model.RuleType = s.RuleType
	model.CheckId = s.CheckId
}

func (s *ConfigRuleMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// ConfigRuleMasterGetReq 功能获取请求参数
type ConfigRuleMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *ConfigRuleMasterGetReq) GetId() interface{} {
	return s.Id
}

// ConfigRuleMasterDeleteReq 功能删除请求参数
type ConfigRuleMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *ConfigRuleMasterDeleteReq) GetId() interface{} {
	return s.Ids
}
