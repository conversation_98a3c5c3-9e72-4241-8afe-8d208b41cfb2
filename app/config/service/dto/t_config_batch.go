package dto

type UploadInfo struct {
	SerialNo     string      `json:"serialNo" comment:"流水号"`
	EndpointName string      `json:"endpointName" comment:"终端名"`
	EnvType      int         `json:"envType" comment:"环境"`
	ProductId    int         `json:"productId" comment:"产品ID"`
	ChannelId    int32       `json:"channelId" comment:"渠道ID"`
	GitName      string      `json:"git_name" comment:"git账号名"`
	GitLog       string      `json:"git_log" comment:"git日志"`
	ClientIp     string      `json:"client_ip" comment:"客户端日志"`
	RowCnt       int         `json:"row_cnt" comment:"总行数"`
	RowIndex     int         `json:"row_index" comment:"当前行号"`
	ExcelSheet   *ExcelSheet `json:"sheet" comment:"上传信息"`
	ChannelStr   string      `json:"channel_str" comment:"提交的渠道列表,隔开"`
	TableType    string      `json:"table_type" comment:"表类型"`
}

type ExcelSheet struct {
	FileName  string `json:"fileName" comment:"文件名"`
	SheetName string `json:"sheetName" comment:"Sheet名"`
	Md5       string `json:"md5" comment:"MD5"`
	CtxJson   string `json:"ctxJson" comment:"Json内容"`
}

type AuditInfo struct {
	EnvType     int32  `form:"envType" json:"envType" comment:"环境"`
	ProductId   int32  `form:"productId" json:"productId" comment:"产品ID"`
	ChannelId   int32  `form:"channelId" json:"channelId" comment:"渠道ID"`
	Remark      string `form:"remark" json:"remark" comment:"备注"`
	AuditBy     int32  `form:"auditBy" comment:"审核"`
	SelectedIds string `form:"selectedIds" comment:"选中的ID列表"`
}
