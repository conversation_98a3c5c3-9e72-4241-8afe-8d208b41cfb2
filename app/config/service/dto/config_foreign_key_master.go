package dto

import (
	"time"

	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type ConfigForeignKeyMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	Sheet          string `form:"sheet"  search:"type:contains;column:sheet;table:config_foreign_key_master" comment:"sheet"`
	FileName       string `form:"fileName"  search:"type:contains;column:file_name;table:config_foreign_key_master" comment:"file name"`
	Field          string `form:"field"  search:"type:contains;column:field;table:config_foreign_key_master" comment:"field"`
	FieldName      string `form:"fieldName"  search:"type:contains;column:field_name;table:config_foreign_key_master" comment:"field name"`
	PlanetId       int64  `form:"planetId"  search:"type:exact;column:planet_id;table:config_foreign_key_master" comment:"planet"`
	ConfigForeignKeyMasterOrder
}

type ConfigForeignKeyMasterOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:config_foreign_key_master"`
	PlanetId  int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:config_foreign_key_master"`
	Sheet     string    `form:"sheetOrder"  search:"type:order;column:sheet;table:config_foreign_key_master"`
	FileName  string    `form:"fileNameOrder"  search:"type:order;column:file_name;table:config_foreign_key_master"`
	Field     string    `form:"fieldOrder"  search:"type:order;column:field;table:config_foreign_key_master"`
	FieldName string    `form:"fieldNameOrder"  search:"type:order;column:field_name;table:config_foreign_key_master"`
	ParamJson string    `form:"paramJsonOrder"  search:"type:order;column:param_json;table:config_foreign_key_master"`
	CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:config_foreign_key_master"`
	UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:config_foreign_key_master"`
	CreateBy  string    `form:"createByOrder"  search:"type:order;column:create_by;table:config_foreign_key_master"`
	UpdateBy  string    `form:"updateByOrder"  search:"type:order;column:update_by;table:config_foreign_key_master"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:config_foreign_key_master"`
}

func (m *ConfigForeignKeyMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type ConfigForeignKeyMasterInsertReq struct {
	Id        int    `json:"-" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	Sheet     string `json:"sheet" comment:"sheet"`
	FileName  string `json:"fileName" comment:"fileName"`
	Field     string `json:"field" comment:"field"`
	FieldName string `json:"fieldName" comment:"field name"`
	ParamJson string `json:"paramJson" comment:"检查ID json集合"`
	common.ControlBy
}

func (s *ConfigForeignKeyMasterInsertReq) Generate(model *models.ConfigForeignKeyMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Sheet = s.Sheet
	model.FileName = s.FileName
	model.Field = s.Field
	model.FieldName = s.FieldName
	model.ParamJson = s.ParamJson
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *ConfigForeignKeyMasterInsertReq) GetId() interface{} {
	return s.Id
}

type ConfigForeignKeyMasterUpdateReq struct {
	Id        int    `uri:"id" comment:""` //
	PlanetId  int64  `json:"planetId" comment:"planet"`
	Sheet     string `json:"sheet" comment:"sheet"`
	FileName  string `json:"fileName" comment:"fileName"`
	SheetName string `json:"sheetName" comment:"sheet"`
	Field     string `json:"field" comment:"field"`
	FieldName string `json:"fieldName" comment:"field name"`
	ParamJson string `json:"paramJson" comment:"检查ID json集合"`
	common.ControlBy
}

func (s *ConfigForeignKeyMasterUpdateReq) Generate(model *models.ConfigForeignKeyMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Sheet = s.Sheet
	model.FileName = s.FileName
	model.Field = s.Field
	model.FieldName = s.FieldName
	model.ParamJson = s.ParamJson
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *ConfigForeignKeyMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// ConfigForeignKeyMasterGetReq 功能获取请求参数
type ConfigForeignKeyMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *ConfigForeignKeyMasterGetReq) GetId() interface{} {
	return s.Id
}

// ConfigForeignKeyMasterDeleteReq 功能删除请求参数
type ConfigForeignKeyMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *ConfigForeignKeyMasterDeleteReq) GetId() interface{} {
	return s.Ids
}

type ConfigForeignKeyMasterExcelImpReq struct {
	PlanetId   int64    `json:"planetId" gorm:"type:bigint;comment:planet"`
	Sheet      string   `json:"sheet" comment:"sheet"`
	Hearer     []string `json:"header" comment:"header"`
	Results    []string `json:"results" comment:"results"`
	ParamJsons []string `json:"paramJsons" comment:"ParamJsons"`
	IsEnum     bool     `json:"isEnum" comment:"isEnum"`
}
