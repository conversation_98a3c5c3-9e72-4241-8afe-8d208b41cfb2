package dto

import (
	"fmt"
	"time"

	"go-admin/app/config/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TConfigOperateMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	Id             int    `form:"id"  search:"type:contains;column:id;table:t_config_operate_master" comment:""`
	SerialNo       string `form:"serialNo"  search:"type:contains;column:serial_no;table:t_config_operate_master" comment:"流水号"`
	ClientIp       string `form:"clientIp"  search:"type:contains;column:client_ip;table:t_config_operate_master" comment:"终端IP"`
	EndpointName   string `form:"endpointName"  search:"type:contains;column:endpoint_name;table:t_config_operate_master" comment:"终端名"`
	EnvType        int32  `form:"envType"  search:"type:exact;column:env_type;table:t_config_operate_master" comment:"环境"`
	ProductId      int32  `form:"productId"  search:"type:exact;column:product_id;table:t_config_operate_master" comment:"产品ID"`
	ChannelId      int32  `form:"channelId"  search:"type:exact;column:channel_id;table:t_config_operate_master" comment:"渠道ID"`
	FileName       string `form:"fileName"  search:"type:exact;column:file_name;table:t_config_operate_master" comment:"文件名"`
	SheetName      string `form:"sheetName"  search:"type:exact;column:sheet_name;table:t_config_operate_master" comment:"Sheet名"`
	GitName        string `form:"gitName"  search:"type:exact;column:git_name;table:t_config_operate_master" comment:"GitName"`
	GitLog         string `form:"gitLog"  search:"type:exact;column:git_log;table:t_config_operate_master" comment:"GitName"`
	Md5            string `form:"md5"  search:"type:exact;column:md5;table:t_config_operate_master" comment:"MD5"`
	CtxJson        string `form:"ctxJson"  search:"type:contains;column:ctx_json;table:t_config_operate_master" comment:"Json内容"`
	Remark         string `form:"remark"  search:"type:contains;column:remark;table:t_config_operate_master" comment:"备注"`
	Status         int32  `form:"status" search:"type:int(11);comment:状态"`
	HadModify      int    `form:"hadModify" search:"-"  comment:"是否修改"`
	TConfigOperateMasterOrder
}

type TConfigOperateMasterOrder struct {
	Id           int       `form:"idOrder"  search:"type:order;column:id;table:t_config_operate_master"`
	SerialNo     string    `form:"serialNoOrder"  search:"type:order;column:serial_no;table:t_config_operate_master"`
	ClientIp     string    `form:"clientIpOrder"  search:"type:order;column:client_ip;table:t_config_operate_master"`
	EndpointName string    `form:"endpointNameOrder"  search:"type:order;column:endpoint_name;table:t_config_operate_master"`
	EnvType      int32     `form:"envTypeOrder"  search:"type:order;column:env_type;table:t_config_operate_master"`
	ProductId    int32     `form:"productIdOrder"  search:"type:order;column:product_id;table:t_config_operate_master"`
	ChannelId    int32     `form:"channelIdOrder"  search:"type:order;column:channel_id;table:t_config_operate_master"`
	FileName     string    `form:"fileNameOrder"  search:"type:order;column:file_name;table:t_config_operate_master"`
	SheetName    string    `form:"sheetNameOrder"  search:"type:order;column:sheet_name;table:t_config_operate_master"`
	GitName      string    `form:"gitName"  search:"type:contains;column:git_name;table:t_config_operate_master"`
	GitLog       string    `form:"gitLog"  search:"type:contains;column:git_log;table:t_config_operate_master"`
	Md5          string    `form:"md5Order"  search:"type:order;column:md5;table:t_config_operate_master"`
	CtxJson      string    `form:"ctxJsonOrder"  search:"type:order;column:ctx_json;table:t_config_operate_master"`
	Remark       string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_config_operate_master"`
	CreateBy     string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_config_operate_master"`
	UpdateBy     string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_config_operate_master"`
	CreatedAt    time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_config_operate_master"`
	UpdatedAt    time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_config_operate_master"`
	DeletedAt    time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_config_operate_master"`
}

func (m *TConfigOperateMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TConfigOperateMasterInsertReq struct {
	Id           int    `json:"-" comment:""` //
	SerialNo     string `json:"serialNo" comment:"流水号"`
	ClientIp     string `json:"clientIp" comment:"终端IP"`
	EndpointName string `json:"endpointName" comment:"终端名"`
	EnvType      int32  `json:"envType" comment:"环境"`
	ProductId    int32  `json:"productId" comment:"产品ID"`
	ChannelId    int32  `json:"channelId" comment:"渠道ID"`
	FileName     string `json:"fileName" comment:"文件名"`
	SheetName    string `json:"sheetName" comment:"Sheet名"`
	GitName      string `json:"gitName" comment:"GitName"`
	GitLog       string `json:"gitLog" comment:"GitName"`
	Md5          string `json:"md5" comment:"MD5"`
	CtxJson      string `json:"ctxJson" comment:"Json内容"`
	Remark       string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TConfigOperateMasterInsertReq) Generate(model *models.TConfigOperateMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.SerialNo = s.SerialNo
	model.ClientIp = s.ClientIp
	model.EndpointName = s.EndpointName
	model.EnvType = s.EnvType
	model.ProductId = s.ProductId
	model.ChannelId = s.ChannelId
	model.FileName = s.FileName
	model.SheetName = s.SheetName
	model.GitName = s.GitName
	model.GitLog = s.GitLog
	model.Md5 = s.Md5
	model.CtxJson = s.CtxJson
	model.Remark = s.Remark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TConfigOperateMasterInsertReq) GetId() interface{} {
	return s.Id
}

type TConfigOperateMasterUpdateReq struct {
	Id           int    `uri:"id" comment:""` //
	SerialNo     string `json:"serialNo" comment:"流水号"`
	ClientIp     string `json:"clientIp" comment:"终端IP"`
	EndpointName string `json:"endpointName" comment:"终端名"`
	EnvType      int32  `json:"envType" comment:"环境"`
	ProductId    int32  `json:"productId" comment:"产品ID"`
	ChannelId    int32  `json:"channelId" comment:"渠道ID"`
	FileName     string `json:"fileName" comment:"文件名"`
	SheetName    string `json:"sheetName" comment:"Sheet名"`
	GitName      string `json:"gitName" comment:"GitName"`
	GitLog       string `json:"gitLog" comment:"GitLog"`
	Md5          string `json:"md5" comment:"MD5"`
	CtxJson      string `json:"ctxJson" comment:"Json内容"`
	Remark       string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TConfigOperateMasterUpdateReq) Generate(model *models.TConfigOperateMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.SerialNo = s.SerialNo
	model.ClientIp = s.ClientIp
	model.EndpointName = s.EndpointName
	model.EnvType = s.EnvType
	model.ProductId = s.ProductId
	model.ChannelId = s.ChannelId
	model.FileName = s.FileName
	model.SheetName = s.SheetName
	model.GitName = s.GitName
	model.GitLog = s.GitLog
	model.Md5 = s.Md5
	model.CtxJson = s.CtxJson
	model.Remark = s.Remark
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TConfigOperateMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// TConfigOperateMasterGetReq 功能获取请求参数
type TConfigOperateMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *TConfigOperateMasterGetReq) GetId() interface{} {
	return s.Id
}

// TConfigOperateMasterDeleteReq 功能删除请求参数
type TConfigOperateMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TConfigOperateMasterDeleteReq) GetId() interface{} {
	return s.Ids
}

// GenerateConfigKey 辅助函数，返回唯一健
func GenerateConfigKey(envType, productId, channelId int32, fileName, sheetName, md5, tableType string) string {
	return fmt.Sprintf("%d_%d_%d_%s_%s_%s_%s", envType, productId, channelId, fileName, sheetName, md5, tableType)
}
