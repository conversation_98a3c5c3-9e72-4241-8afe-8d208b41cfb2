package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type ConfigCheckMaster struct {
	service.Service
}

// GetPage 获取ConfigCheckMaster列表
func (e *ConfigCheckMaster) GetPage(c *dto.ConfigCheckMasterGetPageReq, p *actions.DataPermission, list *[]models.ConfigCheckMaster, count *int64) error {
	var err error
	var data models.ConfigCheckMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("ConfigCheckMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取ConfigCheckMaster对象
func (e *ConfigCheckMaster) Get(d *dto.ConfigCheckMasterGetReq, p *actions.DataPermission, model *models.ConfigCheckMaster) error {
	var data models.ConfigCheckMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetConfigCheckMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建ConfigCheckMaster对象
func (e *ConfigCheckMaster) Insert(c *dto.ConfigCheckMasterInsertReq) error {
	var err error
	var data models.ConfigCheckMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("ConfigCheckMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改ConfigCheckMaster对象
func (e *ConfigCheckMaster) Update(c *dto.ConfigCheckMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.ConfigCheckMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("ConfigCheckMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除ConfigCheckMaster
func (e *ConfigCheckMaster) Remove(d *dto.ConfigCheckMasterDeleteReq, p *actions.DataPermission) error {
	var data models.ConfigCheckMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveConfigCheckMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}

// Find ConfigFieldMaster by channel
func (e *ConfigCheckMaster) Find() (data []models.ConfigCheckMaster, err error) {

	err = e.Orm.Find(&data).Error

	return data, err
}
