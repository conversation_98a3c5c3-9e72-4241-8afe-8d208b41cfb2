package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type ConfigFieldMaster struct {
	service.Service
}

// GetPage 获取ConfigFieldMaster列表
func (e *ConfigFieldMaster) GetPage(c *dto.ConfigFieldMasterGetPageReq, p *actions.DataPermission, list *[]models.ConfigFieldMaster, count *int64) error {
	var err error
	var data models.ConfigFieldMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("ConfigFieldMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取ConfigFieldMaster对象
func (e *ConfigFieldMaster) Get(d *dto.ConfigFieldMasterGetReq, p *actions.DataPermission, model *models.ConfigFieldMaster) error {
	var data models.ConfigFieldMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetConfigFieldMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// IsExist Field是否存在
func (e *ConfigFieldMaster) IsExist(sheet string, field string, planetID int64, model *models.ConfigFieldMaster) bool {
	var data models.ConfigFieldMaster

	err := e.Orm.Model(&data).Where("planet_id = ? and field = ? and sheet = ?", planetID, field, sheet).
		First(model).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return false
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return false
	}
	return true
}

// Insert 创建ConfigFieldMaster对象
func (e *ConfigFieldMaster) Insert(c *dto.ConfigFieldMasterInsertReq) error {
	var err error
	var data models.ConfigFieldMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("ConfigFieldMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改ConfigFieldMaster对象
func (e *ConfigFieldMaster) Update(c *dto.ConfigFieldMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.ConfigFieldMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("ConfigFieldMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除ConfigFieldMaster
func (e *ConfigFieldMaster) Remove(d *dto.ConfigFieldMasterDeleteReq, p *actions.DataPermission) error {
	var data models.ConfigFieldMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveConfigFieldMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}

// Find ConfigFieldMaster by channel
func (e *ConfigFieldMaster) Find() (data []models.ConfigFieldMaster, err error) {

	err = e.Orm.Find(&data).Error

	return data, err
}
