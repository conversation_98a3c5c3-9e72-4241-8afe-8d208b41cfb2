package service

import (
	"errors"
	"go-admin/app/config/define"
	"go-admin/app/config/models"
	"go-admin/app/config/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
	"go-admin/core/sdk/service"
	"gorm.io/gorm"
)

type TConfigAuditMaster struct {
	service.Service
}

// GetPage 获取TConfigAuditMaster列表
func (e *TConfigAuditMaster) GetPage(c *dto.TConfigAuditMasterGetPageReq, p *actions.DataPermission, list *[]models.TConfigAuditMaster, count *int64) error {
	var err error
	var data models.TConfigAuditMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()), actions.Permission(data.TableName(), p)).
		Order("created_at desc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>("TConfigAuditMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Insert 创建TConfigAuditMaster对象
func (e *TConfigAuditMaster) Insert(c *dto.TConfigAuditMasterInsertReq) error {
	var err error
	var data models.TConfigAuditMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TConfigAuditMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TConfigAuditMaster对象
func (e *TConfigAuditMaster) Update(c *dto.TConfigAuditMasterUpdateReq, p *actions.DataPermission) error {
	var dataBean models.TConfigAuditMaster
	err := e.Orm.Model(&dataBean).Where("product_id = ? and channel_id = ? and env_type=?",
		c.ProductId, c.ChannelId, c.EnvType).Update("status", define.StatusPublished).Error
	if err != nil {
		return err
	}
	return nil
}

// Get 获取TConfigAuditMaster对象
func (e *TConfigAuditMaster) Get(d *dto.TConfigAuditMasterGetReq, p *actions.DataPermission, model *models.TConfigAuditMaster) error {
	var data models.TConfigAuditMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTConfigAuditMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}
