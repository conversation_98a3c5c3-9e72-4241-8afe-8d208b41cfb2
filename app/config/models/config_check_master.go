package models

import (
	"go-admin/common/models"
)

type ConfigCheckMaster struct {
	models.Model

	PlanetId  int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	CheckName string `json:"checkName" gorm:"type:varchar(32);comment:check name"`
	CheckType int32  `json:"checkType" gorm:"type:int;comment:check类型"`
	ParamJson string `json:"paramJson" gorm:"type:text;comment:检查属性Json"`
	models.ModelTime
	models.ControlBy
}

func (ConfigCheckMaster) TableName() string {
	return "config_check_master"
}

func (e *ConfigCheckMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ConfigCheckMaster) GetId() interface{} {
	return e.Id
}
