package models

import (
	"go-admin/common/models"
)

type ConfigFieldMaster struct {
	models.Model

	PlanetId    int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	Sheet       string `json:"sheet" gorm:"type:varchar(32);comment:sheet"`
	Field       string `json:"field" gorm:"type:varchar(32);comment:字段"`
	FieldName   string `json:"fieldName" gorm:"type:varchar(32);comment:字段名"`
	FieldType   string `json:"fieldType" gorm:"type:int;comment:字段类型"`
	FieldRuleId string `json:"fieldRuleId" gorm:"type:int;comment:规则ID"`
	models.ModelTime
	models.ControlBy
}

func (ConfigFieldMaster) TableName() string {
	return "config_field_master"
}

func (e *ConfigFieldMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ConfigFieldMaster) GetId() interface{} {
	return e.Id
}
