package models

import (
	"go-admin/common/models"
)

type ConfigForeignKeyMaster struct {
	models.Model

	PlanetId  int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	Sheet     string `json:"sheet" gorm:"type:varchar(32);comment:sheet"`
	FileName  string `json:"fileName" gorm:"type:varchar(32);comment:fileName"`
	Field     string `json:"field" gorm:"type:varchar(32);comment:field"`
	FieldName string `json:"fieldName" gorm:"type:varchar(32);comment:field name"`
	ParamJson string `json:"paramJson" gorm:"type:varchar(255);comment:检查ID json集合"`
	models.ModelTime
	models.ControlBy
}

// ConfigForeignKeyMaster2 传给前端用，区分更新到数据库
type ConfigForeignKeyMaster2 struct {
	models.Model

	PlanetId     int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	Sheet        string `json:"sheet" gorm:"type:varchar(32);comment:sheet"`
	FileName     string `json:"fileName" gorm:"type:varchar(32);comment:fileName"`
	Field        string `json:"field" gorm:"type:varchar(32);comment:field"`
	FieldName    string `json:"fieldName" gorm:"type:varchar(32);comment:field name"`
	ParamJson    string `json:"paramJson" gorm:"type:varchar(255);comment:检查ID json集合"`
	ParamJsonReq string `json:"paramJsonReq"`
	models.ModelTime
	models.ControlBy
}

func (ConfigForeignKeyMaster) TableName() string {
	return "config_foreign_key_master"
}

func (e *ConfigForeignKeyMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ConfigForeignKeyMaster) GetId() interface{} {
	return e.Id
}
