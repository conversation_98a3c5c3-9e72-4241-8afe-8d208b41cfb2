package models

import (
	"go-admin/common/models"
)

type TConfigAuditMaster struct {
	models.Model

	SerialNo     string `json:"serialNo" gorm:"type:varchar(20);comment:流水号"`
	ClientIp     string `json:"clientIp" gorm:"type:varchar(11);comment:终端IP"`
	EndpointName string `json:"endpointName" gorm:"type:varchar(100);comment:终端名"`
	EnvType      int32  `json:"envType" gorm:"type:int(11);comment:环境"`
	ProductId    int32  `json:"productId" gorm:"type:int(11);comment:产品ID"`
	ChannelId    int32  `json:"channelId" gorm:"type:int(11);comment:渠道ID"`
	FileName     string `json:"fileName" gorm:"type:varchar(255);comment:文件名"`
	SheetName    string `json:"sheetName" gorm:"type:varchar(255);comment:Sheet名"`
	GitName      string `json:"gitName" gorm:"type:varchar(255);comment:GIT名"`
	GitLog       string `json:"gitLog" gorm:"type:varchar(255);comment:GIT日志"`
	Md5          string `json:"md5" gorm:"type:varchar(100);comment:MD5"`
	CtxJson      string `json:"ctxJson" gorm:"type:text;comment:json内容"`
	Status       int32  `json:"status" gorm:"type:int(11);comment:状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃"`
	Remark       string `json:"remark" gorm:"type:varchar(255);comment:备注"`
	Submitter    int32  `json:"submitter" gorm:"type:bigint(20);comment:提交人"`
	Auditor      int32  `json:"auditor" gorm:"type:bigint(20);comment:审核人"`
	TableType    string `json:"table_type" gorm:"type:varchar(50);comment:表类型"`
	UserName     string `json:"user_name" gorm:"type:varchar(255);comment:表类型"`
	models.ModelTime
	models.ControlBy
}

func (e *TConfigAuditMaster) TableName() string {
	return "t_config_audit_master"
}

func (e *TConfigAuditMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TConfigAuditMaster) GetId() interface{} {
	return e.Id
}

func (e *TConfigAuditMaster) ToConfigOperator() *TConfigOperateMaster {
	return &TConfigOperateMaster{
		SerialNo:     e.SerialNo,
		ClientIp:     e.ClientIp,
		EndpointName: e.EndpointName,
		EnvType:      e.EnvType,
		ProductId:    e.ProductId,
		ChannelId:    e.ChannelId,
		FileName:     e.FileName,
		SheetName:    e.SheetName,
		GitName:      e.GitName,
		GitLog:       e.GitLog,
		Md5:          e.Md5,
		CtxJson:      e.CtxJson,
		Status:       0,
		Remark:       "自动生成",
		//Submitter:    e.Auditor,
	}
}
