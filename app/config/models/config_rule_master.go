package models

import (
	"go-admin/common/models"
)

type ConfigRuleMaster struct {
    models.Model

	PlanetId    int64     `json:"planetId" gorm:"type:bigint;comment:planet"`
    RuleName string `json:"ruleName" gorm:"type:varchar(32);comment:rule name"` 
    RuleType string `json:"ruleType" gorm:"type:int;comment:规则类型"` 
    CheckId string `json:"checkId" gorm:"type:int;comment:检查ID"`
    models.ModelTime
    models.ControlBy
}

func (ConfigRuleMaster) TableName() string {
    return "config_rule_master"
}

func (e *ConfigRuleMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ConfigRuleMaster) GetId() interface{} {
	return e.Id
}