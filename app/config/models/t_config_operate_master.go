package models

import (
	"go-admin/common/models"
	"time"
)

type TConfigOperateMaster struct {
	models.Model

	SerialNo     string                `json:"serialNo" gorm:"type:varchar(11);comment:流水号"`
	ClientIp     string                `json:"clientIp" gorm:"type:varchar(11);comment:终端IP"`
	EndpointName string                `json:"endpointName" gorm:"type:varchar(20);comment:终端名"`
	EnvType      int32                 `json:"envType" gorm:"type:int(11);comment:环境"`
	ProductId    int32                 `json:"productId" gorm:"type:int(11);comment:产品ID"`
	ChannelId    int32                 `json:"channelId" gorm:"type:int(11);comment:渠道ID"`
	FileName     string                `json:"fileName" gorm:"type:varchar(20);comment:文件名"`
	SheetName    string                `json:"sheetName" gorm:"type:varchar(20);comment:Sheet名"`
	GitName      string                `json:"gitName" gorm:"type:varchar(255);comment:GIT名"`
	GitLog       string                `json:"gitLog" gorm:"type:varchar(255);comment:GIT日志"`
	Md5          string                `json:"md5" gorm:"type:varchar(20);comment:MD5"`
	CtxJson      string                `json:"ctxJson" gorm:"type:text;comment:Json内容"`
	Status       int32                 `json:"status" gorm:"type:int(11);comment:状态"`
	Remark       string                `json:"remark" gorm:"type:varchar(255);comment:备注"`
	TableType    string                `json:"table_type" gorm:"type:varchar(50);comment:表类型"`
	Record       []OperateRecord       `json:"record" gorm:"-"`
	Release      *TConfigOperateMaster `json:"release" gorm:"-"`
	Change       int32                 `json:"change" gorm:"-"`

	models.ModelTime
	models.ControlBy
}

type OperateRecord struct {
	CreatedAt    time.Time `json:"createdAt"`
	EndpointName string    `json:"endpointName"`
	GitName      string    `json:"gitName"`
	GitLog       string    `json:"gitLog"`
	Status       int32     `json:"status"`
}

func (e *TConfigOperateMaster) TableName() string {
	return "t_config_operate_master"
}

func (e *TConfigOperateMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TConfigOperateMaster) GetId() interface{} {
	return e.Id
}
