package define

// 状态：0:待发布 1：覆盖 2：待审核 3:已发布 4:废弃
const (
	StatusWaitPublish int32 = 0 // daf:待发布
	StatusOverwrite   int32 = 1 // daf:覆盖
	StatusWaitAudit   int32 = 2 // daf:待审核
	StatusPublished   int32 = 3 // daf:已发布
	StatusDiscard     int32 = 4 // daf:废弃
)

const S3Prefix = "env"
const HadModifyVal = 1 // 有修改

const AesKey = "##fancygame666##"

const AdminUid = 1

const (
	TableTypeServerOnly = "server_only"
	TableTypeClientOnly = "client_only"
)

// Manifest文件名常量
const (
	ConfigManifest        = "config_manifest"
	ConfigManifestRelease = "config_manifest_release"
)
