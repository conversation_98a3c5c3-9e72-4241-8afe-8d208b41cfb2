package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/config/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTConfigOperateMasterRouter)
}

// registerTConfigOperateMasterRouter
func registerTConfigOperateMasterRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TConfigOperateMaster{}
	r := v1.Group("/t-config-operate-master").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
		r.DELETE("", api.Delete)
	}

	rHandler := v1.Group("/config-handler").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		rHandler.GET("/:id", actions.PermissionAction(), api.Get)
		rHandler.GET("", actions.PermissionAction(), api.CommitDone)
	}

	rUploadCfg := v1.Group("/config")
	{
		rUploadCfg.POST("/batch-upload", api.BatchUpload)
	}
}
