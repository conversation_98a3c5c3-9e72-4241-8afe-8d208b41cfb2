package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/config/apis"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTConfigWolongRouter)
}

// registerTConfigAuditMasterRouter
func registerTConfigWolongRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.ConfigCheckWolong{}
	r := v1.Group("/config")
	{
		r.GET("/t-config-check-wolong", api.GetALL)
	}
}
