package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/config/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerConfigCheckMasterRouter)
}

// registerConfigCheckMasterRouter
func registerConfigCheckMasterRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.ConfigCheckMaster{}
	r := v1.Group("/config-check-master").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
		r.DELETE("", api.Delete)
	}
	// rc := v1.Group("/config-check")
}
