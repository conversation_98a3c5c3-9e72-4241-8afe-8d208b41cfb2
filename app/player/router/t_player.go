package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/player/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTPlayerRouter)
}

// registerTPlayerRouter
func registerTPlayerRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TPlayer{}
	r := v1.Group("/player").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
	}
}
