package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/player/models"
	"go-admin/app/player/service"
	"go-admin/app/player/service/dto"
	"go-admin/common/actions"
)

type TBasePropOds struct {
	api.Api
}

// GetPage 获取TBasePropOds列表
// @Summary 获取TBasePropOds列表
// @Description 获取TBasePropOds列表
// @Tags TBasePropOds
// @Param platform query string false "平台"
// @Param timeStampValue query time.Time false "时间戳"
// @Param appVersion query string false "App版本"
// @Param uid query string false "玩家ID"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TBasePropOds}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-base-prop-ods [get]
// @Security Bearer
func (e TBasePropOds) GetPage(c *gin.Context) {
	req := dto.TBasePropOdsGetPageReq{}
	s := service.TBasePropOds{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.RItemOds, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TBasePropOds 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TBasePropOds
// @Summary 获取TBasePropOds
// @Description 获取TBasePropOds
// @Tags TBasePropOds
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TBasePropOds} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-base-prop-ods/{id} [get]
// @Security Bearer
func (e TBasePropOds) Get(c *gin.Context) {
	req := dto.TBasePropOdsGetReq{}
	s := service.TBasePropOds{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.RItemOds

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TBasePropOds失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}
