package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/app/player/service"
	"go-admin/app/player/service/dto"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"
)

type TPlayer struct {
	api.Api
}

// GetPage 获取TPlayer列表
// @Summary 获取TPlayer列表
// @Description 获取TPlayer列表
// @Tags TPlayer
// @Param uid query int64 false "玩家Id"
// @Param name query string false "玩家名称"
// @Param lastLoginTime query time.Time false "时间戳"
// @Param fbId query string false "FBID"
// @Param deviceId query string false "device_id"
// @Param adjustId query string false "adjust_id"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TPlayer}} "{"code": 200, "data": [...]}"
// @Router /api/v1/player [get]
// @Security Bearer
func (e TPlayer) GetPage(c *gin.Context) {
	req := dto.TPlayerGetPageReq{}
	s := service.TPlayer{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("获取TPlayer req=%+v", req)

	res, err := s.GetPage(c, &req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TPlayer 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(res.PlayerInfo, res.Count, req.GetPageIndex(), req.GetPageSize(), "查询成功")
}
