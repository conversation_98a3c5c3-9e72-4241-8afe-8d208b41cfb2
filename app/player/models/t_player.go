package models

// Response 结构体表示服务器响应，包含状态码和数
type Response struct {
	Code int `json:"code"`
	Data struct {
		Ret struct {
			Desc string `json:"desc"`
		} `json:"ret"`
		Data string `json:"data"`
	} `json:"data"`
}

// InnerData 结构体表示内部数据，包含返回信息、玩家信息和分页信息
type InnerData struct {
	Ret        Ret          `json:"ret"`         // Ret 是返回信息
	PlayerInfo []PlayerInfo `json:"player_info"` // PlayerInfo 是玩家信息列表
	Count      int          `json:"count"`       // 总数
}

// PlayerInfo 结构体表示玩家信息
type PlayerInfo struct {
	BriefUserInfo  *BriefUserInfo  `protobuf:"bytes,1,opt,name=brief_user_info,json=briefUserInfo,proto3" json:"brief_user_info"`                     // 玩家简要信息
	BanAccountInfo *BanAccountInfo `protobuf:"bytes,2,opt,name=ban_account_info,json=banAccountInfo,proto3" json:"ban_account_info"`                  // 封号信息
	AppVersion     string          `protobuf:"bytes,3,opt,name=app_version,json=appVersion,proto3" json:"app_version"`                                // 版本号
	AccType        int32           `protobuf:"varint,5,opt,name=acc_type,json=accType,proto3,enum=common.ACC_TYPE" json:"acc_type"`                   // 账号类型，区别于登录类型
	RegisterTime   int64           `protobuf:"varint,7,opt,name=register_time,json=registerTime,proto3" json:"register_time"`                         // 注册时间
	Platform       int32           `protobuf:"varint,9,opt,name=platform,proto3,enum=common.PLATFORM_TYPE" json:"platform"`                           // 平台类型
	AppLanguage    int32           `protobuf:"varint,12,opt,name=app_language,json=appLanguage,proto3,enum=common.LANGUAGE_TYPE" json:"app_language"` // 多语言
	RealNameAuth   bool            `protobuf:"varint,13,opt,name=real_name_auth,json=realNameAuth,proto3" json:"real_name_auth"`                      // 实名状态 TODO:delete
}

// BriefUserInfo 结构体表示玩家的基本用户信息
type BriefUserInfo struct {
	PlayerId       uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id"`                      // 玩家id
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`                                               // 名字
	Avatar         int64  `protobuf:"varint,3,opt,name=avatar,proto3" json:"avatar"`                                          // 游戏内置头像编号
	AvatarUrl      string `protobuf:"bytes,4,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url"`                    // 第三方头像地址
	Frame          int64  `protobuf:"varint,7,opt,name=frame,proto3" json:"frame"`                                            // 游戏内头像框
	Lev            int32  `protobuf:"varint,8,opt,name=lev,proto3" json:"lev"`                                                // 用户等级
	Country        string `protobuf:"bytes,9,opt,name=country,proto3" json:"country"`                                         // 国家
	LastLoginTime  int64  `protobuf:"varint,10,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time"`    // 最后登录时间
	LastLogoutTime int64  `protobuf:"varint,11,opt,name=last_logout_time,json=lastLogoutTime,proto3" json:"last_logout_time"` // 最后退出游戏时间
	LastDeviceCode string `protobuf:"bytes,12,opt,name=last_device_code,json=lastDeviceCode,proto3" json:"last_device_code"`  // 临时带出
}

// 封号信息
type BanAccountInfo struct {
	PlayerId     uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id"`                   // 玩家id
	BanReason    int32  `protobuf:"varint,2,opt,name=BanReason,proto3,enum=common.BAN_ACC_REASON_TYPE" json:"BanReason"` // 封禁原因
	BanLoginTime int64  `protobuf:"varint,3,opt,name=BanLoginTime,proto3" json:"BanLoginTime"`                           // 用户封号的结束时间
}

// Ret 结构体表示响应的返回信息
type Ret struct {
	Desc string `json:"desc"` // Desc 是返回信息的描述
}
