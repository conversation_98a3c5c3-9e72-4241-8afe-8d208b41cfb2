package models

import (
	"database/sql"
	"time"
)

type RItemOds struct {
	Ktable         string         `json:"ktable" gorm:"type:varchar(255);comment:Ktable名称"`
	ProductID      uint8          `json:"productID" gorm:"type:tinyint unsigned;comment:产品ID"`
	ChannelType    uint8          `json:"channelType" gorm:"type:tinyint unsigned;comment:渠道类型"`
	Platform       uint8          `json:"platform" gorm:"type:tinyint unsigned;comment:平台"`
	AppLanguage    uint8          `json:"appLanguage" gorm:"type:tinyint unsigned;comment:应用语言"`
	Country        string         `json:"country" gorm:"type:varchar(255);comment:国家"`
	AccType        uint8          `json:"accType" gorm:"type:tinyint unsigned;comment:账户类型"`
	AppVersion     string         `json:"appVersion" gorm:"type:varchar(255);comment:应用版本"`
	NowDate        time.Time      `json:"nowDate" gorm:"type:date;comment:当前日期"`
	TimeStampValue time.Time      `json:"timeStampValue" gorm:"type:datetime;comment:时间戳值"`
	PlayerId       uint64         `json:"playerId" gorm:"type:bigint unsigned;comment:玩家ID"`
	ClaimId        string         `json:"claimId" gorm:"type:varchar(255);comment:声明ID"`
	InstanceId     sql.NullString `json:"instanceId" gorm:"type:varchar(255);comment:实例ID;default:NULL"`
	ItemId         int32          `json:"itemId" gorm:"type:int;comment:物品ID"`
	ItemType       uint8          `json:"itemType" gorm:"type:tinyint unsigned;comment:物品类型"`
	OptType        uint8          `json:"optType" gorm:"type:tinyint unsigned;comment:操作类型"`
	SrcType        uint8          `json:"srcType" gorm:"type:tinyint unsigned;comment:来源类型"`
	CurCount       int64          `json:"curCount" gorm:"type:bigint;comment:当前数量"`
	DeltaCount     int64          `json:"deltaCount" gorm:"type:bigint;comment:变化数量"`
	OriginalData   string         `json:"originalData" gorm:"type:text;comment:原始数据"`
}

func (RItemOds) TableName() string {
	return "r_item_ods"
}

func (e *RItemOds) GetId() interface{} {
	return e.PlayerId
}
