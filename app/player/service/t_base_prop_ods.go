package service

import (
	"errors"
	"go-admin/common/database"
	"go-admin/common/tool"
	"go-admin/core/sdk"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/player/models"
	"go-admin/app/player/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TBasePropOds struct {
	service.Service
}

// GetPage 获取TBasePropOds列表
func (e *TBasePropOds) GetPage(c *dto.TBasePropOdsGetPageReq, p *actions.DataPermission, list *[]models.RItemOds, count *int64) error {
	var err error
	var data models.RItemOds

	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)

	err = gameDb.Model(&data).Order("TimeStampValue desc").
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("RItemOds GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TBasePropOds对象
func (e *TBasePropOds) Get(d *dto.TBasePropOdsGetReq, p *actions.DataPermission, model *models.RItemOds) error {
	var data models.RItemOds
	ckKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameClickHouse)
	gameDb := sdk.Runtime.GetDbByAssignKey(ckKey)
	err := gameDb.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTBasePropOds error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}
