package dto

import (
	"time"

	"go-admin/common/dto"
)

type TBasePropOdsGetPageReq struct {
	dto.Pagination `search:"-"`
	PlayerId       string `form:"playerId"  search:"type:exact;column:PlayerId;table:r_item_ods" comment:"玩家ID"`
	TBasePropOdsOrder
}

type TBasePropOdsOrder struct {
	TimeStampValue time.Time `form:"timeStampValueOrder"  search:"type:order;column:TimeStampValue;table:r_item_ods"`
}

func (m *TBasePropOdsGetPageReq) GetNeedSearch() interface{} {
	return *m
}

// TBasePropOdsGetReq 功能获取请求参数
type TBasePropOdsGetReq struct {
	PlayerId uint64 `json:"playerId" gorm:"type:bigint unsigned;comment:玩家ID"`
}

func (s *TBasePropOdsGetReq) GetId() interface{} {
	return s.PlayerId
}
