package service

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/app/player/models"
	"go-admin/app/player/service/dto"
	"go-admin/common/rpc"
	"go-admin/core/sdk/service"
)

type TPlayer struct {
	service.Service
}

// GetPage 获取TPlayer列表
func (e *TPlayer) GetPage(c *gin.Context, req *dto.TPlayerGetPageReq) (res models.InnerData, err error) {
	res = models.InnerData{
		Ret:        models.Ret{},
		PlayerInfo: make([]models.PlayerInfo, 0),
	}

	rpcReq := &dto.BatchPlayerInfoReq{
		Pagination: req.Pagination,
		PlayerId:   req.PLayerID,
		ProductId:  int32(c.GetInt("productID")),
	}

	//todo 后面用pb常量代替 1701
	rsp, err := rpc.RpcGm(c, 1701, rpcReq)
	if err != nil {
		e.Log.Error(500, err, fmt.Sprintf("TPlayer 获取失败，\r\n失败信息 %s", err.Error()))
		return
	}

	var response models.Response
	err = json.Unmarshal([]byte(rsp), &response)
	if err != nil {
		e.Log.Error(500, err, fmt.Sprintf("TPlayer 解析失败，\r\n失败信息 %s", err.Error()))
		return
	}

	if response.Code != 200 {
		e.Log.Error(500, err, fmt.Sprintf("TPlayer 内部code错误，\r\n失败信息 %+v", response))
		return
	}

	var innerData models.InnerData
	err = json.Unmarshal([]byte(response.Data.Data), &innerData)
	if err != nil {
		e.Log.Error(500, err, fmt.Sprintf("TPlayer 无法解析内部 JSON 数据，\r\n失败信息 %+v", response))
		return
	}

	e.Log.Info(200, nil, fmt.Sprintf("TPlayer 获取成功，\r\n返回数据 %+v", innerData))
	res = innerData

	return
}
