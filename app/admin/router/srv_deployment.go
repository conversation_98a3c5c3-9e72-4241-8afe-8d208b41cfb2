package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/admin/apis"
	jwt "go-admin/core/sdk/pkg/jwtauth"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerSrvDeploymentRouter)
}

// registerSrvDeploymentRouter
func registerSrvDeploymentRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.SrvDeployment{}
	r := v1.Group("/srv-deployment")
	{
		r.GET("/nodeList", api.NodeList)
		r.PUT("/nodeStop", api.NodeStop)
		r.PUT("/nodeSetColor", api.NodeSetColor)
	}
}
