package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/admin/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTProjectRouter)
}

// registerTProjectRouter
func registerTProjectRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.Project{}
	r := v1.Group("/project").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.GET("/getAllCompleteInfo", actions.PermissionAction(), api.GetAllCompleteInfo) // 获取top frame下拉列表菜单
		r.GET("/myProjectEnvPlanet", actions.PermissionAction(), api.MyProjectEnvPlanet)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
	}
}
