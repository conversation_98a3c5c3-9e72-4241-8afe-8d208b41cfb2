package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/admin/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerPlanetRoleRouter)
}

// registerPlanetRouter
func registerPlanetRoleRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.PlanetRole{}
	r := v1.Group("/planetRole").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("/:role_id", actions.PermissionAction(), api.Get)
		r.PUT("/update", actions.PermissionAction(), api.Update)
	}
}
