package apis

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"
	"gorm.io/gorm"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
)

type PlanetRole struct {
	api.Api
}

// Get 获取环境管理
// @Summary 获取环境管理
// @Description 获取环境管理
// @Tags 环境管理
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TEnv} "{"code": 200, "data": [...]}"
// @Router /api/v1/env/{id} [get]
// @Security Bearer
func (e PlanetRole) Get(c *gin.Context) {
	req := dto.GetPlanetRoleReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var list []models.TPlanetRole
	e.Orm.Where("role_id = ?", req.RoleID).Find(&list)
	e.OK(list, "查询成功")
}

// Update 修改环境管理
// @Summary 修改环境管理
// @Description 修改环境管理
// @Tags 环境管理
// @Accept application/json
// @Product application/json
// @Param data body dto.TEnvUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/env/{id} [put]
// @Security Bearer
func (e PlanetRole) Update(c *gin.Context) {
	req := dto.UpdatePlanetRoleReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	e.Logger.Infof("planetIDs %v", req.PlanetIDs)

	newList := make([]models.TPlanetRole, 0, len(req.PlanetIDs))
	for i, planetID := range req.PlanetIDs {
		item := models.TPlanetRole{
			RoleID:   req.RoleID,
			PlanetID: planetID,
			Env:      req.EnvType[i],
		}
		newList = append(newList, item)
	}

	//事务 ，先删除，在插入
	if err := e.Orm.Transaction(func(tx *gorm.DB) error {
		tx.Where("role_id = ?", req.RoleID).Delete(&models.TPlanetRole{})
		if tx.Error != nil {
			return tx.Error
		}

		//if len(newList) > 0 {
		//	tx.Create(&newList)
		//}
		//return tx.Error

		sql := "INSERT INTO `t_planet_role` (`role_id`,`planet_id`,`env`) VALUES "
		// 循环data数组,组合sql语句
		for key, value := range newList {
			if len(newList)-1 == key {
				//最后一条数据 以分号结尾
				sql += fmt.Sprintf("(%d, %d, %d);", value.RoleID, value.PlanetID, value.Env)
			} else {
				sql += fmt.Sprintf("(%d, %d, %d),", value.RoleID, value.PlanetID, value.Env)
			}
		}
		tx.Exec(sql)
		return tx.Error
	}); err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	e.OK(nil, "修改成功")
}
