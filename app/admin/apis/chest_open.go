package apis

import (
	"encoding/json"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/tidwall/gjson"
	"go-admin/common/rpc"
	"go-admin/core/sdk/pkg/jwtauth/user"
	"go-admin/core/sdk/pkg/planet"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
)

type ChestOpen struct {
	api.Api
}

func (e ChestOpen) GetPage(c *gin.Context) {
	req := dto.ChestOpenGetPageReq{}
	s := service.ChestOpen{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.ChestOpen, 0)
	var count int64

	planetID := planet.GetPlanetID(c)
	err = s.GetPage(&req, planetID, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取记录列表 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

func (e ChestOpen) ChestOpen(c *gin.Context) {
	req := dto.ChestOpenReq{}
	s := service.ChestOpen{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	js, err := json.Marshal(req)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	_, rsp, err := rpc.RpcGm2GameJson("4000077", "", "platform", string(js), c)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := gjson.Parse(rsp)
	list := make([]*GMOpenBoxTestItem, 0)
	tmp := p.Get("Data.List").String()
	if err := json.Unmarshal([]byte(tmp), &list); err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	planetInfo, ok := planet.GetPlanetInfo(c)
	if !ok {
		err = fmt.Errorf("get planet info err")
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	m := new(models.ChestOpen)
	m.CreateBy = user.GetUserId(c)
	m.CreateUser = user.GetUserNick(c)
	m.CreatedAt = time.Now()
	m.PlanetID = planetInfo.ID
	m.Remark = req.Remark
	m.Param = string(js)

	if err := e.Orm.Create(m).Error; err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	//生成excel
	excel := excelize.NewFile()
	sheet := excel.NewSheet("card")
	excel.SetActiveSheet(sheet)
	excel.SetColWidth("card", "A", "I", 16)
	excel.SetColWidth("card", "F", "F", 25)
	excel.SetColWidth("card", "I", "I", 25)

	excel.SetCellValue("card", "A1", "开卡顺序")
	excel.SetCellValue("card", "B1", "普通新卡数量")
	excel.SetCellValue("card", "C1", "普通新卡ID及对应完成度")
	excel.SetCellValue("card", "D1", "普通万能卡数量")
	excel.SetCellValue("card", "E1", "付费新卡数量")
	excel.SetCellValue("card", "F1", "付费新卡ID及对应进度值")
	excel.SetCellValue("card", "G1", "付费万能卡数量")
	excel.SetCellValue("card", "H1", "付费万能卡进度")
	excel.SetCellValue("card", "I1", "集齐卡组数量")
	excel.SetCellValue("card", "J1", "机器卡组ID及体力奖励")

	for i, item := range list {
		row := i + 2
		excel.SetCellValue("card", "A"+strconv.Itoa(row), item.No)
		excel.SetCellValue("card", "B"+strconv.Itoa(row), item.NormalNewCardNum)
		excel.SetCellValue("card", "C"+strconv.Itoa(row), item.NormalCardIDNum)
		excel.SetCellValue("card", "D"+strconv.Itoa(row), item.NormalWildCardNum)
		excel.SetCellValue("card", "E"+strconv.Itoa(row), item.PayNewCardNum)
		excel.SetCellValue("card", "F"+strconv.Itoa(row), item.PayNewCardIDNum)
		excel.SetCellValue("card", "G"+strconv.Itoa(row), item.PayWildCardNum)
		excel.SetCellValue("card", "H"+strconv.Itoa(row), item.PayWildCardProgress)
		excel.SetCellValue("card", "I"+strconv.Itoa(row), item.CompleteNum)
		excel.SetCellValue("card", "J"+strconv.Itoa(row), item.CompleteID)
	}
	buf, err := excel.WriteToBuffer()
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	butKey := "sparklegame"
	region := "us-east-2"
	accessKey := "********************"
	secretKey := "1nnXZTXICu5F0yObCZDOhL8Snktv7QjsBrlnt8mf"

	s3, err := session.NewSession(&aws.Config{
		Credentials: credentials.NewStaticCredentials(accessKey, secretKey, ""),
		Region:      aws.String(region),
	})
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	s3Path := fmt.Sprintf("platform/operation/open-chest/%s-%s-%s-%d.xlsx", planetInfo.ProjectName, planetInfo.EnvName, planetInfo.Name, m.Id)
	uploader := s3manager.NewUploader(s3)
	upInfo, err := uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(butKey),
		Key:    aws.String(s3Path),
		Body:   buf,
		ACL:    aws.String("public-read"),
	})
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	if err := e.Orm.Model(m).Update("URL", upInfo.Location).Error; err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
	}

	e.OK(upInfo, "成功")
}

type GMOpenBoxTestItem struct {
	No                  int32  `protobuf:"varint,1,opt,name=No,proto3" json:"No"`
	NormalNewCardNum    int32  `protobuf:"varint,2,opt,name=NormalNewCardNum,proto3" json:"NormalNewCardNum"`
	NormalWildCardNum   int32  `protobuf:"varint,3,opt,name=NormalWildCardNum,proto3" json:"NormalWildCardNum"`
	NormalCardIDNum     string `protobuf:"bytes,4,opt,name=NormalCardIDNum,proto3" json:"NormalCardIDNum"`
	PayNewCardNum       int32  `protobuf:"varint,5,opt,name=PayNewCardNum,proto3" json:"PayNewCardNum"`
	PayNewCardIDNum     string `protobuf:"bytes,6,opt,name=PayNewCardIDNum,proto3" json:"PayNewCardIDNum"`
	PayWildCardNum      int32  `protobuf:"varint,7,opt,name=PayWildCardNum,proto3" json:"PayWildCardNum"`
	CompleteNum         int32  `protobuf:"varint,8,opt,name=CompleteNum,proto3" json:"CompleteNum"`
	CompleteID          string `protobuf:"bytes,9,opt,name=CompleteID,proto3" json:"CompleteID"`
	CompleteReward      string `protobuf:"bytes,10,opt,name=CompleteReward,proto3" json:"CompleteReward"`
	OpenSpinNum         int32  `protobuf:"varint,11,opt,name=OpenSpinNum,proto3" json:"OpenSpinNum"`
	PayWildCardProgress string `protobuf:"bytes,12,opt,name=PayWildCardProgress,proto3" json:"PayWildCardProgress"`
}
