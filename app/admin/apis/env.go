package apis

import (
	"fmt"

	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"github.com/gin-gonic/gin"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
)

type TEnv struct {
	api.Api
}

// GetPage 获取环境管理列表
// @Summary 获取环境管理列表
// @Description 获取环境管理列表
// @Tags 环境管理
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TEnv}} "{"code": 200, "data": [...]}"
// @Router /api/v1/env [get]
// @Security Bearer
func (e TEnv) GetPage(c *gin.Context) {
	req := dto.TEnvGetPageReq{}
	s := service.TEnv{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TEnv, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取环境管理 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取环境管理
// @Summary 获取环境管理
// @Description 获取环境管理
// @Tags 环境管理
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TEnv} "{"code": 200, "data": [...]}"
// @Router /api/v1/env/{id} [get]
// @Security Bearer
func (e TEnv) Get(c *gin.Context) {
	req := dto.TEnvGetReq{}
	s := service.TEnv{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TEnv

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取环境管理失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建环境管理
// @Summary 创建环境管理
// @Description 创建环境管理
// @Tags 环境管理
// @Accept application/json
// @Product application/json
// @Param data body dto.TEnvInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/env [post]
// @Security Bearer
func (e TEnv) Insert(c *gin.Context) {
	req := dto.TEnvInsertReq{}
	s := service.TEnv{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建环境管理  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改环境管理
// @Summary 修改环境管理
// @Description 修改环境管理
// @Tags 环境管理
// @Accept application/json
// @Product application/json
// @Param data body dto.TEnvUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/env/{id} [put]
// @Security Bearer
func (e TEnv) Update(c *gin.Context) {
	req := dto.TEnvUpdateReq{}
	s := service.TEnv{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改环境管理 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除环境管理
// @Summary 修改环境管理
// @Description 修改环境管理
// @Tags 环境管理
// @Accept application/json
// @Product application/json
// @Param data body dto.TEnvUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/env/{id} [put]
// @Security Bearer
func (e TEnv) Delete(c *gin.Context) {
	s := service.TEnv{}
	req := dto.TEnvDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除平台管理失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
