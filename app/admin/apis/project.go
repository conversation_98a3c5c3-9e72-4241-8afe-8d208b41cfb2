package apis

import (
	"fmt"
	"go-admin/core/sdk/pkg/jwtauth/user"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
)

type Project struct {
	api.Api
}

// GetPage 获取公共部分-项目表列表
// @Summary 获取公共部分-项目表列表
// @Description 获取公共部分-项目表列表
// @Tags 公共部分-项目表
// @Param name query string false "应用名称"
// @Param status query string false "是否有效，1：是，0：否"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TProject}} "{"code": 200, "data": [...]}"
// @Router /api/v1/project [get]
// @Security Bearer
func (e Project) GetPage(c *gin.Context) {
	req := dto.ProjectGetPageReq{}
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TProject, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取公共部分-项目表 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取公共部分-项目表
// @Summary 获取公共部分-项目表
// @Description 获取公共部分-项目表
// @Tags 公共部分-项目表
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TProject} "{"code": 200, "data": [...]}"
// @Router /api/v1/project/{id} [get]
// @Security Bearer
func (e Project) Get(c *gin.Context) {
	req := dto.ProjectGetReq{}
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TProject

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取公共部分-项目表失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

func (e Project) GetAllCompleteInfo(c *gin.Context) {
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	list, err := s.GetAllProjectEnvPlatformList(0)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取公共部分-项目表失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(list, "查询成功")
}

func (e Project) MyProjectEnvPlanet(c *gin.Context) {
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	roleID := user.GetRoleId(c)
	sysRole := models.SysRole{RoleId: roleID}
	if err := s.Orm.Model(sysRole).First(&sysRole).Error; err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var list []*service.ProjectMin
	if sysRole.Admin == true {
		list, err = s.GetAllProjectEnvPlatformList(0)
	} else {
		list, err = s.GetAllProjectEnvPlatformList(sysRole.RoleId)
	}
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取公共部分-项目表失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(list, "查询成功")
}

func (e Project) Insert(c *gin.Context) {
	req := dto.ProjectInsertReq{}
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.Id, "创建成功")
}

func (e Project) Update(c *gin.Context) {
	req := dto.ProjectUpdateReq{}
	s := service.Project{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改环境管理 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}
