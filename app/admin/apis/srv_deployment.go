package apis

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	log "go-admin/core/logger"
	"go-admin/core/sdk/api"
)

type SrvDeployment struct {
	api.Api
}

// NodeList 获取srv节点信息
// @Summary 获取srv节点信息
// @Description 获取srv节点信息
// @Tags 服务发布
// @Router /api/v1/nodeList [get]
// @Security Bearer
func (e SrvDeployment) NodeList(c *gin.Context) {
	req := dto.SrvNodeListReq{}
	s := service.SrvDeployment{}
	err := e.MakeContext(c).
		MakeService(&s.Service).
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	//consulAddress := "http://**************:8500"

	data, err := s.GetNodeList(req.ConsulAddress)
	if err != nil {
		e.Error(500, err, "获取节点信息失败")
		return
	}
	e.OK(data, "查询成功")
}

func (e SrvDeployment) NodeStop(c *gin.Context) {
	req := dto.SrvDepNodeReq{}
	s := service.SrvDeployment{}
	err := e.MakeContext(c).
		MakeService(&s.Service).
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	data, err := s.NodeStop(&req)
	if err != nil {
		e.Error(500, err, "关闭失败")
		return
	}
	e.OK(data, "节点停止成功")
}

func (e SrvDeployment) NodeSetColor(c *gin.Context) {
	req := dto.SrvDepNodeSetColor{}
	s := service.SrvDeployment{}
	err := e.MakeContext(c).
		MakeService(&s.Service).
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	data, err := s.NodeSetColor(&req)
	if err != nil {
		log.Errorf("node set color err:%s", err)
		e.Error(500, err, "修改节点颜色失败")
		return
	}
	e.OK(data, "节点修改成功")
}
