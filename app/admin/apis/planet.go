package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
)

type Planet struct {
	api.Api
}

// GetPage 获取平台管理列表
// @Summary 获取平台管理列表
// @Description 获取平台管理列表
// @Tags 平台管理
// @Param name query string false "平台名称"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.Planet}} "{"code": 200, "data": [...]}"
// @Router /api/v1/planet [get]
// @Security Bearer
func (e Planet) GetPage(c *gin.Context) {
	req := dto.PlanetGetPageReq{}
	s := service.Planet{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.Planet, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取平台管理 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取平台管理
// @Summary 获取平台管理
// @Description 获取平台管理
// @Tags 平台管理
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.Planet} "{"code": 200, "data": [...]}"
// @Router /api/v1/planet/{id} [get]
// @Security Bearer
func (e Planet) Get(c *gin.Context) {
	req := dto.PlanetGetReq{}
	s := service.Planet{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.Planet
	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取平台管理失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

func (e Planet) Insert(c *gin.Context) {
	req := dto.PlanetInsertReq{}
	s := service.Planet{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建Planet管理  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改平台管理
// @Summary 修改平台管理
// @Description 修改平台管理
// @Tags 平台管理
// @Accept application/json
// @Product application/json
// @Param data body dto.PlanetUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/planet/{id} [put]
// @Security Bearer
func (e Planet) Update(c *gin.Context) {
	req := dto.PlanetUpdateReq{}
	s := service.Planet{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.UpdateGMZk(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改平台管理 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除平台管理
// @Summary 删除平台管理
// @Description 删除平台管理
// @Tags 平台管理
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/planet [delete]
// @Security Bearer
func (e Planet) Delete(c *gin.Context) {
	s := service.Planet{}
	req := dto.PlanetDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除平台管理失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
