package service

import (
	"errors"
	cDto "go-admin/common/dto"
	"go-admin/core/sdk/service"

	"gorm.io/gorm"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
)

type Planet struct {
	service.Service
}

// GetPage 获取Planet列表
func (e *Planet) GetPage(c *dto.PlanetGetPageReq, p *actions.DataPermission, list *[]models.Planet, count *int64) error {
	var err error
	var data models.Planet

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
		).
		Find(list).Error
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}

	return nil
}

// Get 获取Planet对象
func (e *Planet) Get(d *dto.PlanetGetReq, p *actions.DataPermission, model *models.Planet) error {
	var data models.Planet
	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetPlanet error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建Planet对象
func (e *Planet) Insert(c *dto.PlanetInsertReq) error {
	var err error
	var data models.Planet
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("PlanetService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// UpdateGMZk 修改Planet对象
func (e *Planet) UpdateGMZk(c *dto.PlanetUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.Planet{}
	db := e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())

	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}

	c.Generate(&data)
	u := map[string]interface{}{
		"product_id":    c.ProductId,
		"planet_id":     c.PlanetId,
		"name":          c.Name,
		"alias_name":    c.AliasName,
		"relation_name": c.RelationName,
		"describe":      c.Describe,
		"is_inland":     c.IsInland,
		"status":        c.Status,
		"app_id":        c.AppId,
		"app_security":  c.AppSecurity,
	}

	updateDB := e.Orm.Model(&data).UpdateColumns(u)
	if updateDB.Error != nil {
		e.Log.Errorf("PlanetService Save error:%s \r\n", updateDB.Error)
		return err
	}

	return nil
}

// Remove 删除Planet
func (e *Planet) Remove(d *dto.PlanetDeleteReq, p *actions.DataPermission) error {
	var data models.Planet

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemovePlanet error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
