package service

import (
	"encoding/json"
	"fmt"
	"go-admin/app/admin/service/dto"
	"go-admin/common/database"
	"go-admin/common/tool"
	log "go-admin/core/logger"
	"go-admin/core/sdk"
	"go-admin/core/sdk/pkg"
	"go-admin/core/sdk/service"
	"go-admin/core/tools/consul"

	"golang.org/x/xerrors"
)

const ServerZkPath = "/etc/servers/play"
const UrlNodeStop = "/oam/config/set/:stop_server"
const UrlNodeSetColor = "/oam/config/set/:set_color"

type SrvDeployment struct {
	service.Service
}

type NodeInfo struct {
	TCPAddr    string `json:"tcp_addr"`
	HTTPAddr   string `json:"http_addr"`
	SrvName    string `json:"srv_name,omitempty"`
	Status     int    `json:"status"`
	Sid        int    `json:"sid,omitempty"`
	Version    string `json:"version,omitempty"`
	ListenMode int    `json:"listen_mode,omitempty"`
	Color      int    `json:"node_color"` // 节点颜色
}

func (e *SrvDeployment) GetNodeList(consulStr string) (map[string][]NodeInfo, error) {
	srvMap := make(map[string][]NodeInfo, 100) // key srvName->[]node

	agentServices, err := consul.GetServicesList(consulStr)
	if err != nil {
		return nil, err
	}

	for _, service := range agentServices {

		isNative := 0 // TODO 服务状态

		srvName := service.Service
		nodeInfo := NodeInfo{
			TCPAddr:    service.Address,
			HTTPAddr:   service.Meta["http_addr"],
			SrvName:    service.Service,
			Status:     isNative,
			Sid:        0,
			Version:    "",
			ListenMode: 0,
			Color:      0,
		}
		srvMap[srvName] = append(srvMap[srvName], nodeInfo)
	}

	log.Infof("srvMap :%v \n", srvMap)
	return srvMap, nil
}

func (e *SrvDeployment) getNodeKey(srvName string, tcpAddr string) string {
	return fmt.Sprintf("%s/%s/%s", ServerZkPath, srvName, tcpAddr)
}

func (e *SrvDeployment) getNodeInfoByNameAddr(srvName string, tcpAddr string) (*NodeInfo, error) {
	key := e.getNodeKey(srvName, tcpAddr)
	zkKey := tool.GetPlanetZkKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameZk)
	info, err := sdk.Runtime.GetZookeeper(zkKey).Get(key)
	if err != nil {
		return nil, err
	}

	if len(info) <= 0 {
		return nil, xerrors.New("node info empty")
	}

	node := &NodeInfo{}
	err = json.Unmarshal([]byte(info), node)
	if err != nil {
		return nil, err
	}
	return node, nil
}

func (e *SrvDeployment) NodeStop(req *dto.SrvDepNodeReq) (string, error) {
	// 确认节点是否存在
	rsp := ""
	nodeInfo, err := e.getNodeInfoByNameAddr(req.SrvName, req.TcpAddr)
	if err != nil {
		return rsp, err
	}

	url := fmt.Sprintf("http://%s%s?time=2", nodeInfo.HTTPAddr, UrlNodeStop)
	rsp, err = pkg.Get(url)
	if err != nil {
		return rsp, err
	}

	return rsp, nil
}

func (e *SrvDeployment) NodeSetColor(req *dto.SrvDepNodeSetColor) (string, error) {
	// 确认节点是否存在
	rsp := ""
	nodeInfo, err := e.getNodeInfoByNameAddr(req.SrvName, req.TcpAddr)
	if err != nil {
		return rsp, err
	}

	url := fmt.Sprintf("http://%s%s?color=%d", nodeInfo.HTTPAddr, UrlNodeSetColor, req.Color)
	rsp, err = pkg.Get(url)
	if err != nil {
		return rsp, err
	}

	return rsp, nil
}
