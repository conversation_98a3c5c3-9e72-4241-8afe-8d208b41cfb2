package dto

import (
	"go-admin/app/admin/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type ProjectGetPageReq struct {
	dto.Pagination `search:"-"`
	Name           string `form:"name"  search:"type:contains;column:name;table:t_project" comment:"应用名称"`
	Status         string `form:"status"  search:"type:exact;column:status;table:t_project" comment:"是否有效，1：是，0：否"`
	ProjectOrder
}

type ProjectOrder struct {
	Id        int    `form:"idOrder"  search:"type:order;column:id;table:t_project"`
	ProductId string `form:"productIdOrder"  search:"type:order;column:appid;table:t_project"`
	Name      string `form:"nameOrder"  search:"type:order;column:name;table:t_project"`
	Describe  string `form:"describeOrder"  search:"type:order;column:describe;table:t_project"`
	Leader    string `form:"leaderOrder"  search:"type:order;column:leader;table:t_project"`
	Subsystem string `form:"subsystemOrder"  search:"type:order;column:subsystem;table:t_project"`
	Status    string `form:"statusOrder"  search:"type:order;column:status;table:t_project"`
}

func (m *ProjectGetPageReq) GetNeedSearch() interface{} {
	return *m
}

// ProjectGetReq 功能获取请求参数
type ProjectGetReq struct {
	Id int `uri:"id"`
}

func (s *ProjectGetReq) GetId() interface{} {
	return s.Id
}

type ProjectInsertReq struct {
	Id        int    `json:"-" comment:""` //
	ProductId string `json:"product_id" comment:"product_id"`
	Name      string `json:"name" comment:"名称"`
	Describe  string `json:"describe" comment:"描述"`
	Leader    string `json:"leader" comment:"leader"`
	Status    int    `json:"status" comment:"status"`
}

func (s *ProjectInsertReq) Generate(model *models.TProject) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.ProductId = s.ProductId
	model.Name = s.Name
	model.Describe = s.Describe
	model.Leader = s.Leader
	model.Status = s.Status
}

type ProjectUpdateReq struct {
	Id        int    `uri:"id" comment:""` //
	ProductId string `json:"product_id" comment:"product_id"`
	Name      string `json:"name" comment:"名称"`
	Describe  string `json:"describe" comment:"描述"`
	Leader    string `json:"leader" comment:"leader"`
	Status    int    `json:"status" comment:"status"`
	common.ControlBy
}

func (s *ProjectUpdateReq) Generate(model *models.TProject) {
	model.Id = s.Id
	model.ProductId = s.ProductId
	model.Name = s.Name
	model.Describe = s.Describe
	model.Leader = s.Leader
	model.Status = s.Status
}

func (s *ProjectUpdateReq) GetId() interface{} {
	return s.Id
}
