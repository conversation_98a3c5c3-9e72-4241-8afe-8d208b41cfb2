package dto

import (
	"time"

	"go-admin/app/admin/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TEnvGetPageReq struct {
	dto.Pagination `search:"-"`
	ProductId      int `form:"productId"  search:"type:exact;column:product_id;table:t_env" comment:"项目名称"`
	PlanetId       int `form:"planetId"  search:"type:exact;column:planet_id;table:t_env" comment:"Planet"`
	TEnvOrder
}

type TEnvOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:t_env"`
	ProductId int       `form:"productIdOrder"  search:"type:order;column:product_id;table:t_env"`
	PlanetId  int       `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_env"`
	Name      string    `form:"nameOrder"  search:"type:order;column:name;table:t_env"`
	ZhName    string    `form:"zhNameOrder"  search:"type:order;column:zh_name;table:t_env"`
	Pid       string    `form:"pidOrder"  search:"type:order;column:pid;table:t_env"`
	CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_env"`
	UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_env"`
	CreatedBy int       `form:"createdByOrder"  search:"type:order;column:created_by;table:t_env"`
	UpdatedBy int       `form:"updatedByOrder"  search:"type:order;column:updated_by;table:t_env"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_env"`
}

func (m *TEnvGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TEnvInsertReq struct {
	Id            int    `json:"-" comment:""` //
	ProductId     int    `json:"productId" comment:"product_id"`
	PlanetId      int    `json:"planetId" comment:"planetId"`
	EnvType       int    `json:"envType" comment:"环境id"`
	Name          string `json:"name" comment:"环境名称"`
	ZhName        string `json:"zhName" comment:"环境中文名称"`
	Timezone      string `json:"timezone" comment:"timezone"`
	Cloud         string `json:"cloud" comment:"cloud"`
	Host          string `json:"host" comment:"host"`
	Ip            string `json:"ip" comment:"ip"`
	Port          int    `json:"port" comment:"port"`
	GmAddr        string `json:"gmAddr" comment:"gmAddr"`
	CkAddr        string `json:"ckAddr" comment:"ckAddr"`
	ConfigOssAddr string `json:"configOssAddr" comment:"配置存储OSS"`
	ConsulAddr    string `json:"consulAddr" comment:"consul或etcd地址"`
	ExpandParam   string `json:"expandParam" comment:"扩展参数"`
	SqlAddr       string `json:"gameSqlAddr" comment:"sqlAddr"`
	common.ControlBy
}

func (s *TEnvInsertReq) Generate(model *models.TEnv) {
	model.Id = s.Id
	model.ProductId = s.ProductId
	model.PlanetId = s.PlanetId
	model.EnvType = s.EnvType
	model.Name = s.Name
	model.ZhName = s.ZhName
	model.Timezone = s.Timezone
	model.Cloud = s.Cloud
	model.Host = s.Host
	model.Ip = s.Ip
	model.Port = s.Port
	model.GmAddr = s.GmAddr
	model.CkAddr = s.CkAddr
	model.ConfigOssAddr = s.ConfigOssAddr
	model.ConsulAddr = s.ConsulAddr
	model.GameSqlAddr = s.SqlAddr
	model.ExpandParam = s.ExpandParam
	model.CreateBy = s.CreateBy
	model.UpdateBy = s.UpdateBy
}

func (s *TEnvInsertReq) GetId() interface{} {
	return s.Id
}

type TEnvUpdateReq struct {
	Id            int    `uri:"id" comment:""` //
	ProductId     int    `json:"productId" comment:"product_id"`
	PlanetId      int    `json:"planetId" comment:"planetId"`
	EnvType       int    `json:"envType" comment:"环境id"`
	Name          string `json:"name" comment:"环境名称"`
	ZhName        string `json:"zhName" comment:"环境中文名称"`
	Timezone      string `json:"timezone" comment:"timezone"`
	Cloud         string `json:"cloud" comment:"cloud"`
	Host          string `json:"host" comment:"host"`
	Ip            string `json:"ip" comment:"ip"`
	Port          int    `json:"port" comment:"port"`
	GmAddr        string `json:"gmAddr" comment:"gmAddr"`
	CkAddr        string `json:"ckAddr" comment:"ckAddr"`
	SqlAddr       string `json:"gameSqlAddr" comment:"sqlAddr"`
	ConfigOssAddr string `json:"configOssAddr" comment:"配置存储OSS"`
	ConsulAddr    string `json:"consulAddr" comment:"consul或etcd地址"`
	ExpandParam   string `json:"expandParam" comment:"扩展参数"`
	common.ControlBy
}

func (s *TEnvUpdateReq) Generate(model *models.TEnv) {
	model.Id = s.Id
	model.ProductId = s.ProductId
	model.PlanetId = s.PlanetId
	model.EnvType = s.EnvType
	model.Name = s.Name
	model.ZhName = s.ZhName
	model.Timezone = s.Timezone
	model.Cloud = s.Cloud
	model.Host = s.Host
	model.Ip = s.Ip
	model.Port = s.Port
	model.GmAddr = s.GmAddr
	model.CkAddr = s.CkAddr
	model.ConfigOssAddr = s.ConfigOssAddr
	model.ConsulAddr = s.ConsulAddr
	model.GameSqlAddr = s.SqlAddr
	model.ExpandParam = s.ExpandParam
	model.CreateBy = s.CreateBy
	model.UpdateBy = s.UpdateBy
}

func (s *TEnvUpdateReq) GetId() interface{} {
	return s.Id
}

// TEnvGetReq 功能获取请求参数
type TEnvGetReq struct {
	Id int `uri:"id"`
}

func (s *TEnvGetReq) GetId() interface{} {
	return s.Id
}

// TEnvDeleteReq 功能删除请求参数
type TEnvDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TEnvDeleteReq) GetId() interface{} {
	return s.Ids
}
