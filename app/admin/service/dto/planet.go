package dto

import (
	"time"

	"go-admin/app/admin/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type PlanetGetPageReq struct {
	dto.Pagination `search:"-"`
	ProductId      string `form:"productId"  search:"type:exact;column:product_id;table:t_planet" comment:"项目名称"`
	Name           string `form:"name"  search:"type:contains;column:name;table:t_planet" comment:"平台名称"`
	PlanetOrder
}

type PlanetOrder struct {
	Id           int       `form:"idOrder"  search:"type:order;column:id;table:t_planet"`
	ProductId    string    `form:"productIdOrder"  search:"type:order;column:product_id;table:t_planet"`
	PlanetId     string    `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_planet"`
	Name         string    `form:"nameOrder"  search:"type:order;column:name;table:t_planet"`
	AliasName    string    `form:"aliasNameOrder"  search:"type:order;column:alias_name;table:t_planet"`
	RelationName string    `form:"relationNameOrder"  search:"type:order;column:relation_name;table:t_planet"`
	Describe     string    `form:"describeOrder"  search:"type:order;column:describe;table:t_planet"`
	IsInland     string    `form:"isInlandOrder"  search:"type:order;column:is_inland;table:t_planet"`
	CreateUser   string    `form:"createUserOrder"  search:"type:order;column:create_user;table:t_planet"`
	CreatedAt    time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_planet"`
	UpdateUser   string    `form:"updateUserOrder"  search:"type:order;column:update_user;table:t_planet"`
	UpdatedAt    time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_planet"`
	Status       string    `form:"statusOrder"  search:"type:order;column:status;table:t_planet"`
	AppId        string    `form:"appId"  search:"type:order;column:app_id;table:t_planet"`
	AppSecurity  string    `form:"appSecurity"  search:"type:order;column:app_security;table:t_planet"`
}

func (m *PlanetGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type PlanetInsertReq struct {
	Id           int    `json:"-" comment:"主键ID"` // 主键ID
	ProductId    string `json:"productId" comment:"项目ID"`
	PlanetId     string `json:"planetId" comment:"planetID"`
	Name         string `json:"name" comment:"平台名称"`
	AliasName    string `json:"aliasName" comment:"平台别名"`
	RelationName string `json:"relationName" comment:""`
	Describe     string `json:"describe" comment:"平台中文名"`
	IsInland     int    `json:"isInland" comment:"是否内地(1-是,2-海外)"`
	CreateUser   string `json:"createUser" comment:"创建人"`
	UpdateUser   string `json:"updateUser" comment:"最后更新人"`
	Status       int    `json:"status" comment:"是否有效，1：是，0：否"`
	AppId        string `json:"appId" comment:"appId"`
	AppSecurity  string `json:"appSecurity" comment:"appSecurity"`
	common.ControlBy
}

func (s *PlanetInsertReq) Generate(model *models.Planet) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.ProductId = s.ProductId
	model.PlanetId = s.PlanetId
	model.Name = s.Name
	model.AliasName = s.AliasName
	model.RelationName = s.RelationName
	model.Describe = s.Describe
	model.IsInland = s.IsInland
	model.Status = s.Status
	model.AppId = s.AppId
	model.AppSecurity = s.AppSecurity
}

func (s *PlanetInsertReq) GetId() interface{} {
	return s.Id
}

type PlanetUpdateReq struct {
	Id           int    `uri:"id" comment:"主键ID"` // 主键ID
	ProductId    string `json:"productId" comment:"项目ID"`
	PlanetId     string `json:"planetId" comment:"planetID"`
	Name         string `json:"name" comment:"平台名称"`
	AliasName    string `json:"aliasName" comment:"平台别名"`
	RelationName string `json:"relationName" comment:""`
	Describe     string `json:"describe" comment:"平台中文名"`
	IsInland     int    `json:"isInland" comment:"是否内地(1-是,2-海外)"`
	CreateUser   string `json:"createUser" comment:"创建人"`
	UpdateUser   string `json:"updateUser" comment:"最后更新人"`
	Status       int    `json:"status" comment:"是否有效，1：是，0：否"`
	AppId        string `json:"appId"`
	AppSecurity  string `json:"appSecurity"`
	common.ControlBy
}

func (s *PlanetUpdateReq) Generate(model *models.Planet) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.ProductId = s.ProductId
	model.PlanetId = s.PlanetId
	model.Name = s.Name
	model.AliasName = s.AliasName
	model.RelationName = s.RelationName
	model.Describe = s.Describe
	model.IsInland = s.IsInland
	model.Status = s.Status
	model.AppId = s.AppId
	model.AppSecurity = s.AppSecurity
}

func (s *PlanetUpdateReq) GetId() interface{} {
	return s.Id
}

// PlanetGetReq 功能获取请求参数
type PlanetGetReq struct {
	Id int `uri:"id"`
}

func (s *PlanetGetReq) GetId() interface{} {
	return s.Id
}

// PlanetDeleteReq 功能删除请求参数
type PlanetDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *PlanetDeleteReq) GetId() interface{} {
	return s.Ids
}
