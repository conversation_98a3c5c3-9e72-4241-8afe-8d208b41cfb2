package service

import (
	"errors"
	log "go-admin/core/logger"
	"go-admin/core/sdk/service"
	"strconv"

	"gorm.io/gorm"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type Project struct {
	service.Service
}

// GetPage 获取TProject列表
func (e *Project) GetPage(c *dto.ProjectGetPageReq, p *actions.DataPermission, list *[]models.TProject, count *int64) error {
	var err error
	var data models.TProject

	orm := e.Orm
	err = orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TProjectService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TProject对象
func (e *Project) Get(d *dto.ProjectGetReq, p *actions.DataPermission, model *models.TProject) error {
	var data models.TProject
	orm := e.Orm
	err := orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTProject error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

func (e *Project) GetAllProjectEnvPlatformList(roleID int) ([]*ProjectMin, error) {

	listSelect := make([]models.ProjectEnvPlanet, 0)

	tx := e.Orm.Table("t_project prj, t_planet tp").
		Joins("left join t_env te on tp.planet_id = te.planet_id").
		Select("prj.id as id, " +
			"prj.product_id," +
			"prj.name as name," +
			"tp.name as planet_name," +
			"prj.`describe` as description," +
			"tp.alias_name," +
			"tp.planet_id as planet_id," +
			"tp.name as planet_name," +
			"tp.`describe` as planet_describe," +
			"te.env_type," +
			"te.name as env_name," +
			"te.zh_name  as env_zh_name").
		Where("tp.product_id = prj.product_id").
		Where("prj.status = 2 and tp.status = 2 ")

	if err := tx.Order("product_id, env_type, planet_id").Scan(&listSelect).Error; err != nil {
		return nil, err
	}

	// 根据权限筛选菜单
	list := make([]models.ProjectEnvPlanet, 0) // 权限筛选后

	listRoleEnv := make([]models.TPlanetRole, 0)
	if roleID != 0 {
		txRole := e.Orm.Table("t_planet_role tpr").
			Select("tpr.planet_id as planetId, tpr.env, tpr.role_id as roleId").
			Where("tpr.role_id = ?", roleID)
		if err := txRole.Scan(&listRoleEnv).Error; err != nil {
			return nil, err
		}

		for _, roleRow := range listSelect {
			// 如果不在角色拥有环境，则剔除
			isRoleGet := false
			for _, roleEnv := range listRoleEnv {
				if roleEnv.Env == roleRow.EnvType {
					isRoleGet = true
					break
				}
			}

			if isRoleGet {
				list = append(list, roleRow)
			}
		}
	} else {
		list = listSelect
	}

	log.Infof("project list:%v", list)

	nestedList := make([]*ProjectMin, 0)
	projectMap := make(map[int]*ProjectMin) // 所有项目
	envMap := make(map[int]map[int]*EnvMin) // 所有环境列表

	for _, item := range list {
		pID, _ := strconv.Atoi(item.ProductId)
		if _, ok := projectMap[pID]; !ok {
			projectMap[pID] = &ProjectMin{
				Id:          pID,
				ProductId:   item.ProductId,
				Name:        item.Name,
				Description: item.Describe,
				EnvList:     make([]*EnvMin, 0),
			}
			envMap[pID] = make(map[int]*EnvMin)
			nestedList = append(nestedList, projectMap[pID])
		}

		if item.EnvType == 0 {
			continue
		}

		pMin := projectMap[pID]
		if _, ok := envMap[pID][item.EnvType]; !ok {
			envMap[pID][item.EnvType] = &EnvMin{
				EnvType:    item.EnvType,
				EnvName:    item.EnvName,
				EnvZhName:  item.ZhName,
				PlanetList: make([]*PlanetMin, 0),
			}
			pMin.EnvList = append(pMin.EnvList, envMap[pID][item.EnvType])
		}

		// 直接把planet加到env中
		pltName := ""
		if item.PlanetId == 0 {
			pltName = "空"
		} else {
			pltName = item.PlanetName
		}

		envMap[pID][item.EnvType].PlanetList = append(envMap[pID][item.EnvType].PlanetList, &PlanetMin{
			ID:             item.PlanetId,
			PlanetName:     pltName,
			PlanetDescribe: item.PlanetDescribe,
		})
	}

	// project -> []env ->[]planet
	// 构造层层级结构返回

	return nestedList, nil
}

// ProjectMin 第一级菜单数据结构，项目产品
type ProjectMin struct {
	Id          int       `json:"id"`
	ProductId   string    `json:"product_id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	EnvList     []*EnvMin `json:"envList"`
}

// EnvMin 第二级菜单，环境
type EnvMin struct {
	ID         int          `json:"ID"`
	EnvType    int          `json:"env_type"`
	EnvName    string       `json:"envName"`
	EnvZhName  string       `json:"zhName"`
	PlanetList []*PlanetMin `json:"planetList"`
}

// PlanetMin 第三级菜单，Planet
type PlanetMin struct {
	ID             int    `json:"ID"`
	PlanetName     string `json:"planetName"`
	PlanetDescribe string `json:"planetDescribe"`
}

func (e *Project) Insert(d *dto.ProjectInsertReq) error {
	var err error
	var data models.TProject
	d.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TProjectService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TEnv对象
func (e *Project) Update(c *dto.ProjectUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TProject{}

	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("Project Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}
