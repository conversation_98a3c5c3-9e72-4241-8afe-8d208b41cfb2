package service

import (
	"errors"
	"go-admin/core/sdk/service"

	"gorm.io/gorm"

	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TEnv struct {
	service.Service
}

// GetPage 获取TEnv列表
func (e *TEnv) GetPage(c *dto.TEnvGetPageReq, p *actions.DataPermission, list *[]models.TEnv, count *int64) error {
	var err error
	var data models.TEnv
	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TEnvService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TEnv对象
func (e *TEnv) Get(d *dto.TEnvGetReq, p *actions.DataPermission, model *models.TEnv) error {
	var data models.TEnv
	// orm := sdk.Runtime.GetDbByAssignKey(database.ExtDBNameConfigure)
	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTEnv error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TEnv对象
func (e *TEnv) Insert(c *dto.TEnvInsertReq) error {
	var err error
	var data models.TEnv
	c.Generate(&data)
	// orm := sdk.Runtime.GetDbByAssignKey(database.ExtDBNameConfigure)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TEnvService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TEnv对象
func (e *TEnv) Update(c *dto.TEnvUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TEnv{}

	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TEnvService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除Planet
func (e *TEnv) Remove(d *dto.TEnvDeleteReq, p *actions.DataPermission) error {
	var data models.TEnv

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTEnv error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
