package service

import (
	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
	"go-admin/core/sdk/service"
)

type ChestOpen struct {
	service.Service
}

// GetPage 获取TProject列表
func (e *ChestOpen) GetPage(c *dto.ChestOpenGetPageReq, planetID int, p *actions.DataPermission, list *[]models.ChestOpen, count *int64) error {
	var err error
	var data models.ChestOpen

	err = e.Orm.Model(&data).
		Scopes(
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Where("planet_id = ?", planetID).Order("created_at desc").Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rrorf("TProjectService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}
