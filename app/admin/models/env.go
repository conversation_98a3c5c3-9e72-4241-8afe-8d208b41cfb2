package models

import (
	"go-admin/common/models"
)

type TEnv struct {
	models.Model
	ProductId     int    `json:"productId" gorm:"type:int;comment:product_id"`
	PlanetId      int    `json:"planetId" gorm:"type:int;comment:planet_id"`
	EnvType       int    `json:"envType" gorm:"type:int;comment:env_id"`
	Name          string `json:"name" gorm:"type:varchar(32);comment:环境名称"`
	ZhName        string `json:"zhName" gorm:"type:varchar(32);comment:环境中文名称"`
	Timezone      string `json:"timezone" gorm:"type:varchar(32);comment:时区"`
	Cloud         string `json:"cloud" gorm:"type:varchar(10);comment:cloud"`
	Host          string `json:"host" gorm:"type:varchar(255);comment:host"`
	Ip            string `json:"ip" gorm:"type:varchar(255);comment:ip"`
	Port          int    `json:"port" gorm:"type:int;comment:port"`
	GmAddr        string `json:"gmAddr" gorm:"type:string;comment:gm_addr"`
	CkAddr        string `json:"ckAddr" gorm:"type:string;comment:ck_addr"`
	ConfigOssAddr string `json:"configOssAddr" gorm:"type:string;comment:config_oss_addr"`
	ConsulAddr    string `json:"consulAddr" gorm:"type:string;comment:consul或etcd地址"`
	GameSqlAddr   string `json:"gameSqlAddr" gorm:"type:string;comment:game_sql_addr"`
	ExpandParam   string `json:"expandParam" gorm:"type:string;comment:expand_param"`
	models.ModelTimeWithoutDel
	models.ControlBy
}

func (TEnv) TableName() string {
	return "t_env"
}

func (e *TEnv) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TEnv) GetId() interface{} {
	return e.Id
}
