package models

import "go-admin/common/models"

type TPlanetRole struct {
	models.Model
	PlanetID int `json:"planetId" gorm:"column:planet_id"`
	RoleID   int `json:"roleId" gorm:"column:role_id"`
	Env      int `json:"env_type" gorm:"column:env"`
	models.ModelTimeWithoutDel
	models.ControlBy
}

func (TPlanetRole) TableName() string {
	return "t_planet_role"
}

func (e *TPlanetRole) GetId() interface{} {
	return e.Id
}
