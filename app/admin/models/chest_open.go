package models

import "time"

type ChestOpen struct {
	Id         int       `json:"id" gorm:"primaryKey;autoIncrement;comment:主键编码"`
	PlanetID   int       `json:"planetID" gorm:"type:int(11);comment:平台ID"`
	Status     int       `json:"status" gorm:"type:tinyint;comment:是否有效，1：是，0：否"`
	URL        string    `json:"URL" gorm:"type:varchar(32);comment:下载链接"`
	Remark     string    `json:"remark" gorm:"type:varchar(32);comment:备注"`
	CreateUser string    `json:"createUser"`
	CreateBy   int       `json:"createBy"`
	CreatedAt  time.Time `json:"createdAt"`
	Param      string    `json:"param" gorm:"type:varchar(1024);comment:操作参数"`
}

func (ChestOpen) TableName() string {
	return "t_chest_open"
}
