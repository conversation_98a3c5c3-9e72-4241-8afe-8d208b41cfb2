package models

import (
	"go-admin/common/models"
	"time"
)

type TProject struct {
	models.Model
	ProductId string    `json:"product_id" gorm:"type:varchar(32);comment:ProductId"`
	Name      string    `json:"name" gorm:"type:varchar(32);comment:应用名称"`
	Describe  string    `json:"describe" gorm:"type:varchar(32);comment:项目中文名"`
	Leader    string    `json:"leader" gorm:"type:varchar(32);comment:负责人邮箱"`
	Subsystem string    `json:"subsystem" gorm:"type:varchar(120);comment:哪些子系统在使用(用逗号分隔)"`
	Status    int       `json:"status" gorm:"type:tinyint;comment:是否有效，1：是，0：否"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
	// Env       []TEnv    `gorm:"foreignKey:pid;references:id"`
	models.ModelTimeWithoutDel
	models.ControlBy
}

func (TProject) TableName() string {
	return "t_project"
}

func (e *TProject) GetId() interface{} {
	return e.Id
}

type ProjectEnvPlanet struct {
	Id             int    `json:"ID"`                                    // 项目ID
	ProductId      string `json:"product_id" gorm:"column:product_id"`   //
	Name           string `json:"name"`                                  // 项目名称
	Describe       string `join:"description" gorm:"column:description"` // 项目中文名
	EnvType        int    `json:"env_type"`                              // 环境-Project ID
	EnvName        string `json:"env_name"`                              // 环境名称
	ZhName         string `json:"zh_name"`                               // 环境中文名
	PlanetId       int    `json:"planetID"`                              // 平台ID
	PlanetName     string `json:"planetName"`                            // 平台名称
	PlanetDescribe string `json:"planetDescribe"`                        // 平台中文名
}
