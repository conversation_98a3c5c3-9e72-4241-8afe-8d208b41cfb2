package models

import (
	"go-admin/common/models"
)

type Planet struct {
	models.Model
	ProductId    string `json:"productId" gorm:"type:int;comment:产品项目ID"`
	PlanetId     string `json:"planetId" gorm:"type:int;comment:planetID"`
	Name         string `json:"name" gorm:"type:varchar(32);comment:平台名称"`
	AliasName    string `json:"aliasName" gorm:"type:varchar(32);comment:平台别名"`
	RelationName string `json:"relationName" gorm:"type:varchar(32);comment:RelationName"`
	Describe     string `json:"describe" gorm:"type:varchar(32);comment:平台中文名"`
	IsInland     int    `json:"isInland" gorm:"type:tinyint;comment:是否内地(1-是,2-海外)"`
	Status       int    `json:"status" gorm:"type:tinyint;comment:是否有效，1：是，0：否"`
	AppId        string `json:"appId" gorm:"type:varchar(255);comment:app_id"`
	AppSecurity  string `json:"appSecurity" gorm:"type:varchar(255);comment:app_security"`
	models.ModelTimeWithoutDel
	models.ControlBy
}

func (Planet) TableName() string {
	return "t_planet"
}

func (e *Planet) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *Planet) GetId() interface{} {
	return e.Id
}

// PlanetListItem 列表数据结构
type PlanetListItem struct {
	ID          int    `json:"id" gorm:"column:id"`
	ProductId   int    `json:"product_id" gorm:"column:product_id"`
	PlanetId    int    `json:"planet_id" gorm:"column:planet_id"`
	Env         int    `json:"env" gorm:"column:env"`
	Name        string `json:"name" gorm:"column:name"`
	GmAddr      string `json:"gmAddr" gorm:"column:gm_addr"`
	ZkAddr      string `json:"zkAddr" gorm:"column:zk_addr"`
	CkAddr      string `json:"ckAddr" gorm:"column:ck_addr"`
	GameSqlAddr string `json:"gameSqlAddr" gorm:"column:game_sql_addr"`
	ProName     string `json:"proName" gorm:"column:proName"`
	ProZhName   string `json:"proZhName" gorm:"column:proZhName"`
	EnvZhName   string `json:"envZhName" gorm:"column:envZhName"`
}
