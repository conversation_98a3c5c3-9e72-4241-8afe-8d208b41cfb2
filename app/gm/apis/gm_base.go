package apis

import (
	"encoding/json"
	"fmt"
	cModels "go-admin/common/models"
	"go-admin/common/rpc"
	"go-admin/config"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service"
	"go-admin/app/gm/service/dto"
)

type GmManage struct {
	api.Api
}

// GetPage
// @Summary GM列表数据
// @Description Get JSON
// @Tags GM/Cmd
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/gm [get]
// @Security Bearer
func (e GmManage) GetPage(c *gin.Context) {
	s := service.GmManage{}
	req := dto.GmGetPageReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req, binding.Form).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	list := make([]models.GmBase, 0)
	var count int64

	err = s.GetPage(&req, &list, &count)
	if err != nil {
		e.Error(500, err, "查询失败")
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// GetPropTypes
// @Summary GM-获取物品道具类型
// @Description Get JSON
// @Tags GM/Cmd
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/gmPropTypeList [get]
// @Security Bearer
func (e GmManage) GetPropTypes(c *gin.Context) {
	s := service.PropType{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(nil, binding.Form).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	list := make([]models.PropType, 0)
	var count int64

	err = s.GetAllPropType(&list, &count)
	if err != nil {
		e.Error(500, err, "查询失败")
		return
	}

	e.OK(list, "查询成功")
}

// Get
// @Summary 获取GM数据
// @Description 获取JSON
// @Param gmId path string false "gmId"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/gm/{id} [get]
// @Security Bearer
func (e GmManage) Get(c *gin.Context) {
	s := service.GmManage{}
	req := dto.GmGetReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req, nil).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, fmt.Sprintf(" %s ", err.Error()))
		return
	}

	var object models.GmBase

	err = s.Get(&req, &object)
	if err != nil {
		e.Error(http.StatusUnprocessableEntity, err, "查询失败")
		return
	}

	e.OK(object, "查询成功")
}

func (e GmManage) GetConfigMD5(c *gin.Context) {
	req := dto.GmGetConfigMD5{}
	err := e.MakeContext(c).Bind(&req, binding.Query).Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, fmt.Sprintf(" %s ", err.Error()))
		return
	}

	if req.Key != "nVLgd9.jmCGwUC_rUp@hM-P-C8cxBeBZ" {
		e.Error(500, err, fmt.Sprintf(" 参数错误"))
		return
	}

	//发送http请求
	param := make(url.Values)
	param["cmd"] = []string{"4000049"}
	param["routeKey"] = []string{"platform"}
	param["Key"] = []string{config.ExtConfig.GetGMKey()}

	gmAddr, err := config.ExtConfig.GetGMAddrByEnv(req.Env)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("找不到对应环境 %s", err.Error()))
		return
	}

	rsp, err := pkg.PostForm(gmAddr.GetGMPlatformURL(), param)
	if err != nil {
		e.Logger.Error(err, rsp)
		e.Error(500, err, fmt.Sprintf(" %s ", err.Error()))
		return
	}

	result := gjson.ParseBytes(rsp)
	if result.Get("Code").Int() != 0 {
		e.Logger.Error("gmsrv err code", result.Get("Code").Int())
		e.Error(500, err, fmt.Sprintf("gmsrv err code:%d ", result.Get("Code").Int()))
		return
	}

	if !result.Get("Data.MD5").Exists() {
		e.Error(500, err, "获取MD5失败")
		return
	}

	md5 := result.Get("Data.MD5").String()
	l := strings.Split(md5, "/")
	c.String(http.StatusOK, l[len(l)-1])
}

// DoneGmCmd
// @Summary 执行GM命令
// @Description 执行GM
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Security Bearer
func (e GmManage) DoneGmCmd(c *gin.Context) {

	s := service.GmManage{}
	// req := dto.GmDoneReq{}
	reqx := make(map[string]interface{})

	err := e.MakeContext(c).
		MakeOrm().
		Bind(&reqx, binding.JSON).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Info("DoneGmCmd")
	gmCmd := cast.ToInt(reqx["gmCmd"])
	gmUid := cast.ToString(reqx["gmUid"])
	if gmUid != "" {
		reqx["player_id"] = cast.ToInt64(gmUid)
	}
	remark := cast.ToString(reqx["remark"])

	rsp, errorRpc := rpc.RpcGm(c, gmCmd, reqx)
	if errorRpc != nil {
		e.Logger.Error(errorRpc)
		e.Error(500, errorRpc, errorRpc.Error())
		return
	}

	logContent, _ := json.Marshal(reqx)
	envType, _ := c.Get("envID")
	s.LogRecord(&models.LogGm{
		Model:       cModels.Model{},
		PlayId:      gmUid,
		TimeStamp:   time.Now(),
		GmCmd:       strconv.Itoa(gmCmd),
		GmType:      "",
		GmEnv:       cast.ToString(envType),
		RequestJson: string(logContent),
		Remark:      remark,
		RspJson:     rsp,
		GmOperator:  user.GetUserName(c),
	})

	e.OK("ok", "执行成功")
}
