package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
)

type LogGm struct {
	api.Api
}

// GetPage 获取LogGm列表
// @Summary 获取LogGm列表
// @Description 获取LogGm列表
// @Tags LogGm
// @Param playId query string false "玩家ID"
// @Param timeStamp query time.Time false "时间戳"
// @Param gmEnv query string false "1:dev 2:test 3:pre 4:product"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.LogGm}} "{"code": 200, "data": [...]}"
// @Router /api/v1/log-gm [get]
// @Security Bearer
func (e LogGm) GetPage(c *gin.Context) {
	req := dto.LogGmGetPageReq{}
	s := service.LogGm{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.GmEnv = c.GetHeader("env")

	p := actions.GetPermissionFromContext(c)
	list := make([]models.LogGm, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取LogGm 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取LogGm
// @Summary 获取LogGm
// @Description 获取LogGm
// @Tags LogGm
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.LogGm} "{"code": 200, "data": [...]}"
// @Router /api/v1/log-gm/{id} [get]
// @Security Bearer
func (e LogGm) Get(c *gin.Context) {
	req := dto.LogGmGetReq{}
	s := service.LogGm{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.LogGm

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取LogGm失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建LogGm
// @Summary 创建LogGm
// @Description 创建LogGm
// @Tags LogGm
// @Accept application/json
// @Product application/json
// @Param data body dto.LogGmInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/log-gm [post]
// @Security Bearer
func (e LogGm) Insert(c *gin.Context) {
	req := dto.LogGmInsertReq{}
	s := service.LogGm{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建LogGm  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改LogGm
// @Summary 修改LogGm
// @Description 修改LogGm
// @Tags LogGm
// @Accept application/json
// @Product application/json
// @Param data body dto.LogGmUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/log-gm/{id} [put]
// @Security Bearer
func (e LogGm) Update(c *gin.Context) {
	req := dto.LogGmUpdateReq{}
	s := service.LogGm{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改LogGm 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除LogGm
// @Summary 删除LogGm
// @Description 删除LogGm
// @Tags LogGm
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/log-gm [delete]
// @Security Bearer
func (e LogGm) Delete(c *gin.Context) {
	s := service.LogGm{}
	req := dto.LogGmDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除LogGm失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
