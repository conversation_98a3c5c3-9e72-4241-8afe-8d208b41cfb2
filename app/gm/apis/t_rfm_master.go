package apis

import (
	"encoding/json"
	"fmt"
	"go-admin/common/rpc"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
)

type TRfmMaster struct {
	api.Api
}

// GetPage 获取TRfmMaster列表
// @Summary 获取TRfmMaster列表
// @Description 获取TRfmMaster列表
// @Tags TRfmMaster
// @Param rfmType query int64 false "操作类型"
// @Param remark query string false "备注"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TRfmMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-rfm-master [get]
// @Security Bearer
func (e TRfmMaster) GetPage(c *gin.Context) {
	req := dto.TRfmMasterGetPageReq{}
	s := service.TRfmMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TRfmMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TRfmMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TRfmMaster
// @Summary 获取TRfmMaster
// @Description 获取TRfmMaster
// @Tags TRfmMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TRfmMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-rfm-master/{id} [get]
// @Security Bearer
func (e TRfmMaster) Get(c *gin.Context) {
	req := dto.TRfmMasterGetReq{}
	s := service.TRfmMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TRfmMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TRfmMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TRfmMaster
// @Summary 创建TRfmMaster
// @Description 创建TRfmMaster
// @Tags TRfmMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TRfmMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-rfm-master [post]
// @Security Bearer
func (e TRfmMaster) Insert(c *gin.Context) {
	req := dto.TRfmMasterInsertReq{}
	s := service.TRfmMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	rpcReq := &dto.TRfmRpcReq{}
	rpcReq.ReqType = int32(req.RfmType)

	js, err := json.Marshal(rpcReq)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	_, rsp, errRpc := rpc.RpcGm2GameJson("4000057", req.RpcReq, "platform", string(js), c)
	if errRpc != nil {
		e.Logger.Error(errRpc)
		e.Error(500, errRpc, "RPC操作失败")
		return
	}

	req.RpcRsp = rsp
	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TRfmMaster  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TRfmMaster
// @Summary 修改TRfmMaster
// @Description 修改TRfmMaster
// @Tags TRfmMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TRfmMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-rfm-master/{id} [put]
// @Security Bearer
func (e TRfmMaster) Update(c *gin.Context) {
	req := dto.TRfmMasterUpdateReq{}
	s := service.TRfmMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TRfmMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TRfmMaster
// @Summary 删除TRfmMaster
// @Description 删除TRfmMaster
// @Tags TRfmMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-rfm-master [delete]
// @Security Bearer
func (e TRfmMaster) Delete(c *gin.Context) {
	s := service.TRfmMaster{}
	req := dto.TRfmMasterDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TRfmMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}
