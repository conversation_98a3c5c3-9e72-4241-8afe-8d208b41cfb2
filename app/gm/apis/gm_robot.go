package apis

import (
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"go-admin/app/gm/service/dto"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg"
	_ "go-admin/core/sdk/pkg/response"
)

type GmRobotManage struct {
	api.Api
}

func (e GmRobotManage) GetInfo(c *gin.Context) {
	req := dto.GmRobotReq{}
	err := e.MakeContext(c).
		Bind(&req, binding.Form).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.<PERSON>rror(500, err, err.<PERSON>rror())
		return
	}

	url := req.ReqUrl + req.Param
	if req.DoType == 1 {
		rsp, errReq := pkg.Get(url)
		if errReq != nil {
			e.Logger.Error(errReq)
			e.Error(500, errReq, "压测服务器异常")
		}
		e.OK(rsp, "查询成功")
	} else {
		pkg.GetSingle(url)
		e.OK("success", "查询成功")
	}
}
