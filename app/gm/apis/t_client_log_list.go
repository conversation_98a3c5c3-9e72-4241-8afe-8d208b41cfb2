package apis

import (
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	"go-admin/common/rpc"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/config"
	"go-admin/core/sdk/pkg/S3"
	"go-admin/core/sdk/pkg/jwtauth/user"
	"go-admin/core/sdk/pkg/planet"
	_ "go-admin/core/sdk/pkg/response"
	"sort"
	"strings"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
)

type TClientLogList struct {
	api.Api
}

// GetPage 获取TClientLogList列表
// @Summary 获取TClientLogList列表
// @Description 获取TClientLogList列表
// @Tags TClientLogList
// @Param uid query string false "用户ID"
// @Param timeStamp query time.Time false "时间戳"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TClientLogList}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-client-log-list [get]
// @Security Bearer
func (e TClientLogList) GetPage(c *gin.Context) {
	req := dto.TClientLogListGetPageReq{}
	s := service.TClientLogList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TClientLogList, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TClientLogList 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TClientLogList
// @Summary 获取TClientLogList
// @Description 获取TClientLogList
// @Tags TClientLogList
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TClientLogList} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-client-log-list/{id} [get]
// @Security Bearer
func (e TClientLogList) Get(c *gin.Context) {
	req := dto.TClientLogListGetReq{}
	s := service.TClientLogList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TClientLogList

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TClientLogList失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TClientLogList
// @Summary 创建TClientLogList
// @Description 创建TClientLogList
// @Tags TClientLogList
// @Accept application/json
// @Product application/json
// @Param data body dto.TClientLogListInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-client-log-list [post]
// @Security Bearer
func (e TClientLogList) Insert(c *gin.Context) {
	req := dto.TClientLogListInsertReq{}
	s := service.TClientLogList{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))
	req.PlanetId = planet.GetPlanetID(c)
	req.LogStatus = 1
	req.CreateName = user.GetUserNick(c)

	pTimeArr, result, success := e.RpcHandle(c, s, req, 0)
	if !success {
		e.Error(500, err, fmt.Sprintf("RPC游戏服 失败"))
		return
	}

	req.RpcJson = result

	//// 将pSetArrNew数组拼接称逗号分割字符串存到数据库
	var oriStr string
	for nIndex, dateStr := range pTimeArr {
		oriStr = oriStr + strings.ReplaceAll(dateStr, "-", "")
		if nIndex != len(pTimeArr)-1 {
			oriStr = oriStr + ","
		}
	}
	req.TimeStamp = oriStr

	msgStr := "开启"
	err = s.Insert(&req)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("创建TClientLogList  失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(req.GetId(), msgStr+req.Uid+"客户端日志策略成功")
}

func (e TClientLogList) RpcHandle(c *gin.Context, s service.TClientLogList, req dto.TClientLogListInsertReq, Id int) ([]string, string, bool) {
	isExist, modelReturn, errGet := s.Load(&req)
	if errGet != nil {
		e.Error(500, errGet, fmt.Sprintf("Mysql Get异常，\r\n信息 %s", errGet.Error()))
		return nil, "", true
	}

	// 处理Req请求传过来的日期数据，逗号分割，去除-
	pTimeArr := strings.Split(req.TimeStamp, ",")
	var pSetArr []string
	for _, s2 := range pTimeArr {
		pSetArr = append(pSetArr, strings.ReplaceAll(s2, "-", ""))
	}

	var pSetArrNew []string
	if isExist {
		// 如果存在则将所有时间合并去重，拼接在一起，排序
		var originTimeStr string
		for i := 0; i < len(modelReturn); i++ {
			if modelReturn[i].Id == Id {
				continue
			}
			originTimeStr = originTimeStr + modelReturn[i].TimeStamp
			if i != len(modelReturn)-1 {
				originTimeStr = originTimeStr + ","
			}
		}
		pTimeArr2 := strings.Split(originTimeStr, ",")
		pSetArr = append(pSetArr, pTimeArr2...)
		pSetArrNew = RemoveRepeatedElementAndEmpty(pSetArr)
		sort.Strings(pSetArrNew)
	} else {
		pSetArrNew = pSetArr
	}

	// RPC到游戏服
	_, result, err := rpc.RpcGm2Game(rpc.Req{
		GmUid:            req.Uid,
		GmCmd:            "4000087",
		UID:              req.Uid,
		EnableLog:        true,
		EnableReceiveLog: true,
		LogDate:          pSetArrNew,
	}, c)

	if err != nil {
		e.Error(500, err, fmt.Sprintf("通知GM服务失败，\r\n信息 %s", err.Error()))
		return nil, "", false
	}
	return pTimeArr, result, true
}

func RemoveRepeatedElementAndEmpty(arr []string) []string {
	newArr := make([]string, 0)
	for _, item := range arr {
		if "" == strings.TrimSpace(item) {
			continue
		}

		repeat := false
		if len(newArr) > 0 {
			for _, v := range newArr {
				if v == item {
					repeat = true
					break
				}
			}
		}

		if repeat {
			continue
		}

		newArr = append(newArr, item)
	}
	return newArr
}

// Update 修改TClientLogList
// @Summary 修改TClientLogList
// @Description 修改TClientLogList
// @Tags TClientLogList
// @Accept application/json
// @Product application/json
// @Param data body dto.TClientLogListUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-client-log-list/{id} [put]
// @Security Bearer
func (e TClientLogList) Update(c *gin.Context) {
	req := dto.TClientLogListUpdateReq{}
	s := service.TClientLogList{}
	req.LogStatus = 2
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	req.CreateName = user.GetUserNick(c)
	req.PlanetId = planet.GetPlanetID(c)

	p := actions.GetPermissionFromContext(c)

	// 关闭时 RPC到游戏服，同步最新的数据
	reqRpc := dto.TClientLogListInsertReq{}
	reqRpc.Uid = req.Uid
	reqRpc.PlanetId = req.PlanetId
	_, result, success := e.RpcHandle(c, s, reqRpc, req.Id)
	if !success {
		e.Error(500, err, fmt.Sprintf("RPC游戏服 失败"))
		return
	}

	req.RpcJson = result

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TClientLogList 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TClientLogList
// @Summary 删除TClientLogList
// @Description 删除TClientLogList
// @Tags TClientLogList
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-client-log-list [delete]
// @Security Bearer
func (e TClientLogList) Delete(c *gin.Context) {
	s := service.TClientLogList{}
	req := dto.TClientLogListDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TClientLogList失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "删除成功")
}

// GetLogList 获取S3 TClientLogList列表
// @Summary 获取TClientLogList列表
// @Description 获取TClientLogList列表
// @Tags TClientLogList
// @Param uid query string false "用户ID"
// @Param timeStamp query time.Time false "时间戳"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TClientLogList}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-client-log-list [get]
// @Security Bearer
func (e TClientLogList) GetLogList(c *gin.Context) {
	req := dto.TClientLogListGetLogListReq{}
	//s := service.TClientLogList{}
	err := e.MakeContext(c).
		Bind(&req).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	list := make([]models.TS3Log, 0)

	scCfg := config.S3Config
	//remoteBucketHost := "https://d1e9q4gqdlef28.cloudfront.net/"
	//envStr := "Develop"
	//uid := "89061"
	//timeDay := "20220902"
	remoteBucketHost := scCfg.RemoteBucketHost
	uid := req.Uid

	envStr, errEnv := planet.GetEnvName(c)
	if errEnv != nil {
		e.Logger.Error(errEnv)
		e.Error(500, errEnv, errEnv.Error())
		return
	}

	//请求S3获取文件列表
	s3Session := S3.GetS3Session()
	var key *string
	if req.DayTime.IsZero() {
		key = aws.String(fmt.Sprintf("mt/%s/%s", envStr, uid))
	} else {
		timeDay := req.DayTime.Local().Format("20060102")
		key = aws.String(fmt.Sprintf("mt/%s/%s/%s", envStr, uid, timeDay))
	}

	svc := s3.New(s3Session)

	pageNum := 0
	params := &s3.ListObjectsInput{
		Bucket: aws.String(scCfg.BucketKey), //配置参数
		Prefix: key,
	}
	err = svc.ListObjectsPages(params,
		func(page *s3.ListObjectsOutput, lastPage bool) bool {
			pageNum++
			for _, item := range page.Contents {
				iKey := *item.Key
				keyArray := strings.Split(iKey, "/")
				fileName := keyArray[len(keyArray)-1]
				row := models.TS3Log{
					Key:          iKey,
					Size:         *item.Size,
					LastModified: *item.LastModified,
					Url:          remoteBucketHost + *item.Key,
					FileName:     fileName,
				}
				list = append(list, row)
			}
			return true
		})

	if req.BatchFlg == 1 {
		e.PageOK(list, len(list), req.GetPageIndex(), req.GetPageSize(), "查询成功")
		return
	}

	//去除全部数据后，进行分页
	listRet := make([]models.TS3Log, 0)
	aPage := req.GetPageIndex() - 1
	aPageRa := aPage * req.PageSize
	aPageRb := aPage*req.PageSize + req.GetPageSize()

	if (aPageRa > len(list)) && (aPageRb > len(list)) {
		// 选择第二页，切换一页显示数量的情况
		aPageRa = 0
		aPageRb = 10
	} else {
		if aPageRb > len(list) {
			aPageRb = len(list)
		}
	}
	listRet = list[aPageRa:aPageRb]

	e.PageOK(listRet, len(list), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}
