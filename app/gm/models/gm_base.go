package models

import "go-admin/common/models"

type GmBase struct {
	GmId    int    `json:"gmId" gorm:"primaryKey;autoIncrement"` // 编码
	GmName  string `json:"gmName" gorm:"size:128;"`              // 名称
	GmType  string `json:"gmType" gorm:"size:128;"`              // 命令类型
	Status  string `json:"status" gorm:"size:4;"`                // （1:inactive 2:active）
	GmCmd   string `json:"gmCmd" gorm:"size:128;"`               // GM Cmd
	GmParam string `json:"gmParam" gorm:"size:255;"`             // gmParamJson
	Remark  string `json:"remark" gorm:"size:255;"`              // 备注
	models.ControlBy
	models.ModelTime
}

func (GmBase) TableName() string {
	return "gm_base"
}

func (e *GmBase) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *GmBase) GetId() interface{} {
	return e.GmId
}
