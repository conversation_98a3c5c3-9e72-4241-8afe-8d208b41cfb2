package models

import "go-admin/common/models"

type PropType struct {
	PropId        int    `json:"propId" gorm:"primaryKey;autoIncrement"` // 编码
	PropName      string `json:"propName" gorm:"size:128;"`              // 名称
	PropType      string `json:"propType" gorm:"size:128;"`              // 类型
	PropHadPropid int    `json:"propHadPropId" gorm:"int;"`              // 类型
	Status        string `json:"status" gorm:"size:4;"`                  // （1:inactive 2:active）
	models.ControlBy
	models.ModelTime
}

func (PropType) TableName() string {
	return "sp_prop_master"
}

func (e *PropType) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *PropType) GetId() interface{} {
	return e.PropId
}
