package models

import (
	"time"

	"go-admin/common/models"
)

type TS3Log struct {
	Key          string    `json:"key"`
	Size         int64     `json:"size"`
	LastModified time.Time `json:"timeStamp" `
	Url          string    `json:"url" `
	FileName     string    `json:"fileName" `
}

type TClientLogList struct {
	models.Model

	PlanetId   int    `json:"planetId" gorm:"type:bigint;comment:planet"`
	Uid        string `json:"uid" gorm:"type:varchar(20);comment:用户ID"`
	LogStatus  int64  `json:"logStatus" gorm:"type:tinyint(1);comment:操作状态 1：开启 2：关闭"`
	Remark     string `json:"remark" gorm:"type:varchar(255);comment:备注"`
	TimeStamp  string `json:"timeStamp" gorm:"type:timestamp;comment:时间戳"`
	RpcJson    string `json:"rpcJson" gorm:"type:text;comment:RPC请求Json"`
	CreateName string `json:"createName" gorm:"type:text;comment:创建玩家昵称"`
	models.ModelTime
	models.ControlBy
}

func (TClientLogList) TableName() string {
	return "t_client_log_list"
}

func (e *TClientLogList) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TClientLogList) GetId() interface{} {
	return e.Id
}
