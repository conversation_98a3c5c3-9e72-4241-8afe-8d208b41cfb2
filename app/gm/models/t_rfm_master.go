package models

import (
	"go-admin/common/models"
)

type TRfmMaster struct {
	models.Model

	PlanetId int64  `json:"planetId" gorm:"type:bigint;comment:planet"`
	RfmType  int64  `json:"rfmType" gorm:"type:int;comment:操作类型"`
	RpcReq   string `json:"rpcReq" gorm:"type:text;comment:rpc请求Json"`
	RpcRsp   string `json:"rpcRsp" gorm:"type:text;comment:rpc返回Json"`
	Remark   string `json:"remark" gorm:"type:varchar(255);comment:备注"`
	models.ModelTime
	models.ControlBy
}

func (TRfmMaster) TableName() string {
	return "t_rfm_master"
}

func (e *TRfmMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TRfmMaster) GetId() interface{} {
	return e.Id
}
