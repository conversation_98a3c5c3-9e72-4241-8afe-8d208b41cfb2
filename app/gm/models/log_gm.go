package models

import (
	"time"

	"go-admin/common/models"
)

type LogGm struct {
	models.Model

	PlayId      string    `json:"playId" gorm:"type:varchar(255);comment:玩家ID"`
	TimeStamp   time.Time `json:"timeStamp" gorm:"type:timestamp;comment:时间戳"`
	GmCmd       string    `json:"gmCmd" gorm:"type:int;comment:GmCmd"`
	GmType      string    `json:"gmType" gorm:"type:varchar(128);comment:归类"`
	GmEnv       string    `json:"gmEnv" gorm:"type:varchar(4);comment:1:dev 2:test 3:pre 4:product"`
	RequestJson string    `json:"requestJson" gorm:"type:text;comment:RPC请求Json"`
	Remark      string    `json:"remark" gorm:"type:varchar(255);comment:Remark"`
	RspJson     string    `json:"rspJson" gorm:"type:text;comment:RPC返回Json"`
	GmOperator  string    `json:"gmOperator"  gorm:"type:text;comment:操作者"`
	models.ModelTime
	models.ControlBy
}

func (LogGm) TableName() string {
	return "log_gm"
}

func (e *LogGm) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *LogGm) GetId() interface{} {
	return e.Id
}
