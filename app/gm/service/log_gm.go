package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type LogGm struct {
	service.Service
}

// GetPage 获取LogGm列表
func (e *LogGm) GetPage(c *dto.LogGmGetPageReq, p *actions.DataPermission, list *[]models.LogGm, count *int64) error {
	var err error
	var data models.LogGm

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Order("created_at DESC").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("LogGmService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取LogGm对象
func (e *LogGm) Get(d *dto.LogGmGetReq, p *actions.DataPermission, model *models.LogGm) error {
	var data models.LogGm

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetLogGm error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建LogGm对象
func (e *LogGm) Insert(c *dto.LogGmInsertReq) error {
	var err error
	var data models.LogGm
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("LogGmService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改LogGm对象
func (e *LogGm) Update(c *dto.LogGmUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.LogGm{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("LogGmService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除LogGm
func (e *LogGm) Remove(d *dto.LogGmDeleteReq, p *actions.DataPermission) error {
	var data models.LogGm

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveLogGm error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
