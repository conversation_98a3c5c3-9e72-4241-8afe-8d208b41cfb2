package service

import (
	"go-admin/app/gm/models"
	"go-admin/core/sdk/service"
)

type PropType struct {
	service.Service
}

// GetAllPropType 获取GetAllPropType列表
func (e *PropType) GetAllPropType(list *[]models.PropType, count *int64) error {
	var err error
	var data models.PropType

	err = e.Orm.Model(&data).
		Find(list).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}
