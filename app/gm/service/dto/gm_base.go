package dto

import (
	"go-admin/common/dto"
)

type GmGetPageReq struct {
	dto.Pagination `search:"-"`
	GmCmd          string `form:"gmCmd" search:"type:exact;column:gm_cmd;table:gm_base" comment:"GM命令"` // GM命令
	GmType         string `form:"gmType" search:"type:exact;column:gm_type;table:gm_base" comment:"分类"` // 分类
	Status         string `form:"status" search:"type:exact;column:status;table:gm_base" comment:"状态"`  // 状态
}

func (m *GmGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type GmGetReq struct {
	Id int `uri:"gmId"`
}

func (s *GmGetReq) GetId() interface{} {
	return s.Id
}

type GmGetConfigMD5 struct {
	Env string `form:"env"` //环境
	Key string `form:"key"` //key检验
}
