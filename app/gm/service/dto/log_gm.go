package dto

import (
	"time"

	"go-admin/app/gm/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type LogGmGetPageReq struct {
	dto.Pagination `search:"-"`
	PlayId         string    `form:"playId"  search:"type:exact;column:play_id;table:log_gm" comment:"玩家ID"`
	GmOperator     string    `form:"gmOperator"  search:"type:exact;column:gm_operator;table:log_gm" comment:"操作者"`
	TimeStamp      time.Time `form:"timeStamp"  search:"type:exact;column:time_stamp;table:log_gm" comment:"时间戳"`
	GmEnv          string    `form:"gmEnv"  search:"type:exact;column:gm_env;table:log_gm" comment:"1:dev 2:test 3:pre 4:product"`
	GmCmd          string    `form:"gmCmd"  search:"type:exact;column:gm_cmd;table:log_gm" comment:"操作CMD"`
	LogGmOrder
}

type LogGmOrder struct {
	Id          int       `form:"idOrder"  search:"type:order;column:id;table:log_gm"`
	PlayId      string    `form:"playIdOrder"  search:"type:order;column:play_id;table:log_gm"`
	TimeStamp   time.Time `form:"timeStampOrder"  search:"type:order;column:time_stamp;table:log_gm"`
	GmCmd       string    `form:"gmCmdOrder"  search:"type:order;column:gm_cmd;table:log_gm"`
	GmType      string    `form:"gmTypeOrder"  search:"type:order;column:gm_type;table:log_gm"`
	GmEnv       string    `form:"gmEnvOrder"  search:"type:order;column:gm_env;table:log_gm"`
	GmOperator  string    `form:"GmOperator"  search:"type:order;column:gm_operator;table:log_gm"`
	RequestJson string    `form:"requestJsonOrder"  search:"type:order;column:request_json;table:log_gm"`
	Remark      string    `form:"remarkOrder"  search:"type:order;column:remark;table:log_gm"`
	RspJson     string    `form:"rspJsonOrder"  search:"type:order;column:rsp_json;table:log_gm"`
}

func (m *LogGmGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type LogGmInsertReq struct {
	Id          int       `json:"-" comment:""` //
	PlayId      string    `json:"playId" comment:"玩家ID"`
	TimeStamp   time.Time `json:"timeStamp" comment:"时间戳"`
	GmCmd       string    `json:"gmCmd" comment:""`
	GmType      string    `json:"gmType" comment:"归类"`
	GmEnv       string    `json:"gmEnv" comment:"1:dev 2:test 3:pre 4:product"`
	RequestJson string    `json:"requestJson" comment:"RPC请求Json"`
	Remark      string    `json:"remark" comment:""`
	RspJson     string    `json:"rspJson" comment:"RPC返回Json"`
	common.ControlBy
}

func (s *LogGmInsertReq) Generate(model *models.LogGm) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlayId = s.PlayId
	model.TimeStamp = s.TimeStamp
	model.GmCmd = s.GmCmd
	model.GmType = s.GmType
	model.GmEnv = s.GmEnv
	model.RequestJson = s.RequestJson
	model.Remark = s.Remark
	model.RspJson = s.RspJson
}

func (s *LogGmInsertReq) GetId() interface{} {
	return s.Id
}

type LogGmUpdateReq struct {
	Id          int       `uri:"id" comment:""` //
	PlayId      string    `json:"playId" comment:"玩家ID"`
	TimeStamp   time.Time `json:"timeStamp" comment:"时间戳"`
	GmCmd       string    `json:"gmCmd" comment:""`
	GmType      string    `json:"gmType" comment:"归类"`
	GmEnv       string    `json:"gmEnv" comment:"1:dev 2:test 3:pre 4:product"`
	RequestJson string    `json:"requestJson" comment:"RPC请求Json"`
	Remark      string    `json:"remark" comment:""`
	RspJson     string    `json:"rspJson" comment:"RPC返回Json"`
	common.ControlBy
}

func (s *LogGmUpdateReq) Generate(model *models.LogGm) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlayId = s.PlayId
	model.TimeStamp = s.TimeStamp
	model.GmCmd = s.GmCmd
	model.GmType = s.GmType
	model.GmEnv = s.GmEnv
	model.RequestJson = s.RequestJson
	model.Remark = s.Remark
	model.RspJson = s.RspJson
}

func (s *LogGmUpdateReq) GetId() interface{} {
	return s.Id
}

// LogGmGetReq 功能获取请求参数
type LogGmGetReq struct {
	Id int `uri:"id"`
}

func (s *LogGmGetReq) GetId() interface{} {
	return s.Id
}

// LogGmDeleteReq 功能删除请求参数
type LogGmDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *LogGmDeleteReq) GetId() interface{} {
	return s.Ids
}
