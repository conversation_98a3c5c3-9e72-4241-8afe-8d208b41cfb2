package dto

type GmDoneReq struct {
	Id         int    `form:"gmId" comment:"GM编码"`             // GM编码
	GmUid      string `form:"gmUid" comment:"用户ID"`            // 用户ID
	GmName     string `form:"gmName" comment:"GM名称"`           // GM名称
	GmCmd      string `form:"gmCmd" comment:"GM名称"`            // GMCmd
	GmOperator string `form:"gmOperator" comment:"gmOperator"` // gmOperator
	PropType   int32  `form:"PropType" comment:"PropType"`     // PropType
	PropID     int32  `form:"PropID" comment:"PropID"`         // PropID
	Value      int32  `form:"Value" comment:"Value"`           // Value
	Remark     string `form:"reason"  comment:"reason"`        // 备注
	ReqType    int32  `form:"ReqType"  comment:"reason"`       // ReqType
}

func (m *GmDoneReq) GetNeedSearch() interface{} {
	return *m
}

func (m *GmDoneReq) GetId() interface{} {
	return m.Id
}
