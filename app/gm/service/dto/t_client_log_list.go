package dto

import (
	"time"

	"go-admin/app/gm/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TClientLogListGetPageReq struct {
	dto.Pagination `search:"-"`
	Uid            string `form:"uid"  search:"type:exact;column:uid;table:t_client_log_list" comment:"用户ID"`
	BeginTime      string `form:"beginTime" search:"type:gte;column:created_at;table:t_client_log_list" comment:"创建时间"`
	EndTime        string `form:"endTime" search:"type:lte;column:created_at;table:t_client_log_list" comment:"创建时间"`
	CreateName     string `form:"createName"  search:"type:contains;column:create_name;table:t_client_log_list" comment:"创建者"`
	Remark         string `form:"remark"  search:"type:contains;column:remark;table:t_client_log_list" comment:"remark"`
	LogStatus      string `form:"logStatus"  search:"type:contains;column:log_status;table:t_client_log_list" comment:"remark"`
	PlanetId       string `form:"planetId"  search:"type:contains;column:planet_id;table:t_client_log_list" comment:"planet_id"`
	TClientLogListOrder
}

type TClientLogListGetLogListReq struct {
	dto.Pagination `search:"-"`
	Uid            string    `form:"uid"  search:"type:exact;column:uid;table:t_client_log_list" comment:"用户ID"`
	DayTime        time.Time `form:"dayTime"  search:"type:exact;column:time_stamp;table:t_client_log_list" comment:"时间戳"`
	BatchFlg       int       `form:"batchFlg" comment:"是否全量下载"`
}

type TClientLogListOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:t_client_log_list"`
	PlanetId  string    `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_client_log_list"`
	Uid       string    `form:"uidOrder"  search:"type:order;column:uid;table:t_client_log_list"`
	LogStatus int64     `form:"logStatusOrder"  search:"type:order;column:log_status;table:t_client_log_list"`
	Remark    string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_client_log_list"`
	CreateBy  string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_client_log_list"`
	TimeStamp time.Time `form:"timeStampOrder"  search:"type:order;column:time_stamp;table:t_client_log_list"`
	UpdateBy  string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_client_log_list"`
	CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_client_log_list"`
	UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_client_log_list"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_client_log_list"`
}

func (m *TClientLogListGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TClientLogListInsertReq struct {
	Id         int    `json:"-" comment:""` //
	PlanetId   int    `json:"planetId" comment:"planet"`
	Uid        string `json:"uid" comment:"用户ID"`
	LogStatus  int64  `json:"logStatus" comment:"操作状态 1：开启 2：关闭"`
	Remark     string `json:"remark" comment:"备注"`
	TimeStamp  string `json:"timeStamp" comment:"时间戳"`
	RpcJson    string `json:"rpcJson" comment:"RPC请求Json"`
	CreateName string `json:"createName" comment:"创建玩家昵称"`
	common.ControlBy
}

func (s *TClientLogListInsertReq) Generate(model *models.TClientLogList) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.LogStatus = s.LogStatus
	model.Remark = s.Remark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
	model.TimeStamp = s.TimeStamp
	model.RpcJson = s.RpcJson
	model.CreateName = s.CreateName
}

func (s *TClientLogListInsertReq) GetId() interface{} {
	return s.Id
}

type TClientLogListUpdateReq struct {
	Id         int    `uri:"id" comment:""` //
	PlanetId   int    `json:"planetId" comment:"planet"`
	Uid        string `json:"uid" comment:"用户ID"`
	LogStatus  int64  `json:"logStatus" comment:"操作状态 1：开启 2：关闭"`
	Remark     string `json:"remark" comment:"备注"`
	TimeStamp  string `json:"timeStamp" comment:"时间戳"`
	RpcJson    string `json:"rpcJson" gorm:"comment:RPC请求Json"`
	CreateName string `json:"createName" comment:"创建玩家昵称"`
	common.ControlBy
}

func (s *TClientLogListUpdateReq) Generate(model *models.TClientLogList) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.Uid = s.Uid
	model.LogStatus = s.LogStatus
	model.Remark = s.Remark
	model.TimeStamp = s.TimeStamp
	model.RpcJson = s.RpcJson
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
	model.CreateName = s.CreateName
}

func (s *TClientLogListUpdateReq) GetId() interface{} {
	return s.Id
}

// TClientLogListGetReq 功能获取请求参数
type TClientLogListGetReq struct {
	Id int `uri:"id"`
}

func (s *TClientLogListGetReq) GetId() interface{} {
	return s.Id
}

// TClientLogListDeleteReq 功能删除请求参数
type TClientLogListDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TClientLogListDeleteReq) GetId() interface{} {
	return s.Ids
}
