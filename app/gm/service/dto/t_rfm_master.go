package dto

import (
	"time"

	"go-admin/app/gm/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TRfmMasterGetPageReq struct {
	dto.Pagination `search:"-"`
	PlanetId       int64  `form:"planetId"  search:"type:exact;column:planet_id;table:t_rfm_master" comment:"planet_id"`
	RfmType        int64  `form:"rfmType"  search:"type:exact;column:rfm_type;table:t_rfm_master" comment:"操作类型"`
	Remark         string `form:"remark"  search:"type:contains;column:remark;table:t_rfm_master" comment:"备注"`
	TRfmMasterOrder
}

type TRfmMasterOrder struct {
	Id        int       `form:"idOrder"  search:"type:order;column:id;table:t_rfm_master"`
	PlanetId  int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_rfm_master"`
	RfmType   int64     `form:"rfmTypeOrder"  search:"type:order;column:rfm_type;table:t_rfm_master"`
	RpcReq    string    `form:"rpcReqOrder"  search:"type:order;column:rpc_req;table:t_rfm_master"`
	RpcRsp    string    `form:"rpcRspOrder"  search:"type:order;column:rpc_rsp;table:t_rfm_master"`
	Remark    string    `form:"remarkOrder"  search:"type:order;column:remark;table:t_rfm_master"`
	CreateBy  string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_rfm_master"`
	UpdateBy  string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_rfm_master"`
	CreatedAt time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_rfm_master"`
	UpdatedAt time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_rfm_master"`
	DeletedAt time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_rfm_master"`
}

func (m *TRfmMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TRfmMasterInsertReq struct {
	Id       int    `json:"-" comment:""` //
	PlanetId int64  `json:"planetId" comment:"planet"`
	RfmType  int64  `json:"rfmType" comment:"操作类型"`
	RpcReq   string `json:"rpcReq" comment:"rpc请求Json"`
	RpcRsp   string `json:"rpcRsp" comment:"rpc返回Json"`
	Remark   string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TRfmMasterInsertReq) Generate(model *models.TRfmMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.RfmType = s.RfmType
	model.RpcReq = s.RpcReq
	model.RpcRsp = s.RpcRsp
	model.Remark = s.Remark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TRfmMasterInsertReq) GetId() interface{} {
	return s.Id
}

type TRfmMasterUpdateReq struct {
	Id       int    `uri:"id" comment:""` //
	PlanetId int64  `json:"planetId" comment:"planet"`
	RfmType  int64  `json:"rfmType" comment:"操作类型"`
	RpcReq   string `json:"rpcReq" comment:"rpc请求Json"`
	RpcRsp   string `json:"rpcRsp" comment:"rpc返回Json"`
	Remark   string `json:"remark" comment:"备注"`
	common.ControlBy
}

func (s *TRfmMasterUpdateReq) Generate(model *models.TRfmMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.RfmType = s.RfmType
	model.RpcReq = s.RpcReq
	model.RpcRsp = s.RpcRsp
	model.Remark = s.Remark
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TRfmMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// TRfmMasterGetReq 功能获取请求参数
type TRfmMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *TRfmMasterGetReq) GetId() interface{} {
	return s.Id
}

// TRfmMasterDeleteReq 功能删除请求参数
type TRfmMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TRfmMasterDeleteReq) GetId() interface{} {
	return s.Ids
}

// TRfmRpcReq RPCGame服 RFM操作
type TRfmRpcReq struct {
	ReqType int32 `json:"ReqType" comment:"ReqType"` // ReqType
}
