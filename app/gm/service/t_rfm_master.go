package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TRfmMaster struct {
	service.Service
}

// GetPage 获取TRfmMaster列表
func (e *TRfmMaster) GetPage(c *dto.TRfmMasterGetPageReq, p *actions.DataPermission, list *[]models.TRfmMaster, count *int64) error {
	var err error
	var data models.TRfmMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Order("created_at desc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TRfmMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TRfmMaster对象
func (e *TRfmMaster) Get(d *dto.TRfmMasterGetReq, p *actions.DataPermission, model *models.TRfmMaster) error {
	var data models.TRfmMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTRfmMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TRfmMaster对象
func (e *TRfmMaster) Insert(c *dto.TRfmMasterInsertReq) error {
	var err error
	var data models.TRfmMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TRfmMasterService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TRfmMaster对象
func (e *TRfmMaster) Update(c *dto.TRfmMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TRfmMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TRfmMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TRfmMaster
func (e *TRfmMaster) Remove(d *dto.TRfmMasterDeleteReq, p *actions.DataPermission) error {
	var data models.TRfmMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTRfmMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
