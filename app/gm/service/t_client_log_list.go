package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/gm/models"
	"go-admin/app/gm/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TClientLogList struct {
	service.Service
}

// GetPage 获取TClientLogList列表
func (e *TClientLogList) GetPage(c *dto.TClientLogListGetPageReq, p *actions.DataPermission, list *[]models.TClientLogList, count *int64) error {
	var err error
	var data models.TClientLogList

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Order("id DESC").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TClientLogListService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// GetLogListPage 获取TClientLogList列表
func (e *TClientLogList) GetLogListPage(c *dto.TClientLogListGetPageReq, p *actions.DataPermission, list *[]models.TClientLogList, count *int64) error {
	var err error
	var data models.TClientLogList

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TClientLogListService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TClientLogList对象
func (e *TClientLogList) Get(d *dto.TClientLogListGetReq, p *actions.DataPermission, model *models.TClientLogList) error {
	var data models.TClientLogList

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTClientLogList error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Load 查询对象
func (e *TClientLogList) Load(c *dto.TClientLogListInsertReq) (bool, []models.TClientLogList, error) {
	var err error
	var data models.TClientLogList
	c.Generate(&data)

	tLogList := make([]models.TClientLogList, 0)

	//var model models.TClientLogList
	if err := e.Orm.Table("t_client_log_list").
		//Select("uid").
		Where("planet_id = ? ", c.PlanetId).
		Where("uid = ? ", c.Uid).
		Where("log_status", 1).
		Find(&tLogList).Error; err != nil {
		return false, tLogList, err
	}
	if len(tLogList) > 0 {
		return true, tLogList, nil //多行的话，只取第一行
	}
	if err != nil {
		e.Log.Errorf("TClientLogListService Insert error:%s \r\n", err)
		return false, tLogList, err
	}
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return false, tLogList, err
	}

	return false, tLogList, nil
}

// Insert 创建TClientLogList对象
func (e *TClientLogList) Insert(c *dto.TClientLogListInsertReq) error {
	var err error
	var data models.TClientLogList
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TClientLogListService Insert error:%s \r\n", err)
		return err
	}
	return nil
}

// Update 修改TClientLogList对象
func (e *TClientLogList) Update(c *dto.TClientLogListUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TClientLogList{}

	if err := e.Orm.Table("t_client_log_list").
		//Update("log_status", 2).
		Where("planet_id = ? ", c.PlanetId).
		Where("uid = ? ", c.Uid).
		Find(&data).Error; err != nil {
	}
	c.Generate(&data)
	data.Id = c.Id
	data.LogStatus = 2

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TClientLogListService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TClientLogList
func (e *TClientLogList) Remove(d *dto.TClientLogListDeleteReq, p *actions.DataPermission) error {
	var data models.TClientLogList

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTClientLogList error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
