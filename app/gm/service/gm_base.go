package service

import (
	"errors"
	"go-admin/app/gm/models"
	"go-admin/app/gm/service/dto"
	cDto "go-admin/common/dto"
	"go-admin/core/sdk/service"
	"gorm.io/gorm"
)

type GmManage struct {
	service.Service
}

// GetPage 获取GMBase列表
func (e *GmManage) GetPage(c *dto.GmGetPageReq, list *[]models.GmBase, count *int64) error {
	var err error
	var data models.GmBase

	err = e.Orm.Model(&data).Scopes(
		cDto.MakeCondition(c.GetNeedSearch()),
		cDto.Paginate(c.GetPageSize(), c.GetPageIndex())).
		Order("gm_cmd").
		Find(list).Count(count).
		Error
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Get 获取SysRole对象
func (e *GmManage) Get(d *dto.GmGetReq, model *models.GmBase) error {
	var err error
	db := e.Orm.First(model, d.GetId())
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("db error:%s", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}

	return nil
}

// LogRecord 日志
func (e *GmManage) LogRecord(model *models.LogGm) error {
	var err error
	var data models.LogGm
	db := e.Orm.Model(&data).Create(model)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("db error:%s", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}

	return nil
}
