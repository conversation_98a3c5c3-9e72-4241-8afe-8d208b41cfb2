package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/gm/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTClientLogListRouter)
}

// registerTClientLogListRouter
func registerTClientLogListRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TClientLogList{}
	r := v1.Group("/t-client-log-list").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", actions.PermissionAction(), api.Update)
		r.DELETE("", api.Delete)
	}

	d := v1.Group("/t-client-log-down-list").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		d.GET("", actions.PermissionAction(), api.GetLogList)
	}
}
