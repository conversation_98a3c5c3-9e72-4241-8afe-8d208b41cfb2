package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/gm/apis"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerGmRobotRouter)
}

// 需认证的路由代码
func registerGmRobotRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.GmRobotManage{}

	r2 := v1.Group("/getRobotInfo").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		//r2.GET("", api.GetInfo)
		r2.GET("/", api.GetInfo)
	}
}
