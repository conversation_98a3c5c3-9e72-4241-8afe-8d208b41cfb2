package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/gm/apis"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerGmBaseRouter)
}

// 需认证的路由代码
func registerGmBaseRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.GmManage{}
	r := v1.Group("/gm").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r.GET("", api.GetPage)
		r.GET("/:gmId", api.Get)
	}

	r2 := v1.Group("/gmPropTypeList").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
	{
		r2.GET("", api.GetPropTypes)
	}

	r3 := v1.Group("/gmTool/")
	{
		r3.GET("configMd5", api.GetConfigMD5)
	}

	r4 := v1.Group("/DoneGmCmd").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r4.PUT("", api.DoneGmCmd)
	}
}
