package service

import (
	"errors"
	"go-admin/common/database"
	"go-admin/common/tool"
	"go-admin/core/sdk"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/payment/models"
	"go-admin/app/payment/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TOrderBillLog struct {
	service.Service
}

// GetPage 获取TOrderBillLog列表
func (e *TOrderBillLog) GetPage(c *dto.TOrderBillLogGetPageReq, p *actions.DataPermission, list *[]models.TOrderBillLog, count *int64) error {
	var err error
	var data models.TOrderBillLog

	gameKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameGame)
	gameDb := sdk.Runtime.GetDbByAssignKey(gameKey)

	err = gameDb.Model(&data).Order("time_stamp DESC").
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TOrderBillLogService GetPage error:%s \r\n", err)
		return err
	}

	return nil
}

// Get 获取TOrderBillLog对象
func (e *TOrderBillLog) Get(d *dto.TOrderBillLogGetReq, p *actions.DataPermission, model *models.TOrderBillLog) error {
	var data models.TOrderBillLog

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTOrderBillLog error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}
