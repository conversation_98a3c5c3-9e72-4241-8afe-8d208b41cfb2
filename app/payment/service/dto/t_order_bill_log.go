package dto

import (
    "time"

    "go-admin/common/dto"
)

type TOrderBillLogGetPageReq struct {
	dto.Pagination     `search:"-"`
    OrderId string `form:"orderId"  search:"type:exact;column:order_id;table:t_order_bill_log" comment:"订单号"`
    Uid string `form:"uid"  search:"type:exact;column:uid;table:t_order_bill_log" comment:"玩家ID"`
    PayChannel string `form:"payChannel"  search:"type:exact;column:pay_channel;table:t_order_bill_log" comment:"支付渠道"`
    OrderStatus string `form:"orderStatus"  search:"type:exact;column:order_status;table:t_order_bill_log" comment:"订单状态"`
    TOrderBillLogOrder
}

type TOrderBillLogOrder struct {Id int `form:"idOrder"  search:"type:order;column:id;table:t_order_bill_log"`
    OrderId string `form:"orderIdOrder"  search:"type:order;column:order_id;table:t_order_bill_log"`
    Uid string `form:"uidOrder"  search:"type:order;column:uid;table:t_order_bill_log"`
    PayChannel string `form:"payChannelOrder"  search:"type:order;column:pay_channel;table:t_order_bill_log"`
    ReqUrl string `form:"reqUrlOrder"  search:"type:order;column:req_url;table:t_order_bill_log"`
    OrderStatus string `form:"orderStatusOrder"  search:"type:order;column:order_status;table:t_order_bill_log"`
    SdkOrderStatus string `form:"sdkOrderStatusOrder"  search:"type:order;column:sdk_order_status;table:t_order_bill_log"`
    SdkConsumptionState string `form:"sdkConsumptionStateOrder"  search:"type:order;column:sdk_consumption_state;table:t_order_bill_log"`
    ErrorCode string `form:"errorCodeOrder"  search:"type:order;column:error_code;table:t_order_bill_log"`
    ErrorMsg string `form:"errorMsgOrder"  search:"type:order;column:error_msg;table:t_order_bill_log"`
    DealRet string `form:"dealRetOrder"  search:"type:order;column:deal_ret;table:t_order_bill_log"`
    TimeStamp time.Time `form:"timeStampOrder"  search:"type:order;column:time_stamp;table:t_order_bill_log"`
    
}

func (m *TOrderBillLogGetPageReq) GetNeedSearch() interface{} {
	return *m
}

// TOrderBillLogGetReq 功能获取请求参数
type TOrderBillLogGetReq struct {
     Id int `uri:"id"`
}
func (s *TOrderBillLogGetReq) GetId() interface{} {
	return s.Id
}