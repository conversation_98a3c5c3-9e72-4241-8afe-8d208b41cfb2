package dto

import (
	"go-admin/common/dto"
)

type PaymentGetPageReq struct {
	dto.Pagination `search:"-"`
	OrderId        string `form:"order_id" search:"type:contains;column:order_id;table:t_order_master" comment:"订单ID"`
	Uid            string `form:"uid" search:"type:contains;column:uid;table:t_order_master" comment:"玩家ID"`
	Status         int    `form:"status" search:"type:exact;column:order_status;table:t_order_master" comment:"状态"`
	BeginTime      string `form:"beginTime" search:"type:gte;column:create_stamp;table:t_order_master" comment:"创建时间"`
	EndTime        string `form:"endTime" search:"type:lte;column:create_stamp;table:t_order_master" comment:"创建时间"`
	PaymentOrder
}

type PaymentOrder struct {
	CreatedAtOrder string `search:"type:order;column:time_stamp;table:t_order_master" form:"createdAtOrder"`
}

func (m *PaymentGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type PaymentGetReq struct {
	Id int `uri:"id"`
}

func (s *PaymentGetReq) GetId() interface{} {
	return s.Id
}
