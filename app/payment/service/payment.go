package service

import (
	"go-admin/app/payment/models"
	"go-admin/app/payment/service/dto"
	"go-admin/common/database"
	cDto "go-admin/common/dto"
	"go-admin/common/tool"
	"go-admin/core/sdk"
	"go-admin/core/sdk/service"
)

type Payment struct {
	service.Service
}

// GetPage 获取Payment列表
func (e *Payment) GetPage(c *dto.PaymentGetPageReq, list *[]models.OrderMaster, count *int64) error {
	var err error
	var data models.OrderMaster

	gameKey := tool.GetPlanetSQLKey(tool.GetPlanetIDFromCtx(e.Context), database.ExtDBNameGame)
	gameDb := sdk.Runtime.GetDbByAssignKey(gameKey)

	err = gameDb.Model(&data).
		Table("t_order_master").
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
		).
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.<PERSON>rf("Service GetPaymentPage error:%s", err.Error())
		return err
	}

	return nil
}
