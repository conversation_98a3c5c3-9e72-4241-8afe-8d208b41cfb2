package models

import (
	"time"
)

type OrderMaster struct {
	OrderId       int64     `json:"order_id" gorm:"size:40;comment:订单ID"`
	PurchaseId    int32     `json:"purchase_id" gorm:"size:40;comment:支付ID"`
	Uid           string    `json:"uid" gorm:"size:40;comment:玩家ID"`
	Platform      int       `json:"platform" gorm:"size:10;comment:平台"`
	PayChannel    int       `json:"pay_channel" gorm:"size:10;comment:支付渠道"`
	ProductId     string    `json:"product_id" gorm:"size:40;comment:商品ID"`
	TimeStamp     time.Time `json:"time_stamp" gorm:"size:40;comment:时间戳"`
	CreateStamp   time.Time `json:"create_stamp" gorm:"size:40;comment:订单创建时间"`
	OrderStatus   int       `json:"order_status" gorm:"size:10;comment:订单状态"`
	EntranceType  int       `json:"entrance_type" gorm:"size:40;comment:订单状态(0:未知 1:下单 2:已支付 3:已取消 4:已发货 5:校验失败 6:补单成功 7:坏账"`
	TriggerType   int       `json:"trigger_type" gorm:"size:40;comment:触发支付入口"`
	PriceInCent   int       `json:"price_in_cent" gorm:"size:40;comment:美分计价"`
	Receipt       string    `json:"receipt" gorm:"size:40;comment:收据凭证"`
	FailType      string    `json:"fail_type" gorm:"size:40;comment:失败原因"`
	RewordJson    string    `json:"reword_json" gorm:"size:40;comment:订单物品Json"`
	IsTest        bool      `json:"is_test" gorm:"size:10;comment:是否测试订单"`
	TransactionId string    `json:"transaction_id" gorm:"size:255;comment:TransactionId"`
}

func (OrderMaster) TableName() string {
	return "t_order_master"
}

func (e *OrderMaster) GetId() interface{} {
	return e.OrderId
}
