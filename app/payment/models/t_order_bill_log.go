package models

import (
     "time"
)

type TOrderBillLog struct {
    OrderId string `json:"orderId" gorm:"type:varchar(40);comment:订单号"`
    Uid string `json:"uid" gorm:"type:varchar(40);comment:玩家ID"` 
    PayChannel string `json:"payChannel" gorm:"type:int;comment:支付渠道"` 
    ReqUrl string `json:"reqUrl" gorm:"type:text;comment:请求URL"` 
    OrderStatus string `json:"orderStatus" gorm:"type:tinyint(1);comment:订单状态"` 
    SdkOrderStatus string `json:"sdkOrderStatus" gorm:"type:tinyint(1);comment:SDK返回支付状态"` 
    SdkConsumptionState string `json:"sdkConsumptionState" gorm:"type:tinyint(1);comment:SDK返回消费状态"` 
    ErrorCode string `json:"errorCode" gorm:"type:int;comment:订单内部处理错误码"` 
    ErrorMsg string `json:"errorMsg" gorm:"type:text;comment:订单内部处理错误内容"` 
    DealRet string `json:"dealRet" gorm:"type:text;comment:渠道处理结果"` 
    TimeStamp time.Time `json:"timeStamp" gorm:"type:timestamp;comment:时间戳"`
}

func (TOrderBillLog) TableName() string {
    return "t_order_bill_log"
}

func (e *TOrderBillLog) GetId() interface{} {
	return e.OrderId
}