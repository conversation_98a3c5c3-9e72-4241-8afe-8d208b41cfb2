package apis

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"

	"go-admin/app/payment/models"
	"go-admin/app/payment/service"
	"go-admin/app/payment/service/dto"
	"go-admin/common/actions"
)

type TOrderBillLog struct {
	api.Api
}

// GetPage 获取TOrderBillLog列表
// @Summary 获取TOrderBillLog列表
// @Description 获取TOrderBillLog列表
// @Tags TOrderBillLog
// @Param orderId query string false "订单号"
// @Param uid query string false "玩家ID"
// @Param payChannel query string false "支付渠道"
// @Param orderStatus query string false "订单状态"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TOrderBillLog}} "{"code": 200, "data": [...]}"
// @Router /api/v1/payment-bill [get]
// @Security Bearer
func (e TOrderBillLog) GetPage(c *gin.Context) {
    req := dto.TOrderBillLogGetPageReq{}
    s := service.TOrderBillLog{}
    err := e.MakeContext(c).
        MakeOrm().
        Bind(&req).
        MakeService(&s.Service).
        Errors
   	if err != nil {
   		e.Logger.Error(err)
   		e.Error(500, err, err.Error())
   		return
   	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TOrderBillLog, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TOrderBillLog 失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TOrderBillLog
// @Summary 获取TOrderBillLog
// @Description 获取TOrderBillLog
// @Tags TOrderBillLog
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TOrderBillLog} "{"code": 200, "data": [...]}"
// @Router /api/v1/payment-bill/{id} [get]
// @Security Bearer
func (e TOrderBillLog) Get(c *gin.Context) {
	req := dto.TOrderBillLogGetReq{}
	s := service.TOrderBillLog{}
    err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TOrderBillLog

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TOrderBillLog失败，\r\n失败信息 %s", err.Error()))
        return
	}

	e.OK( object, "查询成功")
}