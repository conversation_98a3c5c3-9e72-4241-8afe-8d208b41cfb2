package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/payment/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTOrderBillLogRouter)
}

// registerTOrderBillLogRouter
func registerTOrderBillLogRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.TOrderBillLog{}
	r := v1.Group("/payment-bill").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.GET("/:id", actions.PermissionAction(), api.Get)
	}
}
