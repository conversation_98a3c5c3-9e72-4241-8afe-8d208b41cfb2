package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/payment/apis"
	"go-admin/common/middleware"
	jwt "go-admin/core/sdk/pkg/jwtauth"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerPaymentRouter)
}

// 需认证的路由代码
func registerPaymentRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.Payment{}
	r := v1.Group("/list").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", api.GetPage)
	}
}
