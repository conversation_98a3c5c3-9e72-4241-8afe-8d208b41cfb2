package service

import (
	"errors"

	"go-admin/core/sdk/service"
	"gorm.io/gorm"

	"go-admin/app/mail/models"
	"go-admin/app/mail/service/dto"
	"go-admin/common/actions"
	cDto "go-admin/common/dto"
)

type TMailMaster struct {
	service.Service
}

// GetPage 获取TMailMaster列表
func (e *TMailMaster) GetPage(c *dto.TMailMasterGetPageReq, p *actions.DataPermission, list *[]models.TMailMaster, count *int64) error {
	var err error
	var data models.TMailMaster

	err = e.Orm.Model(&data).
		Scopes(
			cDto.MakeCondition(c.GetNeedSearch()),
			cDto.Paginate(c.GetPageSize(), c.GetPageIndex()),
			actions.Permission(data.TableName(), p),
		).Order("created_at desc").
		Find(list).Limit(-1).Offset(-1).
		Count(count).Error
	if err != nil {
		e.Log.Errorf("TMailMasterService GetPage error:%s \r\n", err)
		return err
	}
	return nil
}

// Get 获取TMailMaster对象
func (e *TMailMaster) Get(d *dto.TMailMasterGetReq, p *actions.DataPermission, model *models.TMailMaster) error {
	var data models.TMailMaster

	err := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).
		First(model, d.GetId()).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		e.Log.Errorf("Service GetTMailMaster error:%s \r\n", err)
		return err
	}
	if err != nil {
		e.Log.Errorf("db error:%s", err)
		return err
	}
	return nil
}

// Insert 创建TMailMaster对象
func (e *TMailMaster) Insert(c *dto.TMailMasterInsertReq) error {
	var err error
	var data models.TMailMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMailMasterService Insert error:%s \r\n", err)
		return err
	}

	return nil
}

// Insert2 创建TMailMaster对象
func (e *TMailMaster) Insert2(c *dto.TMailMasterInsertReq) (error, int) {
	var err error
	var data models.TMailMaster
	c.Generate(&data)
	err = e.Orm.Create(&data).Error
	if err != nil {
		e.Log.Errorf("TMailMasterService Insert error:%s \r\n", err)
		return err, 0
	}

	return nil, data.Id
}

// Update 修改TMailMaster对象
func (e *TMailMaster) Update(c *dto.TMailMasterUpdateReq, p *actions.DataPermission) error {
	var err error
	var data = models.TMailMaster{}
	e.Orm.Scopes(
		actions.Permission(data.TableName(), p),
	).First(&data, c.GetId())
	c.Generate(&data)

	db := e.Orm.Save(&data)
	if db.Error != nil {
		e.Log.Errorf("TMailMasterService Save error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

// Remove 删除TMailMaster
func (e *TMailMaster) Remove(d *dto.TMailMasterDeleteReq, p *actions.DataPermission) error {
	var data models.TMailMaster

	db := e.Orm.Model(&data).
		Scopes(
			actions.Permission(data.TableName(), p),
		).Delete(&data, d.GetId())
	if err := db.Error; err != nil {
		e.Log.Errorf("Service RemoveTMailMaster error:%s \r\n", err)
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("无权删除该数据")
	}
	return nil
}
