package dto

import (
	"time"

	"go-admin/app/mail/models"
	"go-admin/common/dto"
	common "go-admin/common/models"
)

type TMailMasterGetPageReq struct {
	dto.Pagination    `search:"-"`
	PlanetId          int64  `form:"planetId"  search:"type:exact;column:planet_id;table:t_mail_master" comment:"planet"`
	MailTitle         string `form:"mailTitle"  search:"type:exact;column:mail_title;table:t_mail_master" comment:"标题"`
	MailDetail        string `form:"mailDetail"  search:"type:exact;column:mail_detail;table:t_mail_master" comment:"内容"`
	MailRewardJson    string `form:"mailRewardJson"  search:"type:exact;column:mail_reward_json;table:t_mail_master" comment:"奖励Json"`
	MailUserTag       string `form:"mailUserTag"  search:"type:exact;column:mail_user_tag;table:t_mail_master" comment:"标签"`
	MailDest          string `form:"mailDest"  search:"type:contains;column:mail_dest;table:t_mail_master" comment:"目标UID数组Json"`
	MailType          int64  `form:"mailType"  search:"type:exact;column:mail_type;table:t_mail_master" comment:"邮件类型：1-系统 2-普通 3-补偿"`
	MailActive        int64  `form:"mailActive"  search:"type:exact;column:mail_active;table:t_mail_master" comment:"操作：1-只读 2-可领奖 3-问卷调查"`
	MailOperateSource int64  `form:"mailOperateSource"  search:"type:exact;column:mail_operate_source;table:t_mail_master" comment:"创建来源：1-表单 2-批量导入"`
	TMailMasterOrder
}

type TMailMasterOrder struct {
	Id                int       `form:"idOrder"  search:"type:order;column:id;table:t_mail_master"`
	PlanetId          int64     `form:"planetIdOrder"  search:"type:order;column:planet_id;table:t_mail_master"`
	MailTitle         string    `form:"mailTitleOrder"  search:"type:order;column:mail_title;table:t_mail_master"`
	MailDetail        string    `form:"mailDetailOrder"  search:"type:order;column:mail_detail;table:t_mail_master"`
	MailRewardJson    string    `form:"mailRewardJsonOrder"  search:"type:order;column:mail_reward_json;table:t_mail_master"`
	MailUserTag       string    `form:"mailUserTagOrder"  search:"type:order;column:mail_user_tag;table:t_mail_master"`
	MailDest          string    `form:"mailDestOrder"  search:"type:order;column:mail_dest;table:t_mail_master"`
	MailType          int64     `form:"mailTypeOrder"  search:"type:order;column:mail_type;table:t_mail_master"`
	MailCreatedAt     time.Time `form:"mailCreatedAtOrder"  search:"type:order;column:mail_created_at;table:t_mail_master"`
	MailEndAt         time.Time `form:"mailEndAtOrder"  search:"type:order;column:mail_end_at;table:t_mail_master"`
	MailActive        int64     `form:"mailActiveOrder"  search:"type:order;column:mail_active;table:t_mail_master"`
	MailActiveParam   string    `form:"mailActiveParamOrder"  search:"type:order;column:mail_active_param;table:t_mail_master"`
	MailOperateSource int64     `form:"mailOperateSourceOrder"  search:"type:order;column:mail_operate_source;table:t_mail_master"`
	MailRemark        string    `form:"mailRemarkOrder"  search:"type:order;column:mail_remark;table:t_mail_master"`
	CreateBy          string    `form:"createByOrder"  search:"type:order;column:create_by;table:t_mail_master"`
	UpdateBy          string    `form:"updateByOrder"  search:"type:order;column:update_by;table:t_mail_master"`
	CreatedAt         time.Time `form:"createdAtOrder"  search:"type:order;column:created_at;table:t_mail_master"`
	UpdatedAt         time.Time `form:"updatedAtOrder"  search:"type:order;column:updated_at;table:t_mail_master"`
	DeletedAt         time.Time `form:"deletedAtOrder"  search:"type:order;column:deleted_at;table:t_mail_master"`
}

func (m *TMailMasterGetPageReq) GetNeedSearch() interface{} {
	return *m
}

type TMailMasterInsertReq struct {
	Id                int       `json:"-" comment:""` //
	PlanetId          int64     `json:"planetId" comment:"planet"`
	MailTitle         string    `json:"mailTitle" comment:"标题"`
	MailDetail        string    `json:"mailDetail" comment:"内容"`
	MailRewardJson    string    `json:"mailRewardJson" comment:"奖励Json"`
	MailUserTag       string    `json:"mailUserTag" comment:"标签"`
	MailDest          string    `json:"mailDest" comment:"目标UID数组Json"`
	MailType          int64     `json:"mailType" comment:"邮件类型：1-系统 2-普通 3-补偿"`
	MailCreatedAt     time.Time `json:"mailCreatedAt" comment:"邮件创建时间"`
	MailEndAt         time.Time `json:"mailEndAt" comment:"邮件截至时间"`
	MailActive        int64     `json:"mailActive" comment:"操作：1-只读 2-可领奖 3-问卷调查"`
	MailActiveParam   string    `json:"mailActiveParam" comment:"操作附带参数"`
	MailOperateSource int64     `json:"mailOperateSource" comment:"创建来源：1-表单 2-批量导入"`
	MailRemark        string    `json:"mailRemark" comment:"备注"`
	common.ControlBy
}

// TMailContextItem RPCGame服结构
type TMailContextItem struct {
	Title   string `json:"title" comment:"title"`
	Lang    string `json:"lang" comment:"lang"`
	Context string `json:"context" comment:"context"`
}

// TMailPropItem RPCGame 页面传递奖励结构
type TMailPropItem struct {
	PropType string `json:"PropType" comment:"PropType"`
	PropID   string `json:"propId" comment:"propId"`
	Value    int64  `json:"DeltaCount" comment:"DeltaCount"`
}

func (s *TMailMasterInsertReq) Generate(model *models.TMailMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.MailTitle = s.MailTitle
	model.MailDetail = s.MailDetail
	model.MailRewardJson = s.MailRewardJson
	model.MailUserTag = s.MailUserTag
	model.MailDest = s.MailDest
	model.MailType = s.MailType
	model.MailCreatedAt = s.MailCreatedAt
	model.MailEndAt = s.MailEndAt
	model.MailActive = s.MailActive
	model.MailActiveParam = s.MailActiveParam
	model.MailOperateSource = s.MailOperateSource
	model.MailRemark = s.MailRemark
	model.CreateBy = s.CreateBy // 添加这而，需要记录是被谁创建的
}

func (s *TMailMasterInsertReq) GetId() interface{} {
	return s.Id
}

type TMailMasterUpdateReq struct {
	Id                int       `uri:"id" comment:""` //
	PlanetId          int64     `json:"planetId" comment:"planet"`
	MailTitle         string    `json:"mailTitle" comment:"标题"`
	MailDetail        string    `json:"mailDetail" comment:"内容"`
	MailRewardJson    string    `json:"mailRewardJson" comment:"奖励Json"`
	MailUserTag       string    `json:"mailUserTag" comment:"标签"`
	MailDest          string    `json:"mailDest" comment:"目标UID数组Json"`
	MailType          int64     `json:"mailType" comment:"邮件类型：1-系统 2-普通 3-补偿"`
	MailCreatedAt     time.Time `json:"mailCreatedAt" comment:"邮件创建时间"`
	MailEndAt         time.Time `json:"mailEndAt" comment:"邮件截至时间"`
	MailActive        int64     `json:"mailActive" comment:"操作：1-只读 2-可领奖 3-问卷调查"`
	MailActiveParam   string    `json:"mailActiveParam" comment:"操作附带参数"`
	MailOperateSource int64     `json:"mailOperateSource" comment:"创建来源：1-表单 2-批量导入"`
	MailRemark        string    `json:"mailRemark" comment:"备注"`
	common.ControlBy
}

func (s *TMailMasterUpdateReq) Generate(model *models.TMailMaster) {
	if s.Id == 0 {
		model.Model = common.Model{Id: s.Id}
	}
	model.PlanetId = s.PlanetId
	model.MailTitle = s.MailTitle
	model.MailDetail = s.MailDetail
	model.MailRewardJson = s.MailRewardJson
	model.MailUserTag = s.MailUserTag
	model.MailDest = s.MailDest
	model.MailType = s.MailType
	model.MailCreatedAt = s.MailCreatedAt
	model.MailEndAt = s.MailEndAt
	model.MailActive = s.MailActive
	model.MailActiveParam = s.MailActiveParam
	model.MailOperateSource = s.MailOperateSource
	model.MailRemark = s.MailRemark
	model.UpdateBy = s.UpdateBy // 添加这而，需要记录是被谁更新的
}

func (s *TMailMasterUpdateReq) GetId() interface{} {
	return s.Id
}

// TMailMasterGetReq 功能获取请求参数
type TMailMasterGetReq struct {
	Id int `uri:"id"`
}

func (s *TMailMasterGetReq) GetId() interface{} {
	return s.Id
}

// TMailMasterDeleteReq 功能删除请求参数
type TMailMasterDeleteReq struct {
	Ids []int `json:"ids"`
}

func (s *TMailMasterDeleteReq) GetId() interface{} {
	return s.Ids
}

type MailMessage struct {
	Title   string `json:"title" comment:"标题"`
	Content string `json:"content" comment:"内容"`
	Lang    int32  `json:"lang" comment:"语言"`
}

type GMProp struct {
	PropType int32 `json:"prop_type" comment:"道具类型"`
	PropID   int64 `json:"prop_id" comment:"PropID"`
	Value    int64 `json:"value" comment:"Value"`
}

// TMailRpcReq RPCGame服结构
type TMailRpcReq struct {
	ProductId int `json:"product_id" comment:"productId"`
	ChannelId int `json:"channel_id" comment:"channelId"`

	GMProp      []GMProp      `json:"gm_prop" comment:"GMProp"`
	MailMessage []MailMessage `json:"mail_message" comment:"邮件内容"`

	StartTime int64  `json:"start_time" comment:"开始时间"`
	EndTime   int64  `json:"end_time" comment:"结束时间"`
	Param     string `json:"param" comment:"Param"`
	Uids      string `json:"uids" comment:"UID"`

	MailType int32  `json:"mail_type" comment:"邮件类型"`
	Label    string `json:"label" comment:"Label"`
	MailID   int64  `json:"mail_id" comment:"MailID"`
}

// TMailDeleteRpcReq RPCGame服删除邮件结构
type TMailDeleteRpcReq struct {
	MailID int64 `json:"MailID" comment:"MailID"`
}
