package apis

import (
	"encoding/json"
	"fmt"
	"go-admin/common/rpc"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/pkg/jwtauth/user"
	"go-admin/core/sdk/pkg/planet"
	_ "go-admin/core/sdk/pkg/response"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"

	"go-admin/app/mail/models"
	"go-admin/app/mail/service"
	"go-admin/app/mail/service/dto"
	"go-admin/common/actions"
)

type TMailMaster struct {
	api.Api
}

// GetPage 获取TMailMaster列表
// @Summary 获取TMailMaster列表
// @Description 获取TMailMaster列表
// @Tags TMailMaster
// @Param planetId query int64 false "planet"
// @Param mailTitle query string false "标题"
// @Param mailDetail query string false "内容"
// @Param mailRewardJson query string false "奖励Json"
// @Param mailUserTag query string false "标签"
// @Param mailDest query string false "目标UID数组Json"
// @Param mailType query int64 false "邮件类型：1-系统 2-普通 3-补偿"
// @Param mailActive query int64 false "操作：1-只读 2-可领奖 3-问卷调查"
// @Param mailOperateSource query int64 false "创建来源：1-表单 2-批量导入"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response{data=response.Page{list=[]models.TMailMaster}} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-mail-master [get]
// @Security Bearer
func (e TMailMaster) GetPage(c *gin.Context) {
	req := dto.TMailMasterGetPageReq{}
	s := service.TMailMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	p := actions.GetPermissionFromContext(c)
	list := make([]models.TMailMaster, 0)
	var count int64

	err = s.GetPage(&req, p, &list, &count)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TMailMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

// Get 获取TMailMaster
// @Summary 获取TMailMaster
// @Description 获取TMailMaster
// @Tags TMailMaster
// @Param id path string false "id"
// @Success 200 {object} response.Response{data=models.TMailMaster} "{"code": 200, "data": [...]}"
// @Router /api/v1/t-mail-master/{id} [get]
// @Security Bearer
func (e TMailMaster) Get(c *gin.Context) {
	req := dto.TMailMasterGetReq{}
	s := service.TMailMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	var object models.TMailMaster

	p := actions.GetPermissionFromContext(c)
	err = s.Get(&req, p, &object)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("获取TMailMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	e.OK(object, "查询成功")
}

// Insert 创建TMailMaster
// @Summary 创建TMailMaster
// @Description 创建TMailMaster
// @Tags TMailMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TMailMasterInsertReq true "data"
// @Success 200 {object} response.Response	"{"code": 200, "message": "添加成功"}"
// @Router /api/v1/t-mail-master [post]
// @Security Bearer
func (e TMailMaster) Insert(c *gin.Context) {
	req := dto.TMailMasterInsertReq{}
	s := service.TMailMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	// 设置创建人
	req.SetCreateBy(user.GetUserId(c))

	//req.MailCreatedAt = time.Now()

	p := actions.GetPermissionFromContext(c)

	errInsert, Id := s.Insert2(&req)
	if errInsert != nil {
		e.Error(500, errInsert, fmt.Sprintf("创建TMailMaster  失败，\r\n失败信息 %s", errInsert.Error()))
		return
	}

	rpcReq := &dto.TMailRpcReq{}
	rpcReq.StartTime = req.MailCreatedAt.Unix()
	rpcReq.EndTime = req.MailEndAt.Unix()
	rpcReq.MailType = int32(req.MailType)
	rpcReq.Uids = req.MailDest
	rpcReq.Label = req.MailUserTag
	rpcReq.Param = req.MailActiveParam
	rpcReq.MailID = int64(Id)
	planetInfo, ok := planet.GetPlanetInfo(c)
	if !ok {
		err := fmt.Errorf("plantInfo not found:%v", planet.GetPlanetID(c))
		e.Error(500, err, err.Error())
		return

	}
	rpcReq.ProductId = cast.ToInt(planetInfo.ProductId)

	// 内容
	contextParse := make([]dto.TMailContextItem, 0)
	if errShall1 := json.Unmarshal([]byte(req.MailDetail), &contextParse); err != nil {
		e.Logger.Error(errShall1)
		e.Error(500, errShall1, errShall1.Error())
		return
	}

	for _, item := range contextParse {
		lang, _ := strconv.Atoi(item.Lang)
		rpcReq.MailMessage = append(rpcReq.MailMessage, dto.MailMessage{
			Title:   item.Title,
			Content: item.Context,
			Lang:    int32(lang),
		})
	}

	// 奖励
	rewardParse := make([]dto.TMailPropItem, 0)
	if errShall2 := json.Unmarshal([]byte(req.MailRewardJson), &rewardParse); err != nil {
		e.Logger.Error(errShall2)
		e.Error(500, errShall2, errShall2.Error())
		return
	}

	for _, item := range rewardParse {
		pType, _ := strconv.Atoi(item.PropType)
		// propId 转化为int64 
		pId, _ := strconv.ParseInt(item.PropID, 10, 64)

		rpcReq.GMProp = append(rpcReq.GMProp, dto.GMProp{
			PropType: int32(pType),
			PropID:   pId,
			Value:    item.Value,
		})
	}

	rsp, err := rpc.RpcGm(c, 1601, rpcReq)
	if err != nil {
		e.Logger.Error(err)
		deleteBean := dto.TMailMasterDeleteReq{}
		deleteBean.Ids = append(deleteBean.Ids, Id)
		errUpdate := s.Remove(&deleteBean, p)
		if errUpdate != nil {
			e.Error(500, errUpdate, fmt.Sprintf("操作失败，\r\n失败信息 %s", errUpdate.Error()))
			return
		}

		e.Error(500, err, "操作失败:RPC失败，已删除平台邮件记录")

		return
	}

	if rsp != "" {
		e.Logger.Info(rsp)
	}

	e.OK(req.GetId(), "创建成功")
}

// Update 修改TMailMaster
// @Summary 修改TMailMaster
// @Description 修改TMailMaster
// @Tags TMailMaster
// @Accept application/json
// @Product application/json
// @Param data body dto.TMailMasterUpdateReq true "body"
// @Success 200 {object} response.Response	"{"code": 200, "message": "修改成功"}"
// @Router /api/v1/t-mail-master/{id} [put]
// @Security Bearer
func (e TMailMaster) Update(c *gin.Context) {
	req := dto.TMailMasterUpdateReq{}
	s := service.TMailMaster{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	req.SetUpdateBy(user.GetUserId(c))
	p := actions.GetPermissionFromContext(c)

	err = s.Update(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("修改TMailMaster 失败，\r\n失败信息 %s", err.Error()))
		return
	}
	e.OK(req.GetId(), "修改成功")
}

// Delete 删除TMailMaster
// @Summary 删除TMailMaster
// @Description 删除TMailMaster
// @Tags TMailMaster
// @Param ids body []int false "ids"
// @Success 200 {object} response.Response	"{"code": 200, "message": "删除成功"}"
// @Router /api/v1/t-mail-master [delete]
// @Security Bearer
func (e TMailMaster) Delete(c *gin.Context) {
	s := service.TMailMaster{}
	req := dto.TMailMasterDeleteReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// req.SetUpdateBy(user.GetUserId(c))

	rpcReq := &dto.TMailDeleteRpcReq{}
	rpcReq.MailID = int64(req.Ids[0])

	js, err := json.Marshal(rpcReq)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	_, rsp, errRpc := rpc.RpcGm2GameJson("4000113", "", "platform", string(js), c)
	if errRpc != nil {
		e.Logger.Error(errRpc)
		//e.Error(500, errRpc, errRpc.Error())
		//return
	}

	if rsp != "" {
		e.Logger.Info(rsp)
	}

	result := gjson.ParseBytes([]byte(rsp))
	code := result.Get("Code").Int()

	p := actions.GetPermissionFromContext(c)
	err = s.Remove(&req, p)
	if err != nil {
		e.Error(500, err, fmt.Sprintf("删除TMailMaster失败，\r\n失败信息 %s", err.Error()))
		return
	}

	if code == 20007 {
		e.OK(req.GetId(), "游戏数据不存在，仅删除平台数据成功")
	} else {
		e.OK(req.GetId(), "撤销并且删除成功")
	}
}
