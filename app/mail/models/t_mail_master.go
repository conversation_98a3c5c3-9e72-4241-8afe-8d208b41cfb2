package models

import (
	"time"

	"go-admin/common/models"
)

type TMailMaster struct {
	models.Model

	Id                int       `json:"id" gorm:"type:bigint;comment:id"`
	PlanetId          int64     `json:"planetId" gorm:"type:bigint;comment:planet"`
	MailTitle         string    `json:"mailTitle" gorm:"type:varchar(150);comment:标题"`
	MailDetail        string    `json:"mailDetail" gorm:"type:text;comment:内容"`
	MailRewardJson    string    `json:"mailRewardJson" gorm:"type:varchar(256);comment:奖励Json"`
	MailUserTag       string    `json:"mailUserTag" gorm:"type:varchar(128);comment:标签"`
	MailDest          string    `json:"mailDest" gorm:"type:varchar(256);comment:目标UID数组Json"`
	MailType          int64     `json:"mailType" gorm:"type:int;comment:邮件类型：1-系统 2-普通 3-补偿"`
	MailCreatedAt     time.Time `json:"mailCreatedAt" gorm:"type:datetime(3);comment:邮件创建时间"`
	MailEndAt         time.Time `json:"mailEndAt" gorm:"type:datetime(3);comment:邮件截至时间"`
	MailActive        int64     `json:"mailActive" gorm:"type:int;comment:操作：1-只读 2-可领奖 3-问卷调查"`
	MailActiveParam   string    `json:"mailActiveParam" gorm:"type:varchar(255);comment:操作附带参数"`
	MailOperateSource int64     `json:"mailOperateSource" gorm:"type:int;comment:创建来源：1-表单 2-批量导入"`
	MailRemark        string    `json:"mailRemark" gorm:"type:varchar(255);comment:备注"`
	models.ModelTime
	models.ControlBy
}

func (TMailMaster) TableName() string {
	return "t_mail_master"
}

func (e *TMailMaster) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *TMailMaster) GetId() interface{} {
	return e.Id
}
