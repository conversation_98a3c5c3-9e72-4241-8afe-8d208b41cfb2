package router

import (
	"github.com/gin-gonic/gin"
	jwt "go-admin/core/sdk/pkg/jwtauth"

	"go-admin/app/operate/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerTOperateMasterRouter)
}

// registerTOperateMasterRouter
func registerTOperateMasterRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.OperateMaster{}
	r := v1.Group("/msg_ann_popup").Use(authMiddleware.MiddlewareFunc()).Use(middleware.PlanetCheckMid())
	{
		r.GET("", actions.PermissionAction(), api.GetPage)
		r.POST("", api.Edit)
		r.DELETE("", api.Delete)
	}
}
