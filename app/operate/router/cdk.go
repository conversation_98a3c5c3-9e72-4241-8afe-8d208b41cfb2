package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/operate/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
	jwt "go-admin/core/sdk/pkg/jwtauth"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerCDKRouter)
}

// registerCDKRouter CDK相关路由注册
func registerCDKRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.CDKController{}
	r := v1.Group("/cdk").Use(authMiddleware.MiddlewareFunc()).Use(middleware.PlanetCheckMid())
	{
		// CDK批次管理
		r.POST("/batch", actions.PermissionAction(), api.CreateCDKBatch)         // 创建CDK批次
		r.GET("/batches", actions.PermissionAction(), api.QueryCDKBatches)       // 查询CDK批次列表
		r.PUT("/batch/disable", actions.PermissionAction(), api.DisableCDKBatch) // 作废CDK批次

		// CDK记录查看
		r.GET("/records", actions.PermissionAction(), api.QueryCDKRecords) // 查询CDK记录
	}
}
