package router

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/operate/apis"
	"go-admin/common/actions"
	"go-admin/common/middleware"
	jwt "go-admin/core/sdk/pkg/jwtauth"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerS3ImageRouter)
}

// registerS3ImageRouter S3图片管理相关路由注册
func registerS3ImageRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.S3ImageController{}
	r := v1.Group("/s3/image").Use(authMiddleware.MiddlewareFunc()).Use(middleware.PlanetCheckMid())
	{
		// 图片上传
		r.POST("/upload", actions.PermissionAction(), api.UploadImage)

		// 图片删除
		r.DELETE("/delete", actions.PermissionAction(), api.DeleteImage)

		// 图片列表
		r.GET("/list", actions.PermissionAction(), api.ListImages)
	}
}
