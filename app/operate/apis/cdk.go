package apis

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/operate/models"
	"go-admin/app/operate/service"
	"go-admin/app/operate/service/dto"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"
)

type CDKController struct {
	api.Api
}

// CreateCDKBatch 创建CDK批次和对应的CDK码
func (e CDKController) CreateCDKBatch(c *gin.Context) {
	s := service.CDKService{}
	req := dto.CreateCDKBatchReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("CDK create batch req:%+v", req)
	if req.CdkLimit == 0 {
		e.Error(500, err, "不能为0")
		return
	}

	batch, err := s.CreateCDKBatch(c, &req)
	if err != nil {
		e.Error(500, err, "创建CDK批次失败")
		return
	}

	e.OK(batch, "创建CDK批次成功")
}

// QueryCDKBatches CDK批次查询接口（分页）
func (e CDKController) QueryCDKBatches(c *gin.Context) {
	s := service.CDKService{}
	req := dto.QueryCDKBatchesReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("CDK query batches req:%+v", req)

	list := make([]*models.CDKBatch, 0)
	var count int64

	list, count, err = s.QueryCDKBatches(c, &req)
	if err != nil {
		e.Error(500, err, "查询CDK批次失败")
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询CDK批次成功")
}

// QueryCDKRecords CDK记录查看接口
func (e CDKController) QueryCDKRecords(c *gin.Context) {
	s := service.CDKService{}
	req := dto.QueryCDKRecordsReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("CDK query records req:%+v", req)

	list := make([]*models.CDKRecord, 0)
	var count int64

	list, count, err = s.QueryCDKRecords(c, &req)
	if err != nil {
		e.Error(500, err, "查询CDK记录失败")
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询CDK记录成功")
}

// DisableCDKBatch CDK批次作废接口
func (e CDKController) DisableCDKBatch(c *gin.Context) {
	s := service.CDKService{}
	req := dto.DisableCDKBatchReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("CDK disable batch req:%+v", req)

	err = s.DisableCDKBatch(c, &req)
	if err != nil {
		e.Error(500, err, "作废CDK批次失败")
		return
	}

	e.OK(req.BatchID, "作废CDK批次成功")
}
