package apis

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/operate/models"
	"go-admin/app/operate/service"
	"go-admin/app/operate/service/dto"
	"go-admin/core/sdk/api"
	_ "go-admin/core/sdk/pkg/response"
)

type OperateMaster struct {
	api.Api
}

func (e OperateMaster) GetPage(c *gin.Context) {
	s := service.OperateMaster{}
	req := dto.AnnGetPageReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("ann query req:%+v", req)

	list := make([]*models.AnnPopup, 0)
	var count int64

	list, count, err = s.GetPage(c, &req)
	if err != nil {
		e.<PERSON><PERSON>r(500, err, "查询失败")
		return
	}

	e.Page<PERSON>(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}

func (e OperateMaster) Edit(c *gin.Context) {
	s := service.OperateMaster{}
	req := models.AnnPopup{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("ann edit req:%+v", req)

	err = s.Edit(c, &req)
	if err != nil {
		e.Error(500, err, "修改失败")
		return
	}
	e.OK(req.Id, "修改成功")

}

func (e OperateMaster) Delete(c *gin.Context) {
	s := service.OperateMaster{}
	req := dto.AnnRemoveReq{}
	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}
	e.Logger.Infof("ann delete req:%+v", req)

	err = s.Remove(c, req.Id)
	if err != nil {
		e.Error(500, err, "删除失败")
		return
	}
	e.OK(req.Id, "删除成功")
}
