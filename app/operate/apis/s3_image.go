package apis

import (
	"github.com/gin-gonic/gin"
	"go-admin/app/operate/service"
	"go-admin/app/operate/service/dto"
	"go-admin/core/sdk/api"
	"mime/multipart"
)

type S3ImageController struct {
	api.Api
}

// UploadImage 上传图片
func (e S3ImageController) UploadImage(c *gin.Context) {
	s := service.S3ImageService{}
	req := dto.UploadImageReq{}

	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		e.Logger.Error(err)
		e.Error(400, err, "请选择要上传的文件")
		return
	}

	// 类型检查
	var fileHeader *multipart.FileHeader = file

	e.Logger.Infof("图片上传请求: folder=%s, fileName=%s, fileSize=%d",
		req.Folder, req.FileName, file.Size)

	result, err := s.UploadImage(c, &req, fileHeader)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, "上传失败")
		return
	}

	e.OK(result, "上传成功")
}

// DeleteImage 删除图片
func (e S3ImageController) DeleteImage(c *gin.Context) {
	s := service.S3ImageService{}
	req := dto.DeleteImageReq{}

	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	e.Logger.Infof("图片删除请求: key=%s", req.Key)

	err = s.DeleteImage(c, &req)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, "删除失败")
		return
	}

	e.OK(req.Key, "删除成功")
}

// ListImages 列出图片
func (e S3ImageController) ListImages(c *gin.Context) {
	s := service.S3ImageService{}
	req := dto.ListImagesReq{}

	err := e.MakeContext(c).
		MakeOrm().
		Bind(&req).
		MakeService(&s.Service).
		Errors
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, err.Error())
		return
	}

	e.Logger.Infof("图片列表请求: folder=%s, pageIndex=%d, pageSize=%d",
		req.Folder, req.GetPageIndex(), req.GetPageSize())

	list, count, err := s.ListImages(c, &req)
	if err != nil {
		e.Logger.Error(err)
		e.Error(500, err, "查询失败")
		return
	}

	e.PageOK(list, int(count), req.GetPageIndex(), req.GetPageSize(), "查询成功")
}
