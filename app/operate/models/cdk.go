package models

import (
	"encoding/json"
	"github.com/sirupsen/logrus"
)

// CDKBatch CDK批次
type CDKBatch struct {
	Id          uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                         // 批次ID
	ChannelId   int32  `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道ID
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`                                        // 批次描述
	CdkCount    int32  `protobuf:"varint,4,opt,name=cdk_count,json=cdkCount,proto3" json:"cdk_count,omitempty"`                             // CDK数量
	CdkLimit    int32  `protobuf:"varint,5,opt,name=cdk_limit,json=cdkLimit,proto3" json:"cdk_limit,omitempty"`                             // 单个CDK可被所有用户使用的总次数 (-1表示不限制，默认为0)
	Status      int32  `protobuf:"varint,6,opt,name=status,proto3,enum=common.CDK_BATCH_STATUS" json:"status,omitempty"`                    // 状态
	StartTime   int64  `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                          // 生效开始时间 (Unix时间戳)
	EndTime     int64  `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                                // 生效结束时间 (Unix时间戳)
	CreatedAt   int64  `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                          // 创建时间 (Unix时间戳)
	Rewards     string `protobuf:"bytes,10,opt,name=rewards,proto3" json:"rewards,omitempty"`                                               // 奖励信息
}

// CDKRecord CDK记录
type CDKRecord struct {
	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                // 记录ID
	Cdk       string `protobuf:"bytes,2,opt,name=cdk,proto3" json:"cdk,omitempty"`                               // CDK码
	UsedCount int32  `protobuf:"varint,3,opt,name=used_count,json=usedCount,proto3" json:"used_count,omitempty"` // 使用次数
	CreatedAt int64  `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 创建时间 (Unix时间戳)
	UpdatedAt int64  `protobuf:"varint,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间 (Unix时间戳)
}

// 使用已存在的ApiResponse结构

// CDKBatchData GM返回的CDK批次数据
type CDKBatchData struct {
	Batches []*CDKBatch `json:"batches"`
	Total   int64       `json:"total"`
}

// CDKRecordData GM返回的CDK记录数据
type CDKRecordData struct {
	Records []*CDKRecord `json:"records"`
	Total   int64        `json:"total"`
}

// ParseCDKBatchResponse 解析CDK批次查询响应
func ParseCDKBatchResponse(jsonData string) ([]*CDKBatch, int64, error) {
	logrus.Infof("ParseCDKBatchResponse: %s", jsonData)
	var apiResp ApiResponse
	if err := json.Unmarshal([]byte(jsonData), &apiResp); err != nil {
		return nil, 0, err
	}

	var batchData CDKBatchData
	if err := json.Unmarshal([]byte(apiResp.Data.Data), &batchData); err != nil {
		return nil, 0, err
	}

	return batchData.Batches, batchData.Total, nil
}

// ParseCDKRecordResponse 解析CDK记录查询响应
func ParseCDKRecordResponse(jsonData string) ([]*CDKRecord, int64, error) {
	logrus.Infof("ParseCDKRecordResponse: %s", jsonData)
	var apiResp ApiResponse
	if err := json.Unmarshal([]byte(jsonData), &apiResp); err != nil {
		return nil, 0, err
	}

	var recordData CDKRecordData
	if err := json.Unmarshal([]byte(apiResp.Data.Data), &recordData); err != nil {
		return nil, 0, err
	}

	return recordData.Records, recordData.Total, nil
}
