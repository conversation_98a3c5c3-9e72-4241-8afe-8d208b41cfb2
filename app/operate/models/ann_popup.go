package models

import (
	"encoding/json"
)

// AnnPopup 弹窗公告配置
type AnnPopup struct {
	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`                                // 唯一标识（自增主键）
	Priority  int32 `protobuf:"varint,2,opt,name=priority,proto3" json:"priority"`                    // 优先级（数值越小越优先）
	ChannelId int32 `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id"` // 渠道ID
	PopStyle  int32 `protobuf:"varint,4,opt,name=pop_style,json=popStyle,proto3" json:"pop_style"`    // 弹窗样式（1=有关闭按钮）
	// 文本内容配置（JSON格式）
	AnnContent string `protobuf:"bytes,5,opt,name=ann_content,json=annContent,proto3" json:"ann_content"`
	// 用户行为配置（JSON格式）
	AnnAction string `protobuf:"bytes,6,opt,name=ann_action,json=annAction,proto3" json:"ann_action"`
	// 展示条件配置（JSON格式）
	AnnConditions string `protobuf:"bytes,7,opt,name=ann_conditions,json=annConditions,proto3" json:"ann_conditions"`
	Enable        bool   `protobuf:"varint,8,opt,name=enable,proto3" json:"enable"`                        // 是否启用（默认true）
	StartTime     int64  `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time"` // 生效开始时间
	EndTime       int64  `protobuf:"varint,10,opt,name=end_time,json=endTime,proto3" json:"end_time"`      // 生效结束时间
}

// 实际GM数据
type PopupData struct {
	PopupInfo []*AnnPopup `json:"popup_info"`
}

func ParseGetResponse(jsonData string) ([]*AnnPopup, error) {
	var apiResp ApiResponse
	if err := json.Unmarshal([]byte(jsonData), &apiResp); err != nil {
		return nil, err
	}

	// 解析第三层数据
	var popupData PopupData
	if err := json.Unmarshal([]byte(apiResp.Data.Data), &popupData); err != nil {
		return nil, err
	}

	return popupData.PopupInfo, nil
}
