package service

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go-admin/app/operate/service/dto"
	"go-admin/core/sdk/pkg/awssdk"
	"go-admin/core/sdk/service"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"
)

type S3ImageService struct {
	service.Service
}

// UploadImage 上传图片到S3
func (e *S3ImageService) UploadImage(c *gin.Context, req *dto.UploadImageReq, file *multipart.FileHeader) (*dto.UploadImageRes, error) {
	// 验证文件类型
	if !isValidImageType(file.Filename) {
		return nil, fmt.Errorf("不支持的图片格式，仅支持: jpg, jpeg, png, gif, webp")
	}

	// 验证文件大小 (10MB限制)
	if file.Size > 10*1024*1024 {
		return nil, fmt.Errorf("文件大小不能超过10MB")
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		e.Log.Errorf("打开文件失败: %v", err)
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer src.Close()

	// 读取文件内容
	fileData, err := io.ReadAll(src)
	if err != nil {
		e.Log.Errorf("读取文件失败: %v", err)
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	// 生成文件名
	fileName := req.FileName
	if fileName == "" {
		ext := filepath.Ext(file.Filename)
		fileName = fmt.Sprintf("%s%s", uuid.New().String(), ext)
	} else {
		// 确保自定义文件名有正确的扩展名
		if !strings.Contains(fileName, ".") {
			ext := filepath.Ext(file.Filename)
			fileName = fileName + ext
		}
	}

	// 构建S3 key
	key := fmt.Sprintf("image/%s/%s", req.Folder, fileName)

	// 获取内容类型
	contentType := getContentType(file.Filename)

	// 初始化S3配置
	cfg, err := awssdk.LoadS3Cfg()
	if err != nil {
		e.Log.Errorf("加载S3配置失败: %v", err)
		return nil, fmt.Errorf("S3配置失败: %w", err)
	}

	// 创建S3客户端检查文件是否存在
	s3Client := s3.NewFromConfig(cfg)

	// 检查文件是否已存在
	exists, err := awssdk.FileExists(context.Background(), s3Client, key)
	if err != nil {
		e.Log.Warnf("检查文件是否存在时出错: %v", err)
	} else if exists {
		e.Log.Infof("文件 %s 已存在，将被覆盖", key)
	}

	// 创建上传器
	uploader := manager.NewUploader(s3Client)

	// 上传文件
	err = awssdk.UploadImage(context.Background(), uploader, fileData, key, contentType)
	if err != nil {
		e.Log.Errorf("上传文件到S3失败: %v", err)
		return nil, fmt.Errorf("上传失败: %w", err)
	}

	// 构建返回数据
	url := fmt.Sprintf("s3://clientsrv/%s", key)

	return &dto.UploadImageRes{
		Key:      key,
		URL:      url,
		FileName: fileName,
		Size:     file.Size,
	}, nil
}

// DeleteImage 从S3删除图片
func (e *S3ImageService) DeleteImage(c *gin.Context, req *dto.DeleteImageReq) error {
	// 验证key格式
	if !strings.HasPrefix(req.Key, "image/") {
		return fmt.Errorf("无效的文件key，必须以'image/'开头")
	}

	// 初始化S3配置
	cfg, err := awssdk.LoadS3Cfg()
	if err != nil {
		e.Log.Errorf("加载S3配置失败: %v", err)
		return fmt.Errorf("S3配置失败: %w", err)
	}

	// 创建S3客户端
	client := s3.NewFromConfig(cfg)

	// 删除文件
	err = awssdk.DeleteFile(context.Background(), client, req.Key)
	if err != nil {
		e.Log.Errorf("从S3删除文件失败: %v", err)
		return fmt.Errorf("删除失败: %w", err)
	}

	e.Log.Infof("成功删除文件: %s", req.Key)
	return nil
}

// ListImages 列出图片文件
func (e *S3ImageService) ListImages(c *gin.Context, req *dto.ListImagesReq) ([]*dto.ImageInfo, int64, error) {
	// 构建前缀
	prefix := "image/"
	if req.Folder != "" {
		prefix = fmt.Sprintf("image/%s/", req.Folder)
	}

	// 初始化S3配置
	cfg, err := awssdk.LoadS3Cfg()
	if err != nil {
		e.Log.Errorf("加载S3配置失败: %v", err)
		return nil, 0, fmt.Errorf("S3配置失败: %w", err)
	}

	// 创建S3客户端
	client := s3.NewFromConfig(cfg)

	// 列出文件
	maxKeys := int32(req.GetPageSize())
	if maxKeys == 0 {
		maxKeys = 20 // 默认页大小
	}

	files, err := awssdk.ListFiles(context.Background(), client, prefix, maxKeys)
	if err != nil {
		e.Log.Errorf("列出S3文件失败: %v", err)
		return nil, 0, fmt.Errorf("列出文件失败: %w", err)
	}

	// 转换为返回格式
	var images []*dto.ImageInfo
	for _, file := range files {
		// 只返回图片文件
		if isValidImageType(file.Key) {
			fileName := filepath.Base(file.Key)
			url := fmt.Sprintf("https://d28ns0slwt4j0f.cloudfront.net/%s", file.Key)

			images = append(images, &dto.ImageInfo{
				Key:          file.Key,
				URL:          url,
				Size:         file.Size,
				LastModified: file.LastModified.Unix(),
				FileName:     fileName,
			})
		}
	}

	return images, int64(len(images)), nil
}

// isValidImageType 验证是否为有效的图片类型
func isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}

// getContentType 根据文件扩展名获取内容类型
func getContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}
