package service

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go-admin/app/operate/models"
	"go-admin/app/operate/service/dto"
	"go-admin/common/rpc"
	"go-admin/core/sdk/service"
)

type CDKService struct {
	service.Service
}

// CreateCDKBatch 创建CDK批次和对应的CDK码
func (e *CDKService) CreateCDKBatch(c *gin.Context, req *dto.CreateCDKBatchReq) (*models.CDKBatch, error) {
	// 构建请求参数
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["description"] = req.Description
	reqx["start_time"] = req.StartTime
	reqx["end_time"] = req.EndTime
	reqx["rewards"] = req.Rewards
	reqx["generation_option"] = req.GenerationOption
	reqx["generation_count"] = req.GenerationCount
	reqx["manual_cdks"] = req.ManualCDKs
	reqx["cdk_limit"] = req.CdkLimit

	// 调用GM接口创建CDK批次
	rsp, err := rpc.RpcGm(c, 1103, reqx)
	if err != nil {
		e.Log.Error(err)
		return nil, err
	}

	var apiResp models.ApiResponse
	if err = json.Unmarshal([]byte(rsp), &apiResp); err != nil {
		return nil, err
	}

	if apiResp.Code != 200 {
		e.Log.Errorf("create CDK batch failed: code=%d, rsp=%+v", apiResp.Code, apiResp)
		return nil, errors.New("创建CDK批次失败")
	}

	// 解析返回的批次信息
	var batch models.CDKBatch
	if err = json.Unmarshal([]byte(apiResp.Data.Data), &batch); err != nil {
		return nil, err
	}

	return &batch, nil
}

// QueryCDKBatches CDK批次查询接口（分页）
func (e *CDKService) QueryCDKBatches(c *gin.Context, req *dto.QueryCDKBatchesReq) ([]*models.CDKBatch, int64, error) {
	// 构建请求参数
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["pagination"] = map[string]interface{}{
		"page_index": req.GetPageIndex(),
		"page_size":  req.GetPageSize(),
	}
	reqx["status"] = req.Status
	e.Log.Debugf("reqx:%+v", reqx)

	// 调用GM接口查询CDK批次
	rsp, err := rpc.RpcGm(c, 1104, reqx)
	if err != nil {
		e.Log.Error(err)
		return nil, 0, err
	}

	batches, total, err := models.ParseCDKBatchResponse(rsp)
	if err != nil {
		e.Log.Error(err)
		return nil, 0, err
	}

	return batches, total, nil
}

// QueryCDKRecords CDK记录查看接口
func (e *CDKService) QueryCDKRecords(c *gin.Context, req *dto.QueryCDKRecordsReq) ([]*models.CDKRecord, int64, error) {
	// 构建请求参数
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["batch_id"] = req.BatchID
	reqx["pagination"] = map[string]interface{}{
		"page_index": req.GetPageIndex(),
		"page_size":  req.GetPageSize(),
	}

	// 调用GM接口查询CDK记录
	rsp, err := rpc.RpcGm(c, 1105, reqx)
	if err != nil {
		e.Log.Error(err)
		return nil, 0, err
	}

	records, total, err := models.ParseCDKRecordResponse(rsp)
	if err != nil {
		e.Log.Error(err)
		return nil, 0, err
	}

	return records, total, nil
}

// DisableCDKBatch CDK批次作废接口
func (e *CDKService) DisableCDKBatch(c *gin.Context, req *dto.DisableCDKBatchReq) error {
	// 构建请求参数
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["batch_id"] = req.BatchID

	// 调用GM接口作废CDK批次
	rsp, err := rpc.RpcGm(c, 1106, reqx)
	if err != nil {
		e.Log.Error(err)
		return err
	}

	var apiResp models.ApiResponse
	if err = json.Unmarshal([]byte(rsp), &apiResp); err != nil {
		return err
	}

	if apiResp.Code != 200 {
		e.Log.Errorf("disable CDK batch failed: code=%d, rsp=%+v", apiResp.Code, apiResp)
		return errors.New("作废CDK批次失败")
	}

	return nil
}
