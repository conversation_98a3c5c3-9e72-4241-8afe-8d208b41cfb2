package service

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go-admin/common/rpc"

	"go-admin/core/sdk/service"

	"go-admin/app/operate/models"
	"go-admin/app/operate/service/dto"
)

type OperateMaster struct {
	service.Service
}

func (e *OperateMaster) GetPage(c *gin.Context, d *dto.AnnGetPageReq) (list []*models.AnnPopup, count int64, err error) {
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["enable"] = d.Enable
	reqx["id"] = d.Id

	rsp, err := rpc.RpcGm(c, 1611, reqx)
	if err != nil {
		e.Log.Error(err)
		return
	}

	list, err = models.ParseGetResponse(rsp)
	if err != nil {
		e.Log.Error(err)
		return
	}
	count = int64(len(list))
	return
}

func (e *OperateMaster) Edit(c *gin.Context, req *models.AnnPopup) error {

	rsp, err := rpc.RpcGm(c, 1612, req)
	if err != nil {
		return err
	}

	var apiResp models.ApiResponse
	if err = json.Unmarshal([]byte(rsp), &apiResp); err != nil {
		return err
	}

	if apiResp.Code != 200 {
		e.Log.Errorf("ann edit failed: code=%d,rsp=%+v", apiResp.Code, apiResp)
		return errors.New("ann edit failed")
	}

	return nil
}

func (e *OperateMaster) Remove(c *gin.Context, popupId int32) error {
	reqx := make(map[string]interface{})
	reqx["channel_id"] = cast.ToInt(c.GetString("planetID"))
	reqx["id"] = popupId

	rsp, err := rpc.RpcGm(c, 1613, reqx)
	if err != nil {
		return err
	}

	var apiResp models.ApiResponse
	if err = json.Unmarshal([]byte(rsp), &apiResp); err != nil {
		return err
	}

	if apiResp.Code != 200 {
		e.Log.Errorf("ann remove failed: code=%d,rsp=%+v", apiResp.Code, apiResp)
		return errors.New("ann remove failed")
	}
	return nil
}
