package dto

import "go-admin/common/dto"

// UploadImageReq 上传图片请求
type UploadImageReq struct {
	Folder   string `form:"folder" binding:"required" comment:"文件夹名称"`
	FileName string `form:"file_name" comment:"自定义文件名（可选）"`
}

// DeleteImageReq 删除图片请求
type DeleteImageReq struct {
	Key string `form:"key" binding:"required" comment:"S3文件key"`
}

// ListImagesReq 列出图片请求
type ListImagesReq struct {
	dto.Pagination
	Folder string `form:"folder" comment:"文件夹名称"`
}

// ImageInfo 图片信息
type ImageInfo struct {
	Key          string `json:"key"`
	URL          string `json:"url"`
	Size         int64  `json:"size"`
	LastModified int64  `json:"last_modified"`
	FileName     string `json:"file_name"`
}

// UploadImageRes 上传图片响应
type UploadImageRes struct {
	Key      string `json:"key"`
	URL      string `json:"url"`
	FileName string `json:"file_name"`
	Size     int64  `json:"size"`
}
