package service

import (
	"testing"
)

func TestIsValidImageType(t *testing.T) {
	tests := []struct {
		filename string
		expected bool
	}{
		{"image.jpg", true},
		{"image.jpeg", true},
		{"image.png", true},
		{"image.gif", true},
		{"image.webp", true},
		{"image.JPG", true},  // 大写扩展名
		{"image.txt", false}, // 非图片格式
		{"image.pdf", false}, // 非图片格式
		{"image", false},     // 无扩展名
		{"", false},          // 空文件名
	}

	for _, test := range tests {
		t.Run(test.filename, func(t *testing.T) {
			result := isValidImageType(test.filename)
			if result != test.expected {
				t.<PERSON>rrorf("isValidImageType(%s) = %v, expected %v", test.filename, result, test.expected)
			}
		})
	}
}

func TestGetContentType(t *testing.T) {
	tests := []struct {
		filename string
		expected string
	}{
		{"image.jpg", "image/jpeg"},
		{"image.jpeg", "image/jpeg"},
		{"image.png", "image/png"},
		{"image.gif", "image/gif"},
		{"image.webp", "image/webp"},
		{"image.JPG", "image/jpeg"},               // 大写扩展名
		{"image.txt", "application/octet-stream"}, // 非图片格式
		{"image", "application/octet-stream"},     // 无扩展名
	}

	for _, test := range tests {
		t.Run(test.filename, func(t *testing.T) {
			result := getContentType(test.filename)
			if result != test.expected {
				t.Errorf("getContentType(%s) = %s, expected %s", test.filename, result, test.expected)
			}
		})
	}
}
