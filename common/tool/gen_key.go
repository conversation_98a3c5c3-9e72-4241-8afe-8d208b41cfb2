package tool

import (
	"fmt"
	"github.com/gin-gonic/gin"
)

func GetPlanetSQLKey(planetID string, name string) string {
	return fmt.Sprintf("planet_%s_%s_mysql", planetID, name)
}

func GetPlanetZkKey(planetID string, name string) string {
	return fmt.Sprintf("planet_%s_%s_zk", planetID, name)
}

func GetPlanetIDFromCtx(c *gin.Context) string {
	planetID, _ := c.Get("planetID")
	return planetID.(string)
}
