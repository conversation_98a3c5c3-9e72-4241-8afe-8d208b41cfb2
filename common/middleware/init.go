package middleware

import (
	"github.com/gin-gonic/gin"
	"go-admin/common/actions"
	"go-admin/core/sdk"
	jwt "go-admin/core/sdk/pkg/jwtauth"
)

const (
	JwtTokenCheck   string = "JwtToken"
	ParseTokenStr   string = "ParseTokenStr"
	RoleCheck       string = "AuthCheckRole"
	PermissionCheck string = "PermissionAction"
	PlanetCheck     string = "PlanetCheck" //环境选择
)

func InitMiddleware(r *gin.Engine) {
	// 数据库链接
	r.Use(WithContextDb)
	// 日志处理
	//r.Use(LoggerToFile()) // TODO 操作日志记录暂时去掉
	// 自定义错误处理
	r.Use(CustomError)
	// NoCache is a middleware function that appends headers
	r.Use(NoCache)
	// 跨域处理
	r.Use(Options)
	// Secure is a middleware function that appends security
	r.Use(Secure)
	//r.Use(DemoEvn())
	// 链路追踪
	//r.Use(middleware.Trace())
	sdk.Runtime.SetMiddleware(JwtTokenCheck, (*jwt.GinJWTMiddleware).MiddlewareFunc)

	sdk.Runtime.SetMiddleware(RoleCheck, AuthCheckRole())
	sdk.Runtime.SetMiddleware(PermissionCheck, actions.PermissionAction())
	sdk.Runtime.SetMiddleware(PlanetCheck, PlanetCheckMid)
}
