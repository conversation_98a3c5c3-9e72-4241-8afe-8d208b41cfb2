package handler

import (
	"fmt"
	"go-admin/app/admin/models"
	"go-admin/app/admin/service/dto"
	"go-admin/common"
	"go-admin/core/sdk/pkg/captcha"
	"go-admin/core/sdk/pkg/fssdk"
	"golang.org/x/xerrors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/mssola/user_agent"
	gaConfig "go-admin/config"
	"go-admin/core/sdk"
	"go-admin/core/sdk/api"
	"go-admin/core/sdk/config"
	"go-admin/core/sdk/pkg"
	jwt "go-admin/core/sdk/pkg/jwtauth"
	"go-admin/core/sdk/pkg/jwtauth/user"
	"go-admin/core/sdk/pkg/response"

	"go-admin/common/global"
)

func GetRedirectType(data interface{}) int32 {
	v, ok := data.(map[string]interface{})
	if !ok {
		return 0
	}

	rType, ok := v["rType"].(int32)
	if !ok {
		return 0
	}
	if rType == 0 {
		return 0
	}

	return rType
}

func PayloadFunc(data interface{}) jwt.MapClaims {
	if v, ok := data.(map[string]interface{}); ok {
		u, _ := v["user"].(SysUser)
		r, _ := v["role"].(SysRole)
		return jwt.MapClaims{
			jwt.IdentityKey:  u.UserId,
			jwt.RoleIdKey:    r.RoleId,
			jwt.RoleKey:      r.RoleKey,
			jwt.NiceKey:      u.Username,
			jwt.NickName:     u.NickName,
			jwt.DataScopeKey: r.DataScope,
			jwt.RoleNameKey:  r.RoleName,
			jwt.AvatarKey:    u.Avatar,
		}
	}
	return jwt.MapClaims{}
}

func IdentityHandler(c *gin.Context) interface{} {
	claims := jwt.ExtractClaims(c)
	return map[string]interface{}{
		"IdentityKey": claims["identity"],
		"UserName":    claims["nice"],
		"RoleKey":     claims["rolekey"],
		"UserId":      claims["identity"],
		"RoleIds":     claims["roleid"],
		"DataScope":   claims["datascope"],
	}
}

// Authenticator 获取token
// @Summary 登陆
// @Description 获取token
// @Description LoginHandler can be used by clients to get a jwt token.
// @Description Payload needs to be json in the form of {"username": "USERNAME", "password": "PASSWORD"}.
// @Description Reply will be of the form {"token": "TOKEN"}.
// @Description dev mode：It should be noted that all fields cannot be empty, and a value of 0 can be passed in addition to the account password
// @Description 注意：开发模式：需要注意全部字段不能为空，账号密码外可以传入0值
// @Tags 登陆
// @Accept  application/json
// @Product application/json
// @Param account body Login  true "account"
// @Success 200 {string} string "{"code": 200, "expire": "2019-08-07T12:45:48+08:00", "token": ".***********************************************************************.-zvzHvbg0A" }"
// @Router /api/v1/login [post]
func Authenticator(c *gin.Context) (interface{}, error) {
	log := api.GetRequestLogger(c)
	db, err := pkg.GetOrm(c)
	if err != nil {
		log.Errorf("get db error, %s", err.Error())
		response.Error(c, 500, err, "数据库连接获取失败")
		return nil, jwt.ErrFailedAuthentication
	}
	data := make(map[string]interface{}, 0)

	log.Trace("Authenticator。。。")

	var loginVals Login
	var status = "2"
	var msg = "登录成功"
	var username = ""
	var nickname = ""
	defer func() {
		LoginLogToDB(c, status, msg, username, nickname)
	}()

	if err = c.ShouldBind(&loginVals); err != nil {
		username = loginVals.Username
		msg = "数据解析失败"
		status = "1"
		log.Errorf("数据解析失败 : err %v", err)

		return nil, jwt.ErrMissingLoginValues
	}
	data["rType"] = loginVals.RType
	log.Tracef("Authenticator ：%s - %s", loginVals.LoginType, loginVals.Code)

	var fsLoginRsp *fssdk.UserLoginInfoRsp
	if loginVals.LoginType == LoginTypeFs {
		userInfo, errUserGet := fssdk.GetUserInfo(loginVals.Code, loginVals.RedirectUri)
		if errUserGet != nil {
			log.Errorf("get userinfo err %v : uri:%s", errUserGet, loginVals.RedirectUri)
			return data, jwt.ErrFailedFeiShuLogin
		}
		log.Trace(userInfo)

		//飞书登录Userid设置为OpenId
		loginVals.Username = userInfo.OpenId
		fsLoginRsp = userInfo

		user, role, isExist := loginVals.LoadUser(db)
		if isExist {
			username = loginVals.Username
			nickname = user.NickName
			data["user"] = user
			data["role"] = role
			return data, nil
		} else {
			if loginVals.RType != RTypeNone {
				return data, xerrors.New("register first")
			}
			nickname = fsLoginRsp.Name

			//注册
			req := dto.SysUserInsertReq{
				UserId:   0,
				Username: fsLoginRsp.OpenId,
				Password: "sparkle",
				NickName: fsLoginRsp.Name,
				Phone:    fsLoginRsp.Mobile,
				RoleId:   2, //默认给新人角色
				Avatar:   fsLoginRsp.AvatarUrl,
				//Sex:       "",
				Email: fsLoginRsp.Email,
				//DeptId:    0,
				//PostId:    0,
				Remark: "",
				Status: "2",
			}
			errRegister := loginVals.UserRegister(db, &req)
			if errRegister != nil {
				log.Errorf("Fs register error %v", errRegister)
				return nil, jwt.ErrFailedFeiShuLogin
			}

			userRet, roleRet, retExist := loginVals.LoadUser(db)
			if retExist {
				data["user"] = userRet
				data["role"] = roleRet
				return data, nil
			}
			msg = "登录失败"
			status = "1"
			log.Warnf("%s login failed!", loginVals.Username)
		}
	} else {
		//账号密码登录需要验证码
		if config.ApplicationConfig.Mode != "dev" {
			if !captcha.Verify(loginVals.UUID, loginVals.Code, true) {
				username = loginVals.Username
				msg = "验证码错误"
				status = "1"

				return data, jwt.ErrInvalidVerificationode
			}
		}

		user, role, e := loginVals.GetUser(db)
		if e == nil {
			username = loginVals.Username
			nickname = user.NickName
			data["user"] = user
			data["role"] = role
			return data, nil
		} else {
			msg = "登录失败"
			status = "1"
			log.Warnf("%s login failed!", loginVals.Username)
		}
	}

	return data, jwt.ErrFailedAuthentication
}

// LoginLogToDB Write log to database
func LoginLogToDB(c *gin.Context, status string, msg string, username string, nickname string) {
	if !config.LoggerConfig.EnabledDB {
		return
	}
	log := api.GetRequestLogger(c)
	l := make(map[string]interface{})

	ua := user_agent.New(c.Request.UserAgent())
	l["ipaddr"] = common.GetClientIP(c)
	fmt.Println("gaConfig.ExtConfig.AMap.Key", gaConfig.ExtConfig.AMap.Key)
	//l["loginLocation"] = pkg.GetLocation(common.GetClientIP(c), gaConfig.ExtConfig.AMap.Key)
	l["loginLocation"] = ""
	l["loginTime"] = pkg.GetCurrentTime()
	l["status"] = status
	l["remark"] = c.Request.UserAgent()
	browserName, browserVersion := ua.Browser()
	l["browser"] = browserName + " " + browserVersion
	l["os"] = ua.OS()
	l["platform"] = ua.Platform()
	l["username"] = username
	l["nickname"] = nickname
	l["msg"] = msg

	q := sdk.Runtime.GetMemoryQueue(c.Request.Host)
	message, err := sdk.Runtime.GetStreamMessage("", global.LoginLog, l)
	if err != nil {
		log.Errorf("GetStreamMessage error, %s", err.Error())
		//日志报错错误，不中断请求
	} else {
		err = q.Append(message)
		if err != nil {
			log.Errorf("Append message error, %s", err.Error())
		}
	}
}

// LogOut
// @Summary 退出登录
// @Description 获取token
// LoginHandler can be used by clients to get a jwt token.
// Reply will be of the form {"token": "TOKEN"}.
// @Accept  application/json
// @Product application/json
// @Success 200 {string} string "{"code": 200, "msg": "成功退出系统" }"
// @Router /logout [post]
// @Security Bearer
func LogOut(c *gin.Context) {
	LoginLogToDB(c, "2", "退出成功", user.GetUserName(c), user.GetUserNick(c))
	c.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "退出成功",
	})

}

func Authorizator(data interface{}, c *gin.Context) bool {

	if v, ok := data.(map[string]interface{}); ok {
		u, _ := v["user"].(models.SysUser)
		r, _ := v["role"].(models.SysRole)
		c.Set("role", r.RoleName)
		c.Set("roleIds", r.RoleId)
		c.Set("userId", u.UserId)
		c.Set("userName", u.Username)
		c.Set("dataScope", r.DataScope)
		return true
	}
	return false
}

func Unauthorized(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, gin.H{
		"code": code,
		"msg":  message,
	})
}
