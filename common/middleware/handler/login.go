package handler

import (
	"go-admin/app/admin/service"
	"go-admin/app/admin/service/dto"
	log "go-admin/core/logger"
	"go-admin/core/sdk/pkg"
	"gorm.io/gorm"
)

const (
	LoginTypeAdmin = "admin" //系统管理员登录
	LoginTypeFs    = "fs"    //飞书登录
	LoginTypeOutside = "outside"    //外部登录
	RTypeNone      = 0
	RTypeFS        = 1 //飞书重定向登入
)

type Login struct {
	Username    string `form:"UserName" json:"username" binding:"required"`
	Password    string `form:"Password" json:"password" binding:"required"`
	Code        string `form:"Code" json:"code" binding:"required"`
	RedirectUri string `form:"RedirectUri" json:"redirect_uri"`
	UUID        string `form:"UUID" json:"uuid"`
	LoginType   string `form:"LoginType" json:"loginType" binding:"required"`
	RType       int32  `form:"RType" json:"rType"` //从定向类型 0 没有从定向， 1 配置中心重定向过来
}

func (u *Login) GetUser(tx *gorm.DB) (user SysUser, role SysRole, err error) {
	err = tx.Table("sys_user").Where("username = ?  and status = 2", u.Username).First(&user).Error
	if err != nil {
		log.Errorf("get user error, %s", err.Error())
		return
	}
	_, err = pkg.CompareHashAndPassword(user.Password, u.Password)
	if err != nil {
		log.Errorf("user login error, %s", err.Error())
		return
	}
	err = tx.Table("sys_role").Where("role_id = ? ", user.RoleId).First(&role).Error
	if err != nil {
		log.Errorf("get role error, %s", err.Error())
		return
	}
	return
}

func (u *Login) LoadUser(tx *gorm.DB) (user SysUser, role SysRole, isExist bool) {
	err := tx.Table("sys_user").Where("username = ?  and status = 2", u.Username).First(&user).Error
	if err != nil {
		//log.Errorf("get user error, %s", err.Error())
		isExist = false
		return
	}

	err3 := tx.Table("sys_role").Where("role_id = ? ", user.RoleId).First(&role).Error
	if err3 != nil {
		//log.Errorf("get role error, %s", err.Error())
		//isExist = false
		return
	}
	isExist = true
	return
}

func (u *Login) UserRegister(tx *gorm.DB, req *dto.SysUserInsertReq) error {
	s := service.SysUser{}
	s.Orm = tx

	// 设置创建人
	//req.SetCreateBy("")
	err := s.Insert(req)
	if err != nil {
		return err
	}
	return nil
}
