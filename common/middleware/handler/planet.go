package handler

import "gorm.io/gorm"

type PlanetMid struct {
	ID          int    `json:"id"`
	Name        string `json:"name" gorm:"column:name"`
	ZhName      string `json:"zhName" gorm:"column:zh_name"`
	EnvIDG      int    `json:"env_idg" gorm:"column:env_idg"`
	EnvName     string `json:"envName" gorm:"column:env_name"`
	ZhEvn       string `json:"zhEnv" gorm:"column:zh_env"`
	ProjectName string `json:"projectName" gorm:"column:project_name"`
	ZhProject   string `json:"zhProject" gorm:"column:zh_project"`
	GMAddr      string `json:"gmAddr" gorm:"column:gm_addr"`
	ZkAddr      string `json:"zkAddr" gorm:"column:zk_addr"`
	CkAddr      string `json:"ckAddr" gorm:"column:ck_addr"`
	GameSqlAddr string `json:"gameSqlAddr" gorm:"column:game_sql_addr"`
	ProductId   int    `json:"product_id" gorm:"column:product_id"`
}

func (p *PlanetMid) TableName() string {
	return "t_planet"
}

func (p *PlanetMid) GetInfo(orm *gorm.DB, envID int) error {
	// tx := orm.Table("t_planet tp").Select("tp.id as id, tp.name as name, tp.describe as zh_name, tp.gm_addr, tp.zk_addr, tp.ck_addr, tp.game_sql_addr," +
	// 	"te.name as env_name, te.zh_name as zh_env, te.id as env_idg," +
	// 	"tpj.name as project_name, tpj.describe as zh_project").
	// 	Joins("left join t_env te on tp.env = te.id").
	// 	Joins("left join t_project tpj on tp.id = tpj.id")
	// tx.Where("tp.id  = ?", p.ID)

	tx := orm.Table("t_project prj, t_planet tp").
		Joins("left join t_env te on tp.planet_id = te.planet_id").
		Select("prj.id as id, "+
			"prj.product_id as product_id,"+
			"prj.name as name,"+
			"tp.name as planet_name,"+
			"prj.`describe` as description,"+
			"tp.alias_name,"+
			"tp.planet_id as planet_id,"+
			"tp.name as planet_name,"+
			"tp.`describe` as planet_describe,"+
			"te.env_type,"+
			"te.name as env_name,"+
			"te.gm_addr, "+
			"te.zh_name  as env_zh_name,"+
			"te.ck_addr").
		Where("tp.product_id = prj.product_id").
		Where("prj.status = 2 and tp.status = 2 ").
		Where("tp.planet_id  = ?", p.ID).
		Where("te.env_type = ?", envID)

	return tx.Scan(p).Error
}
