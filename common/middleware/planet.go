package middleware

import (
	"fmt"
	"github.com/spf13/cast"
	"go-admin/common/database"
	"go-admin/common/middleware/handler"
	"go-admin/common/tool"
	"go-admin/core/sdk"
	toolsConfig "go-admin/core/sdk/config"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func PlanetCheckMid() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否有 planetID 如果有则把 planet 的 zk gm 地址 放到header中
		planetIDStr := c.GetHeader("planet")
		envID := cast.ToInt(c.<PERSON>eader("env"))
		if planetIDStr == "" || envID == 0 {
			c.J<PERSON>(http.StatusOK, gin.H{
				"code": 500,
				"msg":  "请先选择环境-平台!!",
			})
			c.Abort()
			return
		}
		planetID, err := strconv.Atoi(planetIDStr)
		if err != nil {
			c.<PERSON>(http.StatusOK, gin.H{
				"code": 500,
				"msg":  "错误的平台ID!!",
			})
			c.Abort()
			return
		}
		c.Set("planetID", planetIDStr)
		c.Set("envID", envID)

		// orm := sdk.Runtime.GetDbByAssignKey(database.ExtDBNameConfigure)
		orm := sdk.Runtime.GetDbByKey(c.Request.Host)
		m := &handler.PlanetMid{ID: planetID}
		if err := m.GetInfo(orm, envID); err != nil {
			c.JSON(http.StatusOK, gin.H{
				"code": 500,
				"msg":  "请先选择环境!!",
			})
			c.Abort()
			return
		}
		c.Set("pGmAddr", m.GMAddr)
		c.Set("productID", m.ProductId)
		c.Set("planetInfo", m)

		if m.GameSqlAddr != "" {
			gameDBKey := tool.GetPlanetSQLKey(planetIDStr, database.ExtDBNameGame)
			if sdk.Runtime.GetDbByAssignKey(gameDBKey) == nil {
				cfg := &toolsConfig.Database{Name: database.ExtDBNameGame, Source: m.GameSqlAddr, Driver: "mysql"}
				if err := database.SetupExtDatabase(gameDBKey, cfg); err != nil {
					c.JSON(http.StatusOK, gin.H{
						"code": 500,
						"msg":  fmt.Sprintf("%s  数据库连接失败", database.ExtDBNameGame),
					})
					c.Abort()
					return
				}
			}
		}

		if m.CkAddr != "" {
			ckDBKey := tool.GetPlanetSQLKey(planetIDStr, database.ExtDBNameClickHouse)
			if sdk.Runtime.GetDbByAssignKey(ckDBKey) == nil {
				cfg := &toolsConfig.Database{Name: database.ExtDBNameClickHouse, Source: m.CkAddr, Driver: "clickhouse"}
				if err := database.SetupExtDatabase(ckDBKey, cfg); err != nil {
					c.JSON(http.StatusOK, gin.H{
						"code": 500,
						"msg":  fmt.Sprintf("%s 数据库连接失败", database.ExtDBNameClickHouse),
					})
					c.Abort()
					return
				}
			}
		}

		// if m.ZkAddr != "" {
		//	zkDBKey := tool.GetPlanetZkKey(planetIDStr, database.ExtDBNameZk)
		//	if sdk.Runtime.GetZookeeper(database.ExtDBNameZk) == nil {
		//		zkCfg := &toolsConfig.Zookeeper{}
		//		err := json.Unmarshal([]byte(m.ZkAddr), zkCfg)
		//		if err != nil {
		//			c.JSON(http.StatusOK, gin.H{
		//				"code": 500,
		//				"msg":  fmt.Sprintf("%s zk配置错误", database.ExtDBNameGame),
		//			})
		//			c.Abort()
		//			return
		//		}
		//
		//		if err := zookeeper.SetUpZK(zkDBKey, zkCfg); err != nil {
		//			c.JSON(http.StatusOK, gin.H{
		//				"code": 500,
		//				"msg":  fmt.Sprintf("%s zooker 连接失败", database.ExtDBNameGame),
		//			})
		//			c.Abort()
		//			return
		//		}
		//	}
		// }

		c.Next()
	}
}
