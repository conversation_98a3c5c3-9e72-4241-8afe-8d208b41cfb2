package zookeeper

import (
	log "go-admin/core/logger"
	"go-admin/core/sdk"
	"go-admin/core/sdk/config"
	"go-admin/core/sdk/pkg"
	"go-admin/core/sdk/pkg/zookeeper"
	"time"
)

//SetUpZK 初始化 zookeeper
func SetUpZK(name string, cfg *config.Zookeeper) error {
	c, err := zookeeper.New(time.Millisecond*time.Duration(cfg.Timeout),
		cfg.Addr...)
	if err != nil {
		return err
	} else {
		log.Info(pkg.Green(" connect zookeeper success !"))
	}
	sdk.Runtime.SetZookeeper(name, c)
	return nil
}
