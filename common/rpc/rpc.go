package rpc

import (
	"encoding/json"
	"errors"
	"fmt"
	"go-admin/config"
	log "go-admin/core/logger"
	"go-admin/core/sdk/pkg"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type Req struct {
	GmUid            string   `form:"gmUid" comment:"用户ID"`                   // 用户ID
	GmCmd            string   `form:"gmCmd" comment:"GM名称"`                   // GMCmd
	Code             string   `form:"Code"  comment:"Code"`                     // Code
	StartTimeStamp   int64    `form:"StartTimeStamp"  comment:"StartTimeStamp"` // StartTimeStamp
	EndTimeStamp     int64    `form:"EndTimeStamp"  comment:"EndTimeStamp"`     // EndTimeStamp
	RewardJson       string   `form:"RewardJson"  comment:"RewardJson"`         // RewardJson
	IsDevTester      bool     `form:"IsDevTester"  comment:"IsDevTester"`       // IsDevTester
	IsPayTester      bool     `form:"IsPayTester"  comment:"IsPayTester"`       // IsPayTester
	IsGrayTester     bool     `form:"IsGrayTester"  comment:"IsGrayTester"`     // IsGrayTester
	UID              string   `form:"UID" comment:"用户ID"`                     // UID
	Action           int32    `form:"UID" comment:"Action"`                     // Action
	Param            string   `form:"UID" comment:"Action"`                     // Param
	EnableLog        bool     `form:"EnableLog" comment:"开启日志"`             // EnableLog
	EnableReceiveLog bool     `form:"EnableReceiveLog" comment:"接受日志"`      // EnableReceiveLog
	LogDate          []string `form:"LogDate" comment:"日志时间段"`             // LogDate
}

func RpcGm2Game(req Req, c *gin.Context) (string, string, error) {
	param := make(url.Values)
	param["cmd"] = []string{req.GmCmd}
	param["routeKey"] = []string{req.GmUid}
	param["uid"] = []string{req.GmUid}
	param["Key"] = []string{config.ExtConfig.GetGMKey()}
	jsonInfo, mErr := json.Marshal(req)
	if mErr != nil {
		log.Error("RpcGm2Game Marshal Err: %s", mErr.Error())
	}
	log.Info(jsonInfo)
	param.Set("data", string(jsonInfo))

	gmAddr, _ := c.Get("pGmAddr")
	rsp, err := pkg.PostForm(gmAddr.(string), param)
	if err != nil {
		log.Error(err, rsp)
		log.Error(500, err, fmt.Sprintf(" %s ", err.Error()))
		return string(jsonInfo), "", err
	}

	result := gjson.ParseBytes(rsp)
	if result.Get("Code").Int() != 0 {
		log.Error("gmsrv err code", result.Get("Code").Int())
		log.Error(500, err, fmt.Sprintf("gmsrv err code:%d ", result.Get("Code").Int()))
		return string(jsonInfo), "", errors.New(string(result.Get("Code").Int()))
	}

	return string(jsonInfo), result.String(), nil
}

func RpcGm2GameJson(cmd string, uid string, routeKey string, data string, c *gin.Context) (string, string, error) {
	param := make(url.Values)
	param["cmd"] = []string{cmd}
	param["routeKey"] = []string{routeKey}
	param["uid"] = []string{uid}
	param["Key"] = []string{config.ExtConfig.GetGMKey()}
	param.Set("data", data)

	gmAddr, _ := c.Get("pGmAddr")
	rsp, err := pkg.PostForm(gmAddr.(string), param)
	if err != nil {
		log.Error(err, rsp)
		log.Error(500, err, fmt.Sprintf(" %s ", err.Error()))
		return data, "", err
	}

	result := gjson.ParseBytes(rsp)
	if result.Get("Code").Int() != 0 {
		log.Error("gmsrv err code", result.Get("Code").Int())
		log.Error(500, err, fmt.Sprintf("gmsrv err code:%d ", result.Get("Code").Int()))
		return data, result.String(), errors.New(strconv.FormatInt(result.Get("Code").Int(), 10))
	}

	return data, result.String(), nil
}

// RpcGm 请求gm服
func RpcGm(c *gin.Context, cmd int, data interface{}) (string, error) {

	jsonData := map[string]interface{}{
		"pid":        c.GetInt("productID"),
		"cid":        cast.ToInt(c.GetString("planetID")),
		"cmd":        cmd,
		"data":       data,
		"secret_key": config.ExtConfig.GetGMKey(),
	}

	gmAddr, _ := c.Get("pGmAddr")
	// gmAddr = "http://192.168.1.103:21601/gmapi/cmd"

	rsp, err := pkg.Post(gmAddr.(string), jsonData, "application/json")
	if err != nil {
		log.Errorf("err:%s rsp:%+v", err, rsp)
		return "", err
	}

	rspData := make(map[string]interface{})
	err = json.Unmarshal(rsp, &rspData)
	if err != nil {
		log.Errorf("err:%s rsp:%+v", err, rsp)
		return "", err
	}
	if rspData["code"] != nil {
		code := cast.ToInt(rspData["code"])
		if code != 200 {
			log.Errorf("err:%+v rsp:%s", err, string(rsp))
			return "", fmt.Errorf("gmsrv err code: %d msg: %s", code, rspData["msg"])
		}
	}
	return string(rsp), nil
}
