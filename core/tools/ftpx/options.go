package ftpx

type Options struct {
	Host     string `json:"host"`
	User<PERSON><PERSON> string `json:"username"`
	Password string `json:"password"`
	BaseDir  string `json:"basedir"`
}

type FuncOptions func(*Options)

func newOptions(opt ...FuncOptions) *Options {
	opts := &Options{
		Host:     "************:8080",
		UserName: "root",
		Password: "fancygame2024@",
		BaseDir:  "configs",
	}
	for _, o := range opt {
		o(opts)
	}
	return opts
}

func WithHost(host string) FuncOptions {
	return func(options *Options) {
		options.Host = host
	}
}

func WithUserName(user string) FuncOptions {
	return func(options *Options) {
		options.UserName = user
	}
}

func WithPassword(password string) FuncOptions {
	return func(options *Options) {
		options.Password = password
	}
}

func WithBaseDir(basedir string) FuncOptions {
	return func(options *Options) {
		options.BaseDir = basedir
	}
}
