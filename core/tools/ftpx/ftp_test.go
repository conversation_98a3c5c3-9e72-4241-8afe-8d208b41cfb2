package ftpx

import (
	"bytes"
	"path/filepath"
	"testing"
)

func TestUpload(t *testing.T) {
	localDir := "/Users/<USER>/Documents/mygo/src/wolong/bin/data/"
	baseDir := "configs/"
	ftpServer := &FtpServer{}
	err := ftpServer.Login(
		WithHost("************:21"),
		WithUserName("user00"),
		WithPassword("fancygame8888$"),
		WithBaseDir("configs"))
	if err != nil {
		t.Log(err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.c.CurrentDir()
	if err != nil {
		return
	}

	ftpServer.UploadDir(baseDir, localDir, basePath)
}

func TestUploadByDB(t *testing.T) {

	jsonStr := `{"key1": "value1", "key2": "value2"}`
	fileName := "testPltUp.json"

	baseDir := "configs/test2/"
	ftpServer := &FtpServer{}
	err := ftpServer.Login(
		WithHost("************:21"),
		WithUserName("user00"),
		WithPassword("fancygame8888$"),
		WithBaseDir("configs"))
	if err != nil {
		t.Log(err)
	}
	defer ftpServer.Close()

	basePath, err := ftpServer.GetRemoteBasePath()
	if err != nil {
		return
	}

	if err := ftpServer.EnsureRemoteDirExists(basePath, baseDir); err != nil {
		t.Log(err)
	}

	remotePath := windowsToLinuxPath(filepath.Join(basePath, baseDir, fileName))

	err = ftpServer.c.Stor(remotePath, bytes.NewBufferString(jsonStr))
	if err != nil {
		t.Log(err)
	}
}
