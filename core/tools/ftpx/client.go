package ftpx

import (
	"bytes"
	"fmt"
	"go-admin/core/logger"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jlaffaye/ftp"
)

type FtpServer struct {
	c *ftp.ServerConn
	o *Options
}

func (f *FtpServer) Login(opt ...FuncOptions) error {
	f.o = newOptions(opt...)
	var err error
	f.c, err = ftp.Dial(f.o.Host, ftp.DialWithTimeout(5*time.Second))
	if err != nil {
		panic(err)
	}

	if err := f.c.Login(f.o.UserName, f.o.Password); err != nil {
		panic(err)
	}

	return nil
}

func (f *FtpServer) Close() {
	f.c.Quit()
}

func (f *FtpServer) GetRemoteBasePath() (string, error) {
	basePath, err := f.c.CurrentDir()

	if err != nil {
		return "", err
	}

	return basePath, nil
}

func (f *FtpServer) UploadJsonStr(basePath, baseDir, fileName, jsonStr string) error {
	remotePath := windowsToLinuxPath(filepath.Join(basePath, baseDir, fileName))

	// 检查远程文件是否存在
	_, errFile := f.c.FileSize(remotePath)
	if errFile == nil {
		// 文件存在，删除后再上传
		errFile = f.c.Delete(remotePath)
		if errFile != nil {
			logger.Errorf("deleting remote file %s: %v", remotePath, errFile)
			return fmt.Errorf("deleting remote file %s: %v", remotePath, errFile)
		}
	}

	return f.c.Stor(remotePath, bytes.NewBufferString(jsonStr))
}

func (f *FtpServer) UploadDir(baseDir, localDir, basePath string) error {
	// 获取本地文件夹内容
	infos, errRead := os.ReadDir(localDir)
	if errRead != nil {
		return fmt.Errorf("reading local directory %s: %v", localDir, errRead)
	}

	logger.Infof("UploadDir, localDir:? , remotePath: ? basePath:?", localDir, baseDir, basePath)

	lastRmPath := ""
	for _, info := range infos {
		localPath := filepath.Join(localDir, info.Name())
		remotePath := windowsToLinuxPath(filepath.Join(baseDir, info.Name()))

		if info.IsDir() {
			// 递归创建远程文件夹
			if err := f.createRemoteDir(remotePath, basePath); err != nil {
				return err
			}

			err := f.UploadDir(remotePath, localPath, basePath)
			if err != nil {
				return err
			}
		} else {
			newPath := windowsToLinuxPath(filepath.Dir(remotePath))
			if newPath != lastRmPath {
				err := f.c.ChangeDir(basePath + "/") // 切换到基础目录
				if err != nil {
					logger.Errorf("changing to base directory %s: %v", basePath, err)
					return fmt.Errorf("changing to base directory %s: %w", basePath, err)
				}

				err = f.c.ChangeDir(newPath + "/") // 切换到目标文件夹
				if err != nil {
					logger.Errorf("changing to remote directory %s: %v", basePath, err)
					return fmt.Errorf("changing to remote directory %s: %w", newPath, err)
				}

				lastRmPath = newPath
			}
			// 上传文件
			if err := f.uploadFile(remotePath, localPath); err != nil {
				return err
			}
		}
	}
	return nil
}

// EnsureRemoteDirExists 确保远程目录存在，如果不存在则创建
func (f *FtpServer) EnsureRemoteDirExists(basePath, baseDir string) error {
	// 分割路径中的各个部分
	pathElements := strings.Split(baseDir, "/")

	// 创建一个变量来记录当前正在构建的路径
	currentPath := basePath

	for _, element := range pathElements {
		if element == "" {
			continue // 跳过空字符串，例如路径中的连续斜杠
		}

		// 构建当前路径
		currentPath = filepath.Join(currentPath, element)

		// 检查当前路径是否存在
		exists, err := f.checkDirExists(currentPath)
		if err != nil {
			return fmt.Errorf("checking directory existence: %w", err)
		}

		// 如果目录不存在，则创建它
		if !exists {
			err = f.makeDir2(currentPath)
			if err != nil {
				return fmt.Errorf("creating directory %s: %w", currentPath, err)
			}
		}
	}

	return nil
}

// 创建目录的方法
func (f *FtpServer) makeDir2(path string) error {
	// 尝试创建目录
	err := f.c.MakeDir(path)
	if err != nil {
		return fmt.Errorf("creating directory %s: %w", path, err)
	}
	return nil
}

// 检查目录是否存在
func (f *FtpServer) checkDirExists(path string) (bool, error) {
	// 尝试进入目录
	err := f.c.ChangeDir(path)
	if err != nil {
		if strings.Contains(err.Error(), "550") {
			// 目录不存在
			return false, nil
		}
		// 其他类型的错误
		return false, fmt.Errorf("changing directory: %w", err)
	}
	return true, nil
}

func (f *FtpServer) createRemoteDir(remotePath, basePath string) error {
	// 一层一层创建
	pathElements := strings.Split(basePath, "/")

	for i := 0; i < len(pathElements); i++ {
		if pathElements[i] == "" {
			continue
		}
		path := strings.Join(pathElements[:i+1], "/")
		allPath := filepath.Join(remotePath, path)

		// 检查远程文件是否存在
		size, err := f.c.FileSize(allPath + "/")
		if err != nil {
			return err
		}

		if size > 0 {
			errMake := f.makeDir(path, basePath)
			if errMake != nil {
				return errMake
			}
		}
	}

	return nil
}

func (f *FtpServer) makeDir(remotePath, basePath string) error {
	// 先切换到目标文件夹
	// err := f.c.ChangeDir(basePath + "/")
	err := f.c.ChangeDir(basePath)
	if err != nil {
		return fmt.Errorf("changing to remote directory %s: %w", basePath, err)
	}

	errMk := f.c.MakeDir(remotePath + "/")
	if errMk != nil {
		return fmt.Errorf("making remote directory %s: %w", remotePath, errMk)
	}

	logger.Infof("created remote directory %s success!", basePath+"/"+remotePath)

	return nil
}

func (f *FtpServer) uploadFile(remotePath, localPath string) error {
	file, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("opening local file %s: %v", localPath, err)
	}
	defer file.Close()

	fileName := filepath.Base(remotePath)
	// 检查远程文件是否存在
	_, err = f.c.FileSize(fileName)
	if err == nil {
		// 文件存在，删除后再上传
		err = f.c.Delete(fileName)
		if err != nil {
			logger.Errorf("deleting remote file %s: %v", fileName, err)
			return fmt.Errorf("deleting remote file %s: %v", fileName, err)
		}
	}

	err = f.c.Stor(fileName, file)
	if err != nil {
		logger.Errorf("uploading file %s to %s: %v", localPath, fileName, err)
		return fmt.Errorf("uploading file %s to %s: %v", localPath, fileName, err)
	}

	logger.Infof("uploaded file %s to %s success!", localPath, fileName)

	return nil
}

func windowsToLinuxPath(windowsPath string) string {
	// 处理盘符
	if len(windowsPath) >= 2 && windowsPath[1] == ':' {
		windowsPath = "/" + strings.ToLower(windowsPath[:1]) + windowsPath[2:]
	}

	// 替换分隔符
	linuxPath := filepath.ToSlash(windowsPath)

	return linuxPath
}
