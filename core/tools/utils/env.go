package utils

const (
	UNKNOWN = 0  // 开发环境
	Develop = 1  // 开发环境
	Test    = 2  // 测试环境
	PRE     = 3  // 预发布环境
	Prod    = 4  // release/prod环境
	Audit   = 5  // audit环境
	Review  = 6  // 审核环境
)

func GetEnvName(envType int) string {

	var envName = ""
	switch envType {
	case Develop: 
		envName = "Develop"
	case Test: 
		envName = "Test"
	case PRE: 
		envName = "Pre"
	case Audit: 
		envName = "Audit"
	case Prod: 
		envName = "Prod"
	case Review: 
		envName = "Review"
	case UNKNOWN: 
		envName = "Unknown"
	}
	return envName
}
