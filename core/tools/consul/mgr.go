package consul

import (
	"fmt"
	"go-admin/core/logger"
	"time"

	consulApi "github.com/hashicorp/consul/api"
)

var (
	consulAddress string
	consulPath    string
)

func GetServicesList(consulAddress string) (map[string]*consulApi.AgentService, error) {
	consulClient, err := consulApi.NewClient(&consulApi.Config{Address: consulAddress})
	if err != nil {
		logger.Fatalf("consul连接失败:", err)
	}
	return consulClient.Agent().Services()
}

// SetServicesStatus 设置服务节点灰度状态
func SetServicesStatus(consulAddress string, srvName string, srvIp string, srvPort int) (bool, error) {
	consulClient, err := consulApi.NewClient(&consulApi.Config{Address: consulAddress})
	srvMap, err := consulClient.Agent().ServicesWithFilter(fmt.Sprintf(`Service == "%s"`, srvName))
	if err != nil {
		logger.Fatalf("consul连接失败:", err)
	}

	for _, service := range srvMap {
		if service.Address == srvIp && service.Port == srvPort {
			service.Tags = []string{"gray"}
			interval := time.Duration(10) * time.Second
			deregister := time.Duration(10) * time.Minute
			id := fmt.Sprintf("%v-%v-%v", service.Service, service.Address, service.Port)

			asr := &consulApi.AgentServiceRegistration{
				Kind:              service.Kind,
				ID:                service.ID,
				Name:              service.Service,
				Tags:              []string{"gray"},
				Port:              service.Port,
				Address:           service.Address,
				SocketPath:        service.SocketPath,
				TaggedAddresses:   service.TaggedAddresses,
				EnableTagOverride: service.EnableTagOverride,
				Meta:              service.Meta,
				Weights:           &service.Weights,
				Check: &consulApi.AgentServiceCheck{
					Interval: interval.String(), // 健康检查间隔
					CheckID:  id,
					Name:     service.Service + " 的健康检测",
					//TTL:    ttlTime.String(),
					DeregisterCriticalServiceAfter: deregister.String(),
					// grpc 支持，执行健康检查的地址，service 会传到 Health.Check 函数中
					GRPC: fmt.Sprintf("%v:%v/%v", service.Address, service.Port, service.Service),
				},
				//Checks:            service.,
				Proxy:     service.Proxy,
				Connect:   service.Connect,
				Namespace: service.Namespace,
				Partition: service.Partition,
				Locality:  service.Locality,
			}
			consulClient.Agent().ServiceRegister(asr)
		}
	}

	logger.Info(srvMap)
	//consulClient.Agent().ServiceRegister()
	return true, nil
}
