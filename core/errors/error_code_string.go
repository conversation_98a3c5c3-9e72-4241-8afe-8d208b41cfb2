// Code generated by "stringer -type ErrorCode -output error_code_string.go"; DO NOT EDIT.

package errors

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[OK-200]
	_ = x[BadRequest-400]
	_ = x[Unauthorized-401]
	_ = x[Forbidden-403]
	_ = x[NotFound-404]
	_ = x[MethodNotAllowed-405]
	_ = x[Timeout-408]
	_ = x[Conflict-409]
	_ = x[InternalServerError-500]
}

const (
	_ErrorCode_name_0 = "OK"
	_ErrorCode_name_1 = "BadRequestUnauthorized"
	_ErrorCode_name_2 = "ForbiddenNotFoundMethodNotAllowed"
	_ErrorCode_name_3 = "TimeoutConflict"
	_ErrorCode_name_4 = "InternalServerError"
)

var (
	_ErrorCode_index_1 = [...]uint8{0, 10, 22}
	_ErrorCode_index_2 = [...]uint8{0, 9, 17, 33}
	_ErrorCode_index_3 = [...]uint8{0, 7, 15}
)

func (i ErrorCode) String() string {
	switch {
	case i == 200:
		return _ErrorCode_name_0
	case 400 <= i && i <= 401:
		i -= 400
		return _ErrorCode_name_1[_ErrorCode_index_1[i]:_ErrorCode_index_1[i+1]]
	case 403 <= i && i <= 405:
		i -= 403
		return _ErrorCode_name_2[_ErrorCode_index_2[i]:_ErrorCode_index_2[i+1]]
	case 408 <= i && i <= 409:
		i -= 408
		return _ErrorCode_name_3[_ErrorCode_index_3[i]:_ErrorCode_index_3[i+1]]
	case i == 500:
		return _ErrorCode_name_4
	default:
		return "ErrorCode(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
