package S3

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"go-admin/core/sdk/config"
	"log"
)

// GetS3Session 列出玩家某天的日志列表
func GetS3Session() *session.Session {
	scCfg := config.S3Config
	s, err := session.NewSession(&aws.Config{
		Region: aws.String(scCfg.Region), // 替换自己账户的region
		Credentials: credentials.NewStaticCredentials(
			scCfg.AccessKey,
			scCfg.SecretKey,
			""), // SessionToken是进程相关，应该是连接中可以返回 （可为空）
	})
	if err != nil {
		log.Println("aws  failed", err)
	}
	return s
}
