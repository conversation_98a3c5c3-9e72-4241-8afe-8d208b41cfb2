package S3

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"os"
	"testing"
)

//const keys = {"mt/Release/63149/20230202/sp_63149_2023020201_1.sgl"}

func downTest(t *testing.T) {
	//s3Session := GetS3Session()
	//svc := s3.New(s3Session)
	//keys := {}
	//BatchDownloadWithClient(svc, "", keys)
}

func BatchDownloadWithClient(svc *s3.S3, bucket string, keys []string) {
	download := s3manager.NewDownloaderWithClient(svc, func(d *s3manager.Downloader) {
		d.PartSize = 64 * 1024 * 1024 // 64MB per part
		d.BufferProvider = s3manager.NewPooledBufferedWriterReadFromProvider(25 * 1024 * 1024)
	})
	objects := make([]s3manager.BatchDownloadObject, 0, len(keys))
	for _, key := range keys {
		f, err := os.Create("d_" + key)
		if err != nil {
			panic(err)
		}
		defer f.Close()
		objects = append(objects, s3manager.BatchDownloadObject{
			Object: &s3.GetObjectInput{
				Bucket: aws.String(bucket),
				Key:    aws.String(key),
			},
			Writer: f,
		})
	}

	iter := &s3manager.DownloadObjectsIterator{Objects: objects}
	if err := download.DownloadWithIterator(aws.BackgroundContext(), iter); err != nil {
		panic(err)
	}
}
