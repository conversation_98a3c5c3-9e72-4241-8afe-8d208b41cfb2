package awssdk

import (
	"bytes"
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	config2 "go-admin/core/sdk/config"
	"strings"
	"time"
)

const (
	bucketName = "clientsrv"
)

// 需要上传的结构体示例
type SensorData struct {
	DeviceID  string    `json:"device_id"`
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Unit      string    `json:"unit"`
}

// S3FileInfo S3文件信息
type S3FileInfo struct {
	Key          string    `json:"key"`
	Size         int64     `json:"size"`
	LastModified time.Time `json:"last_modified"`
	ETag         string    `json:"etag"`
}

func LoadS3Cfg() (aws.Config, error) {

	scCfg := config2.S3Config
	cfg, err := config.LoadDefaultConfig(context.Background(),
		config.WithRegion(scCfg.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			scCfg.AccessKey,
			scCfg.SecretKey,
			"")),
	)

	return cfg, err
}

// UploadJSON JSON序列化上传核心方法
func UploadJSON(ctx context.Context, uploader *manager.Uploader, data string, key string) error {

	// 创建内存reader
	reader := bytes.NewReader([]byte(data))

	// 执行上传
	_, err := uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(key),
		Body:        reader,
		ContentType: aws.String("application/json"), // 明确MIME类型
	})
	return err
}

// UploadImage 上传图片文件
func UploadImage(ctx context.Context, uploader *manager.Uploader, data []byte, key string, contentType string) error {
	// 创建内存reader
	reader := bytes.NewReader(data)

	// 执行上传
	_, err := uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(key),
		Body:        reader,
		ContentType: aws.String(contentType),
	})
	return err
}

// DeleteFile 删除S3文件
func DeleteFile(ctx context.Context, client *s3.Client, key string) error {
	_, err := client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})
	return err
}

// ListFiles 列出指定前缀的文件
func ListFiles(ctx context.Context, client *s3.Client, prefix string, maxKeys int32) ([]S3FileInfo, error) {
	input := &s3.ListObjectsV2Input{
		Bucket:  aws.String(bucketName),
		Prefix:  aws.String(prefix),
		MaxKeys: &maxKeys,
	}

	result, err := client.ListObjectsV2(ctx, input)
	if err != nil {
		return nil, err
	}

	var files []S3FileInfo
	for _, obj := range result.Contents {
		files = append(files, S3FileInfo{
			Key:          *obj.Key,
			Size:         *obj.Size,
			LastModified: *obj.LastModified,
			ETag:         *obj.ETag,
		})
	}

	return files, nil
}

// FileExists 检查S3中文件是否存在
func FileExists(ctx context.Context, client *s3.Client, key string) (bool, error) {
	_, err := client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(key),
	})

	if err != nil {
		// 如果错误信息包含 NoSuchKey 或 NotFound，说明文件不存在
		errStr := err.Error()
		if strings.Contains(errStr, "NoSuchKey") || strings.Contains(errStr, "NotFound") || strings.Contains(errStr, "404") {
			return false, nil
		}
		// 如果是其他类型的错误，返回错误
		return false, err
	}

	// 如果没有错误，说明文件存在
	return true, nil
}
