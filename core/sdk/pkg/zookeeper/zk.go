package zookeeper

import (
	"github.com/go-zookeeper/zk"
	"log"
	"net"
	"runtime"
	"strings"
	"time"
)

type Client struct {
	cli       *client
	localAddr string
}

func New(dialTimeout time.Duration, endpoint ...string) (cli *Client, err error) {
	c, err := NewInnerClient(strings.Join(endpoint, ","), "", dialTimeout)
	if err != nil {
		return
	}
	conn, err := net.DialTimeout("tcp", endpoint[0], dialTimeout)
	if err != nil {
		return
	}
	localAddr := strings.Split(conn.LocalAddr().String(), ":")[0]
	conn.Close()
	cli = &Client{cli: c, localAddr: localAddr}
	return
}

func (c *Client) Get(key string) (value string, err error) {
	data, err := c.cli.Read(key, true)
	if err != nil {
		return
	}
	value = string(data)
	return
}

func (c *Client) GetDir(prefix string) (data map[string]string, err error) {
	ret, err := c.cli.List(prefix, false)
	if err != nil {
		return
	}
	data = make(map[string]string)
	var buf []byte
	for _, v := range ret {
		buf, err = c.cli.Read(v, true)
		if err != nil {
			return
		}
		data[v] = string(buf)
	}
	return
}

////watch数据变化
//func (c *Client) Watch(key string) (ch plzk.WatchChan, err error) {
//	// 暂时无该需求，先实现为空函数
//	return
//}

//WatchDir 启动监听zk目录的变化的协程，并返回通知channel
func (c *Client) WatchDir(prefix string) (ch WatchChan, err error) {
	ch = make(WatchChan)
	go func() {
		for {
			var cch <-chan struct{}
			cch, _, err = c.cli.WatchInOrder(prefix)
			if err != nil {
				close(ch)
				return
			}

			<-cch // 阻塞等待zk发送目录变化通知

			//针对ZK来说，它的api不返回之前的额值，仅仅告知目录发生变化
			ch <- KVEvent{
				Type:  EventPut,
				KV:    nil,
				PreKV: nil,
			}
		}
	}()
	runtime.Gosched()
	time.Sleep(10 * time.Millisecond)

	return
}
func (c *Client) GetLocalIP() string {
	return c.localAddr
}

//Put set数据
func (c *Client) Put(key string, value string) (oldValue string, err error) {
	err = c.cli.CreateEphemeral(key, []byte(value))
	if err != nil && err != zk.ErrNodeExists {
		log.Printf("fail to put data, key:%s, value:%s, err:%s", key, value, err.Error())
		return
	}
	err = c.cli.Update(key, []byte(value))
	return
}

//Delete 删除key
func (c *Client) Delete(key string) (err error) {
	return c.cli.Delete(key)
}
func (c *Client) Close() error {
	return c.cli.Close()
}
