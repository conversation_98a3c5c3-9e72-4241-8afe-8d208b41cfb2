package zookeeper

import (
	"errors"
	"fmt"
	"github.com/go-zookeeper/zk"
	"log"
	"path"
	"reflect"
	"sort"
	"strings"
	"sync"
	"time"
)

var ErrClosedClient = errors.New("use of closed zk socket")

var DefaultLogfunc = func(format string, v ...interface{}) {
	log.Print("zookeeper - " + fmt.Sprintf(format, v...))
}

type client struct {
	sync.Mutex
	conn *zk.Conn

	addrlist string
	username string
	password string
	timeout  time.Duration

	logger *zkLogger
	dialAt time.Time
	closed bool
}

type zkLogger struct {
	logfunc func(format string, v ...interface{})
}

func (l *zkLogger) Printf(format string, v ...interface{}) {
	if l != nil && l.logfunc != nil {
		l.logfunc(format, v...)
	}
}

func NewInnerClient(addrlist string, auth string, timeout time.Duration) (*client, error) {
	return NewWithLogfunc(addrlist, auth, timeout, DefaultLogfunc)
}

func NewWithLogfunc(addrlist string, auth string, timeout time.Duration, logfunc func(foramt string, v ...interface{})) (*client, error) {
	if timeout <= 0 {
		timeout = time.Second * 5
	}
	c := &client{
		addrlist: addrlist, timeout: timeout,
		logger: &zkLogger{logfunc},
	}
	if auth != "" {
		split := strings.SplitN(auth, ":", 2)
		if len(split) != 2 || split[0] == "" {
			return nil, errors.New("invalid auth")
		}
		c.username = split[0]
		c.password = split[1]
	}
	if err := c.reset(); err != nil {
		return nil, err
	}
	return c, nil
}

func (c *client) reset() error {
	c.dialAt = time.Now()
	conn, _, err := zk.Connect(strings.Split(c.addrlist, ","), c.timeout)
	if err != nil {
		return err
	}
	if c.conn != nil {
		c.conn.Close()
	}
	c.conn = conn
	c.conn.SetLogger(c.logger)

	c.logger.Printf("zkclient setup new connection to %s", c.addrlist)

	if c.username != "" {
		var auth = fmt.Sprintf("%s:%s", c.username, c.password)
		if err := c.conn.AddAuth("digest", []byte(auth)); err != nil {
			return err
		}
	}

	//go func() {
	//	for e := range events {
	//		log.Printf("zookeeper event: %+v", e)
	//	}
	//}()
	return nil
}

func (c *client) Close() error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return nil
	}
	c.closed = true

	if c.conn != nil {
		c.conn.Close()
	}
	return nil
}

func (c *client) Do(fn func(conn *zk.Conn) error) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	return c.shell(fn)
}

func (c *client) shell(fn func(conn *zk.Conn) error) error {
	if err := fn(c.conn); err != nil {
		for _, e := range []error{zk.ErrNoNode, zk.ErrNodeExists, zk.ErrNotEmpty} {
			if err == e {
				return e
			}
		}
		if time.Since(c.dialAt) > time.Second {
			if err := c.reset(); err != nil {
				log.Println(err, "zkclient reset connection failed")
			}
		}
		return err
	}
	return nil
}

func (c *client) Mkdir(fullPath string) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	//log.Printf("zkclient mkdir node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		return c.mkdir(conn, fullPath)
	})
	if err != nil {
		log.Printf("zkclient mkdir node %s failed: %s", fullPath, err)
		return err
	}
	//log.Printf("zkclient mkdir OK")
	return nil
}

func (c *client) mkdir(conn *zk.Conn, fullPath string) error {
	if fullPath == "" || fullPath == "/" {
		return nil
	}
	if exists, _, err := conn.Exists(fullPath); err != nil {
		log.Printf("execute command:Exists :%s failed, err:%s\n", fullPath, err.Error())
		return err
	} else if exists {
		return nil
	}
	if err := c.mkdir(conn, path.Dir(fullPath)); err != nil {
		return err
	}
	_, err := conn.Create(fullPath, []byte{}, 0, func() []zk.ACL {
		const perm = zk.PermAll
		if c.username != "" {
			return zk.DigestACL(perm, c.username, c.password)
		}
		return zk.WorldACL(perm)
	}())
	if err != nil && err != zk.ErrNodeExists {
		return err
	}
	return nil
}

func (c *client) Create(fullPath string, data []byte) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	log.Printf("zkclient create node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		_, err := c.create(conn, fullPath, data, 0)
		return err
	})
	if err != nil {
		log.Printf("zkclient create node %s failed: %s", fullPath, err)
		return err
	}
	log.Printf("zkclient create OK")
	return nil
}

func (c *client) CreateEphemeral(fullPath string, data []byte) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	log.Printf("zkclient create-ephemeral node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		_, err := c.create(conn, fullPath, data, zk.FlagEphemeral)
		return err
	})
	if err != nil {
		log.Printf("zkclient create-ephemeral node %s failed: %s", fullPath, err)
		return err
	}
	log.Printf("zkclient create-ephemeral OK: %q", fullPath)
	return nil
}

func (c *client) create(conn *zk.Conn, fullPath string, data []byte, flag int32) (string, error) {
	if err := c.mkdir(conn, path.Dir(fullPath)); err != nil {
		return "", err
	}
	p, err := conn.Create(fullPath, data, flag, func() []zk.ACL {
		const perm = zk.PermAdmin | zk.PermRead | zk.PermWrite
		if c.username != "" {
			return zk.DigestACL(perm, c.username, c.password)
		}
		return zk.WorldACL(perm)
	}())
	if err != nil {
		return "", err
	}
	return p, nil
}

func (c *client) watch(conn *zk.Conn, fullPath string) (<-chan struct{}, error) {
	_, _, w, err := conn.GetW(fullPath)
	if err != nil {
		return nil, err
	}
	signal := make(chan struct{})
	go func() {
		defer close(signal)
		<-w
		log.Printf("zkclient watch node %s update", fullPath)
	}()
	return signal, nil
}

func (c *client) Update(fullPath string, data []byte) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	log.Printf("zkclient update node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		return c.update(conn, fullPath, data)
	})
	if err != nil {
		log.Printf("zkclient update node %s failed: %s", fullPath, err)
		return err
	}
	log.Printf("zkclient update OK")
	return nil
}

func (c *client) update(conn *zk.Conn, fullPath string, data []byte) error {
	if exists, _, err := conn.Exists(fullPath); err != nil {
		return err
	} else if !exists {
		_, err := c.create(conn, fullPath, data, 0)
		if err != nil && err != zk.ErrNodeExists {
			return err
		}
	}
	_, err := conn.Set(fullPath, data, -1)
	if err != nil {
		return err
	}
	return nil
}

func (c *client) Delete(fullPath string) error {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return ErrClosedClient
	}
	log.Printf("zkclient delete node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		err := conn.Delete(fullPath, -1)
		if err != nil && err != zk.ErrNoNode {
			return err
		}
		return nil
	})
	if err != nil {
		log.Printf("zkclient delete node %s failed: %s", fullPath, err)
		return err
	}
	log.Printf("zkclient delete OK")
	return nil
}

func (c *client) Read(fullPath string, must bool) ([]byte, error) {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return nil, ErrClosedClient
	}
	var data []byte
	err := c.shell(func(conn *zk.Conn) error {
		b, _, err := conn.Get(fullPath)
		if err != nil {
			if err == zk.ErrNoNode && !must {
				return nil
			}
			return err
		}
		data = b
		return nil
	})
	if err != nil {
		log.Printf("zkclient read node %s failed: %s", fullPath, err)
		return nil, err
	}
	return data, nil
}

func (c *client) List(fullPath string, must bool) ([]string, error) {
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return nil, ErrClosedClient
	}
	var paths []string
	err := c.shell(func(conn *zk.Conn) error {
		nodes, _, err := conn.Children(fullPath)
		if err != nil {
			if err == zk.ErrNoNode && !must {
				return nil
			}
			return err
		}
		for _, node := range nodes {
			paths = append(paths, path.Join(fullPath, node))
		}
		return nil
	})
	if err != nil {
		log.Printf("zkclient list node %s failed: %s", fullPath, err)
		return nil, err
	}
	return paths, nil
}

//func (c *client) CreateEphemeralInOrder(fullPath string, data []byte) (<-chan struct{}, string, error) {
//	c.Lock()
//	defer c.Unlock()
//	if c.closed {
//		return nil, "", ErrClosedClient
//	}
//	if !strings.HasSuffix(fullPath, "/") {
//		fullPath += "/"
//	}
//	var signal <-chan struct{}
//	var node string
//	log.Printf("zkclient create-ephemeral-inorder node %s", fullPath)
//	err := c.shell(func(conn *zk.Conn) error {
//		p, err := c.create(conn, fullPath, data, zk.FlagEphemeral|zk.FlagSequence)
//		if err != nil {
//			return err
//		}
//		w, err := c.watch(conn, p)
//		if err != nil {
//			return err
//		}
//		signal, node = w, p
//		return nil
//	})
//	if err != nil {
//		log.Printf("zkclient create-ephemeral-inorder node %s failed: %s", fullPath, err)
//		return nil, "", err
//	}
//	log.Printf("zkclient create-ephemeral-inorder OK, node = %s", node)
//	return signal, node, nil
//}

func (c *client) WatchInOrder(fullPath string) (<-chan struct{}, []string, error) {
	if err := c.Mkdir(fullPath); err != nil {
		return nil, nil, err
	}
	c.Lock()
	defer c.Unlock()
	if c.closed {
		return nil, nil, ErrClosedClient
	}
	var signal chan struct{}
	var paths []string
	//log.Printf("zkclient watch-inorder node %s", fullPath)
	err := c.shell(func(conn *zk.Conn) error {
		nodes, _, wc, errc := conn.ChildrenW(fullPath)
		if errc != nil {
			return errc
		}
		chs := make([]reflect.SelectCase, 0, len(nodes)+1)
		chs = append(chs, reflect.SelectCase{
			Dir:  reflect.SelectRecv,
			Chan: reflect.ValueOf(wc),
		})
		sort.Strings(nodes)
		for _, node := range nodes {
			paths = append(paths, path.Join(fullPath, node))
		}
		signal = make(chan struct{})

		for _, pth := range paths {
			_, _, wn, errn := conn.GetW(pth)
			if errn != nil {
				return errn
			}
			chs = append(chs, reflect.SelectCase{
				Dir:  reflect.SelectRecv,
				Chan: reflect.ValueOf(wn),
			})
		}

		go func() {
			defer close(signal)
			reflect.Select(chs)
			log.Printf("zkclient watch-inorder node %s update", fullPath)
		}()

		return nil
	})
	if err != nil {
		log.Printf("zkclient watch-inorder node %s failed: %s", fullPath, err)
		return nil, nil, err
	}
	//log.Printf("zkclient watch-inorder OK")
	return signal, paths, nil
}
