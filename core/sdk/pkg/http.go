package pkg

import (
	"bytes"
	"encoding/json"
	"fmt"
	log "go-admin/core/logger"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"time"
)

const (
	ContentTypeForm = "application/x-www-form-urlencoded"
)

// Get 发送GET请求
// url：         请求地址
// response：    请求返回的内容
func Get(url string) (string, error) {

	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer func(Body io.ReadCloser) {
		errClose := Body.Close()
		if errClose != nil {
			log.Error("http close err ： %v", errClose)
		}
	}(resp.Body)
	result, _ := ioutil.ReadAll(resp.Body)

	return string(result), nil
}

// GetSingle 发送GET请求 不需要返回
// url：         请求地址
func GetSingle(url string) {
	resp, err := http.Get(url)
	if err != nil {
		fmt.Println(err)
		return
	}
	resp.Body.Close()
}

func GetFeiShu(url string, accessToken string, params url.Values) ([]byte, error) {
	query := params.Encode()

	client := &http.Client{}
	req, err := http.NewRequest("GET", url, bytes.NewBuffer([]byte(query)))
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)
	if err != nil {
		return nil, err
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errClose := Body.Close()
		if errClose != nil {
			log.Error("http close err ： %v", errClose)
		}
	}(resp.Body)
	result, _ := ioutil.ReadAll(resp.Body)

	return result, nil
}

// Post 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// content：     请求放回的内容
func Post(url string, data interface{}, contentType string) ([]byte, error) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	resp, err := client.Post(url, contentType, bytes.NewBuffer(jsonStr))
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errClose := Body.Close()
		if errClose != nil {
			log.Error("http close err ： %v", errClose)
		}
	}(resp.Body)

	result, _ := ioutil.ReadAll(resp.Body)
	return result, nil

}

// PostForm 发送POST Form请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 固定为application/x-www-form-urlencoded
// content：     请求放回的内容
func PostForm(url string, params url.Values) ([]byte, error) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.PostForm(url, params)
	if err != nil {
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		errClose := Body.Close()
		if errClose != nil {
			log.Error("http close err ： %v", errClose)
		}
	}(resp.Body)

	result, _ := ioutil.ReadAll(resp.Body)
	return result, nil
}
