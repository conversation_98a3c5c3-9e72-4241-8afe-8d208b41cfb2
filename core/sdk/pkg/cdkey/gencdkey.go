package cdkey

import (
	"math/rand"
	"time"
)

var letterRunes = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ"

func RandStringRunes(n int) string {
	rand.Seed(time.Now().UnixNano())

	b := make([]rune, n)
	for i := range b {
		b[i] = rune(letterRunes[rand.Intn(len(letterRunes))])
	}
	return string(b)
}

func RandStringBytes(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterRunes[rand.Intn(len(letterRunes))]
	}
	return string(b)
}
