package cdkey

import "testing"

func TestRandStringRunes(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"test1", args{8}, 8},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RandStringRunes(tt.args.n); len(got) != tt.want {
				t.<PERSON><PERSON>("RandStringRunes() = %v, want %v", got, tt.want)
			} else {
				t.Log(got)
			}
		})
	}
}

func TestRandStringBytes(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{"test1", args{8}, 8},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RandStringBytes(tt.args.n); len(got) != tt.want {
				t.<PERSON><PERSON><PERSON>("RandStringRunes() = %v, want %v", got, tt.want)
			} else {
				t.Log(got)
			}
		})
	}
}
