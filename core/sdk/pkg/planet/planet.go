package planet

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go-admin/common/middleware/handler"
	"go-admin/core/tools/utils"
	"strconv"
)

func GetPlanetID(c *gin.Context) int {
	p, ok := c.Get("planetID")
	if !ok {
		return 0
	}

	pInt, err := strconv.Atoi(p.(string))
	if err != nil {
		return 0
	}

	return pInt
}

func GetPlanetInfo(c *gin.Context) (*handler.PlanetMid, bool) {
	i, ok := c.Get("planetInfo")
	if !ok {
		return nil, false
	}

	info, ok := i.(*handler.PlanetMid)
	if !ok {
		return nil, false
	}

	return info, true
}

func GetEnvName(c *gin.Context) (string, error) {
	planetInfo, ok := GetPlanetInfo(c)
	if !ok {
		return "", errors.New("get planetInfo err")
	}

	envType := planetInfo.EnvIDG

	return utils.GetEnvName(envType), nil
}
