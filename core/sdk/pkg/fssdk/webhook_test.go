package fssdk

import (
	"testing"
	"go-admin/core/tools/utils"
)

func TestPubCfg(t *testing.T) {
	Notify(utils.Develop, "Hi,这是来自平台的一条自动消息，有配置更新，请及时处理")
}

func TestPubCardCommit(t *testing.T) {
	obj := CardMsgInfo{
		Status:   2,
		Title:    "【配置引擎】提交配置",
		Text:     "Hi,这是来自平台的一条自动消息，有配置更新，请及时处理",
		ClickUrl: "http://localhost:9527/config/t-config-operate-master",
		AtMember: "ou_ad3571f473bbf149de2c99e07542014e",
		ConfigInfos: []ConfigInfo{
			{
				FileName:  "fish_basic.xlsx",
				SheetName: "basic_fish_character",
				Editor:    "admin",
				TimeStamp: "2024-09-11 15:00:00",
			},
		},
	}
	NotifyCard(utils.Develop, obj)
}

func TestPubCardAt(t *testing.T) {
	obj := CardMsgInfo{
		Status:   3,
		Title:    "【配置引擎】提交配置",
		Text:     "Hi,这是来自平台的一条自动消息，有配置更新，请及时处理",
		ClickUrl: "http://localhost:9527/config/t-config-operate-master",
		AtMember: "ou_ad3571f473bbf149de2c99e07542014e",
		ConfigInfos: []ConfigInfo{
			{
				FileName:  "fish_basic.xlsx",
				SheetName: "basic_fish_character",
				Editor:    "admin",
				TimeStamp: "2024-09-11 15:00:00",
			},
			{
				FileName:  "AppInfo.xlsx",
				SheetName: "app_update_info",
				Editor:    "admin",
				TimeStamp: "2024-09-11 15:00:00",
			},
			{
				FileName:  "AppInfo.xlsx",
				SheetName: "app_update_info",
				Editor:    "admin",
				TimeStamp: "2024-09-11 15:00:00",
			},
		},
	}
	NotifyCard(utils.Develop, obj)
}

func TestNotifyTemplate(t *testing.T) {
	type args struct {
		req TemplateVariable
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
		{
			name: "test",
			args: args{req: TemplateVariable{
				Topic:      "发布成功",
				TopicColor: "red",
				TableRawArray: []TableRaw{
					{
						ID:        168,
						FileName:  "飞书科技有限公司",
						SheetName: "飞书科技有限公司",
					},
				},
				Date:        "2024-10-28",
				Env:         "develop",
				Product:     "1",
				Channel:     "1001",
				CommitUser:  []string{"ou_522a95e896a6554f5a7895fb1d5e2de5"},
				AuditUser:   []string{"ou_522a95e896a6554f5a7895fb1d5e2de5"},
				Remark:      "",
				PlatformUrl: "http://192.168.1.52:9527/config/t-config-operate-master",
			}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			NotifyTemplate(utils.Develop, tt.args.req)
		})
	}
}
