package fssdk

import (
	"encoding/json"
	"errors"
	log "go-admin/core/logger"
	"go-admin/core/sdk/pkg"
	"net/url"
)

const FsAccessTokenUrl = "https://passport.feishu.cn/suite/passport/oauth/token"
const FsLoginInfoUrl = "https://passport.feishu.cn/suite/passport/oauth/userinfo"
const FsUserInfoSingleUrl = "https://open.feishu.cn/open-apis/contact/v3/users/"
const ContentTypeForm = "application/x-www-form-urlencoded"
const ContentTypeJson = "application/json;charset=UTF-8"
const GrantType = "authorization_code"
const ClientId = "cli_a57b9d3f95f9100c"
const ClientSecret = "l1y7NdrtIe88H4HxuaLLWbDVwagnRhJz"

// 获取飞书AccessToken
func getAccessToken(code string, redirectUri string) (*AccessTokenRsp, error) {
	params := url.Values{}
	params.Set("grant_type", GrantType)
	params.Set("client_id", ClientId)
	params.Set("client_secret", ClientSecret)
	params.Set("code", code)
	// params.Set("code_verifier", "")
	params.Set("redirect_uri", redirectUri)

	bytes, err := pkg.PostForm(FsAccessTokenUrl, params)
	if err != nil {
		log.Error("GetAccessToken err : %s", err.Error())
		return nil, err
	}

	asts := &AccessTokenRsp{}
	if err = json.Unmarshal(bytes, asts); err != nil {
		log.Error("Json解析失败")
		return nil, errors.New("getAccessToken Json解析失败")
	}
	return asts, nil
}

func GetUserInfo(code string, redirectUri string) (*UserLoginInfoRsp, error) {
	if code == "" {
		return nil, errors.New("code empty error")
	}

	accessTokenRsp, err := getAccessToken(code, redirectUri)
	if err != nil {
		log.Error("getAccessToken error")
		return nil, errors.New(err.Error())
	}

	if accessTokenRsp != nil && accessTokenRsp.Error != "" {
		return nil, errors.New(accessTokenRsp.ErrorDesc)
	}

	// 获取Auth基本信息
	params := url.Values{}
	bytes, err := pkg.GetFeiShu(FsLoginInfoUrl, accessTokenRsp.AccessToken, params)
	if err != nil {
		log.Error("GetLoginUserInfo err : %s", err.Error())
	}

	// {"code":10400,"message":"该应用暂不可用，请联系应用开发者","data":null}
	uiRsp := &UserLoginInfoRsp{}
	if err = json.Unmarshal(bytes, uiRsp); err != nil {
		log.Error("Json解析失败")
		return nil, errors.New("登录信息 Json解析失败")
	}

	// 获取单个用户信息，获取部门等信息
	params2 := url.Values{}
	// params2.Set("user_id_type", "open_id")
	// params2.Set("department_id_type", "open_department_id")
	byteInfo, errInfo := pkg.GetFeiShu(FsUserInfoSingleUrl+uiRsp.OpenId, accessTokenRsp.AccessToken, params2)
	if errInfo != nil {
		log.Error("GetUserInfo err : %s", err.Error())
	}

	userInfo := &UserInfoRet{}
	if err = json.Unmarshal(byteInfo, userInfo); err != nil {
		log.Error("Json解析失败")
		return nil, errors.New("getUserInfo解析失败")
	}

	return uiRsp, nil
}
