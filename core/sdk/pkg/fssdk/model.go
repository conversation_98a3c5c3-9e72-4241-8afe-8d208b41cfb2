package fssdk

// AccessTokenRsp 获取AccessToken Rsp
type AccessTokenRsp struct {
	Error            string `json:"error"`
	ErrorDesc        string `json:"error_description"`
	AccessToken      string `json:"access_token"`       //access_token，用于调用其他接口
	TokenType        string `json:"token_type"`         //OAuth 2.0协议规定的Token类型，固定为 Bearer
	ExpiresIn        int64  `json:"expires_in"`         //access_token 的有效期，
	RefreshToken     string `json:"refresh_token"`      //RefreshToken
	RefreshExpiresIn int64  `json:"refresh_expires_in"` //refresh_expires_in
}

type UserLoginInfoRsp struct {
	Sub          string `json:"sub"`           //用户在应用内的唯一标识，等同于open_id
	Name         string `json:"name"`          //name
	Picture      string `json:"picture"`       //用户头像，等同于avatar_url
	OpenId       string `json:"open_id"`       //用户在应用内的唯一标识, 等同于sub
	UnionId      string `json:"union_id"`      //用户统一ID，在同一租户开发的所有应用内的唯一标识
	EnName       string `json:"en_name"`       //用户英文名称
	TenantKey    string `json:"tenant_key"`    //当前企业标识
	AvatarUrl    string `json:"avatar_url"`    //用户头像，picture
	AvatarThumb  string `json:"avatar_thumb"`  //用户头像 72x72
	AvatarMiddle string `json:"avatar_middle"` //用户头像 240x240
	AvatarBig    string `json:"avatar_big"`    //用户头像 640x640
	UserId       string `json:"user_id"`       //用户 user id，申请了邮箱获取权限(获取用户 user ID)的应用会返回该字段
	Email        string `json:"email"`         //用户邮箱，申请了邮箱获取权限(获取用户邮箱信息)的应用会返回该字段
	Mobile       string `json:"mobile"`        //用户手机号，申请了手机号获取权限(获取用户手机号)的应用会返回该字段
}

type UserInfoRet struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data FSUser `json:"data"`
}

type FSUser struct {
	User UserInfo `json:"user"`
}

type UserInfo struct {
	UserId          string   `json:"user_id"`
	OpenId          string   `json:"open_id"`
	Name            string   `json:"name"`
	EnName          string   `json:"en_name"`
	NickName        string   `json:"nickname"`
	Email           string   `json:"email"`
	Mobile          string   `json:"mobile"`
	Gender          int      `json:"gender"`
	IsTenantManager bool     `json:"is_tenant_manager"` //是否租户超级管理员
	DepartmentIds   []string `json:"department_ids"`
	LeaderUserId    string   `json:"leader_user_id"`
	JobTitle        string   `json:"job_title"`
}

// InteractiveMessage 表示整个交互式消息的结构
type InteractiveMessage struct {
	MsgType string `json:"msg_type"` // 消息类型
	Card    Card   `json:"card"`     // 消息卡片内容
}

// Card 表示消息卡片的结构
type Card struct {
	Type string       `json:"type"` // 卡片类型
	Data TemplateData `json:"data"` // 卡片数据
}

// TemplateData 表示模板数据的具体内容
type TemplateData struct {
	TemplateID          string           `json:"template_id"`           // 模板ID
	TemplateVersionName string           `json:"template_version_name"` // 模板版本名称
	TemplateVariable    TemplateVariable `json:"template_variable"`     // 模板变量
}

// TemplateVariable 表示模板变量的具体内容
type TemplateVariable struct {
	Topic         string     `json:"topic"`           // 主题
	TopicColor    string     `json:"topic_color"`     // 主题颜色
	TableRawArray []TableRaw `json:"table_raw_array"` // 表格数据数组
	Date          string     `json:"date"`            // 日期
	Env           string     `json:"env"`             // 环境信息
	Product       string     `json:"product"`         // 产品信息
	Channel       string     `json:"channel"`         // 渠道信息
	CommitUser    []string   `json:"commit_user"`     // 提交用户列表
	AuditUser     []string   `json:"audit_user"`      // 审核用户列表
	Remark        string     `json:"remark"`          // 备注
	PlatformUrl   string     `json:"platform_url"`    // 后台地址
	At            string     `json:"at"`              // 关注人
}

// TableRaw 表示表格中的一行数据
type TableRaw struct {
	ID        int    `json:"id"`         // 数据ID
	FileName  string `json:"file_name"`  // 文件名称
	SheetName string `json:"sheet_name"` // 表格名称
}
