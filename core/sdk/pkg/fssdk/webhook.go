package fssdk

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/sirupsen/logrus"
	"go-admin/core/tools/utils"
	"io"
	"net/http"
)

// 替换为你的飞书机器人Webhook地址
const (
	DevWebhookURL  = "https://open.feishu.cn/open-apis/bot/v2/hook/e0acdeba-f8a4-45b6-beb4-d84f1c588fdc"
	ProdWebhookURL = "https://open.feishu.cn/open-apis/bot/v2/hook/39204970-62a7-4b27-8c40-dbca59fbc22a"
)

// getWebhookURL 根据环境返回对应的webhook URL
func getWebhookURL(env int) string {
	switch env {
	case utils.Develop, utils.Test:
		return DevWebhookURL
	case utils.Prod, utils.Audit, utils.Review:
		return ProdWebhookURL
	default:
		// 默认使用开发环境
		return DevWebhookURL
	}
}

type FeiShuPayload struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

func Notify(env int, text string) {
	// 创建通知内容
	payload := FeiShuPayload{
		MsgType: "text",
		Content: struct {
			Text string `json:"text"`
		}{
			Text: text,
		},
	}

	// 序列化数据
	payloadBytes, _ := json.Marshal(payload)
	body := bytes.NewReader(payloadBytes)

	// 根据环境选择webhook URL
	webhookURL := getWebhookURL(env)

	// 发送POST请求
	resp, err := http.Post(webhookURL, "application/json", body)
	if err != nil {
		logrus.Errorf("Notify error：%v", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, _ := io.ReadAll(resp.Body)
	fmt.Println(string(respBody))
}

type ConfigInfo struct {
	FileName  string
	SheetName string
	Editor    string
	TimeStamp string
}

type CardMsgInfo struct {
	Status      int32
	Title       string
	Text        string
	ClickUrl    string
	AtMember    string
	ConfigInfos []ConfigInfo
}

func FmtConfigList(configInfos []ConfigInfo) string {
	ctxAll := "更新内容：\n"
	for index, info := range configInfos {
		row := fmt.Sprintf("%d. %s (%s)\n",
			index+1,
			info.SheetName,
			info.FileName,
		)
		ctxAll = ctxAll + row
	}
	return ctxAll
}

func GetColorByStatus(status int32) string {
	color := "yellow"
	switch status {
	case 1:
		color = "yellow"
	case 2:
		color = "orange"
	case 3:
		color = "green"
	case 4:
		color = "red"
	}
	return color
}

func NotifyCard(env int, cardInfo CardMsgInfo) {

	cfgListInfoStr := ""
	if len(cardInfo.ConfigInfos) > 0 {
		cfgListInfoStr = FmtConfigList(cardInfo.ConfigInfos)
	}

	contentStr := cardInfo.Text + "\n" + cfgListInfoStr

	atFmt := fmt.Sprintf("<at id=%s></at>需要去处理配置审核", cardInfo.AtMember)

	// 创建通知内容
	cardMsg := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"config": map[string]bool{
				"wide_screen_mode": true,
				"enable_forward":   true,
			},
			"header": map[string]interface{}{
				"template": GetColorByStatus(cardInfo.Status),
				"title": map[string]interface{}{
					"content": cardInfo.Title,
					"tag":     "plain_text",
				},
			},
			"elements": []interface{}{
				map[string]interface{}{
					"tag": "div",
					"text": map[string]interface{}{
						"content": contentStr,
						"tag":     "lark_md",
					},
				},
				map[string]interface{}{
					"tag": "hr",
				},
				map[string]interface{}{
					"tag": "div",
					"text": map[string]interface{}{
						"content": atFmt,
						"tag":     "lark_md",
					},
				},
				map[string]interface{}{
					"tag": "hr",
				},
				map[string]interface{}{
					"actions": []interface{}{
						map[string]interface{}{
							"tag": "button",
							"text": map[string]interface{}{
								"content": "去管理平台",
								"tag":     "plain_text",
							},
							"url": cardInfo.ClickUrl,
							"value": map[string]interface{}{
								"key": "value",
							},
						},
					},
					"tag": "action",
				},
			},
		},
	}

	// 序列化数据
	payloadBytes, _ := json.MarshalIndent(cardMsg, "", "  ")
	body := bytes.NewReader(payloadBytes)

	// 根据环境选择webhook URL
	webhookURL := getWebhookURL(env)

	// 发送POST请求
	resp, err := http.Post(webhookURL, "application/json", body)
	if err != nil {
		logrus.Errorf("NotifyCard error：%v", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, _ := io.ReadAll(resp.Body)
	fmt.Println(string(respBody))
}

func NotifyTemplate(env int, req TemplateVariable) {

	msg := InteractiveMessage{
		MsgType: "interactive",
		Card: Card{
			Type: "template",
			Data: TemplateData{
				TemplateID:          "AAqD7eFdctur8",
				TemplateVersionName: "1.0.8",
				TemplateVariable:    req,
			},
		},
	}

	// 序列化数据
	payloadBytes, err := json.Marshal(msg)
	if err != nil {
		logrus.Errorf("Serialization failure：%v", err)
		return
	}
	body := bytes.NewReader(payloadBytes)

	// 根据环境选择webhook URL
	webhookURL := getWebhookURL(env)

	// 发送POST请求
	resp, err := http.Post(webhookURL, "application/json", body)
	if err != nil {
		logrus.Errorf("NotifyTemplate error：%v", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, _ := io.ReadAll(resp.Body)
	fmt.Println(string(respBody))
}
