package service

import (
	"fmt"
	"github.com/gin-gonic/gin"

	"go-admin/core/logger"
	"gorm.io/gorm"
)

type Service struct {
	Orm     *gorm.DB
	Msg     string
	MsgID   string
	Log     *logger.Helper
	Error   error
	Context *gin.Context
}

func (db *Service) AddError(err error) error {
	if db.Error == nil {
		db.Error = err
	} else if err != nil {
		db.Error = fmt.Errorf("%v; %w", db.Error, err)
	}
	return db.Error
}
