package main

import (
	"encoding/json"
	"fmt"
	"reflect"
)

type gmReq struct {
	ProductId int `json:"product_id" comment:"product_id"`
	ChannelId int `json:"channel_id" comment:"channel_id"`

	BcType     int      `json:"bc_type"`
	TemplateId int64    `json:"template_id"`
	Params     []string `json:"params"`
	CreateTime int64    `json:"create_time"`
}

var req gmReq

func main() {

	// Generate JSON configuration
	configs, err := generateJSONFromStruct(req)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// Marshal the configuration into a JSON string
	jsonData, err := json.Marshal(configs)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// Print the JSON string
	fmt.Println(string(jsonData))
}

type FieldConfig struct {
	Type     string                 `json:"type"`
	Field    string                 `json:"field"`
	Title    string                 `json:"title"`
	Props    map[string]interface{} `json:"props,omitempty"`
	Required bool                   `json:"$required,omitempty"`
	Value    interface{}            `json:"value,omitempty"`
	Info     string                 `json:"info,omitempty"`
}

func generateJSONFromStruct(obj interface{}) ([]FieldConfig, error) {
	val := reflect.ValueOf(obj)
	if val.Kind() != reflect.Struct {
		return nil, fmt.Errorf("expected a struct, got %s", val.Kind())
	}

	configs := []FieldConfig{
		{
			Type:  "input",
			Field: "gmName",
			Title: "描述",
			Props: map[string]interface{}{"disabled": true},
		},
		{
			Type:  "input",
			Field: "gmCmd",
			Title: "CMD",
			Props: map[string]interface{}{"disabled": true},
		},
		{
			Type:  "input",
			Field: "gmOperator",
			Title: "操作者",
			Props: map[string]interface{}{"disabled": true},
		},
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" {
			continue
		}

		config := FieldConfig{
			Field: jsonTag,
			Title: jsonTag,
		}

		// Determine the type of input based on the field kind
		switch field.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			config.Type = "inputNumber"
			config.Value = field.Int()
		case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
			config.Type = "inputNumber"
			config.Value = field.Uint()
		case reflect.Bool:
			config.Type = "input"
			config.Props = map[string]interface{}{"type": "checkbox"}
			config.Value = field.Bool()
		case reflect.Float32, reflect.Float64:
			config.Type = "input"
			config.Props = map[string]interface{}{"type": "number"}
			config.Value = field.Float()
		case reflect.String:
			config.Type = "input"
			config.Value = field.String()
		default:
			// For other types, you can add more cases or handle them as needed
			config.Type = "input"
			config.Value = fmt.Sprintf("%v", field.Interface())
		}

		config.Required = true

		configs = append(configs, config)
	}

	return configs, nil
}
