package config

import (
	"errors"
	"fmt"
)

var ExtConfig Extend

func (e *Extend) GetGMAddrByEnv(env string) (GMAddr, error) {
	for _, gmAddr := range e.GMInfo.GMList {
		if gmAddr.Env == env {
			return gmAddr, nil
		}
	}
	return GMAddr{}, errors.New("env addr not found")
}

func (e *Extend) GetGMKey() string {
	return e.GMInfo.GMKey
}

// Extend 扩展配置
//  extend:
//    demo:
//      name: demo-name
// 使用方法： config.ExtConfig......即可！！
type Extend struct {
	AMap   AMap   // 这里配置对应配置文件的结构即可
	GMInfo GMInfo `yaml:"GMInfo"`
	BitLy  BitLy  `yaml:"BitLy"`
}

type AMap struct {
	Key string
}

type GMInfo struct {
	GMList []GMAddr `yaml:"GMList"`
	GMKey  string   `yaml:"GMKey"`
}

type BitLy struct {
	UserName     string `yaml:"UserName"`
	PassWord     string `yaml:"PassWord"`
	ClientId     string `yaml:"ClientId"`
	ClientSecret string `yaml:"ClientSecret"`
}

type GMAddr struct {
	Env  string `json:"env"`
	Addr string `json:"addr"`
}

func (g GMAddr) GetGMPlatformURL() string {
	return fmt.Sprintf("%s/oam/config/get/:platform", g.Addr)
}
