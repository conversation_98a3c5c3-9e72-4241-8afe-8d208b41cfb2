settings:
  application:
    # dev开发环境 test测试环境 prod线上环境
    mode: prod
    # 服务器ip，默认使用 0.0.0.0
    host: 0.0.0.0
    # 服务名称
    name: opr
    # 端口号
    port: 8000 # 服务端口号
    readtimeout: 1
    writertimeout: 2
    # 数据权限功能开关
    enabledp: false
  logger:
    # 日志存放路径
    path: temp/logs
    # 日志输出，file：文件，default：命令行，其他：命令行
    stdout: '' #控制台日志，启用后，不输出到文件
    # 日志等级, trace, debug, info, warn, error, fatal
    level: trace
    # 数据库日志开关
    enableddb: true
  jwt:
    # token 密钥，生产环境时及的修改
    secret: mBaJ9YPQ9mTD9e
    # token 过期时间 单位：秒
    timeout: 43200

  database:
    # 数据库类型 mysql, sqlite3, postgres, sqlserver
    # sqlserver: sqlserver://用户名:密码@地址?database=数据库名
    driver: mysql
    # 数据库连接字符串 mysql 缺省信息 charset=utf8&parseTime=True&loc=Local&timeout=1000ms
    source: root:ROOT123@tcp(*************:28001)/sparkleplt?charset=utf8&parseTime=True&loc=Local&timeout=1000ms

  gen:
    # 代码生成读取的数据库名称
    dbname: sparklegen
    # 代码生成是使用前端代码存放位置，需要指定到src文件夹，相对路径
    frontpath: ../go-admin-ui/src
  extend: # 扩展项使用说明
    demo:
      name: data
    GMInfo:
      GMKey: "SHJKDSA^&@!HJ!@GHJVASCSIUJ*("
      GMList:
        - addr: "http://192.168.0.83:5980"
          env: "develop"
        - addr: "http://52.221.223.119:5980"
          env: "test"
        - addr: "http://13.229.34.106:5980"
          env: "beta"
        - addr: "http://13.215.119.241:5980"
          env: "audit"
        - addr: "http://192.168.0.87:20004"
          env: "release"
    BitLy:
      UserName: <EMAIL>
      Password: liuyalong199027
      ClientId: bd41b1197c406ff23beca5d019be342df2b2239f
      ClientSecret: 4b5ec1ecbb6f553fa49a9435e13b449827ca0cbb
  cache:
    memory: ''
  queue:
    memory:
      poolSize: 200
  locker:
    redis:
  gaea:
    homeAddr: http://*************:24004/home

  # S3
  s3:
    bucketKey: sparklelogs
    region: us-east-2
    accessKey: ********************
    secretKey: 1nnXZTXICu5F0yObCZDOhL8Snktv7QjsBrlnt8mf
    remoteBucketHost: https://d1e9q4gqdlef28.cloudfront.net/